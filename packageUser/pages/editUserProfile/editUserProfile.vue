<template>
	<view class="editUserProfile">
		<view class="editUserProfile-container">
			<button open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
				<view>
					头像
				</view>
				<view>
					<image :src="userInfo.headPortrait || '/static/missing-face.png'" mode=""
						style="width: 85rpx;height: 85rpx;border-radius: 50%;"></image>
				</view>
			</button>
			<view>
				<view>
					昵称
				</view>
				<view>
					<input type="nickname" v-model="userInfo.nickName" placeholder="请输入昵称"
						placeholder-style="text-align:right" style="text-align: right;" />
				</view>
			</view>
			<view @click="changeShowPicker">
				<view>
					性别
				</view>
				<view>
					{{userSex}}
					<image src="@/static/right-arrow-gray.png" mode="" style="width: 32rpx;height: 32rpx;"></image>
				</view>
			</view>
			<view>
				<view>
					所在城市
				</view>
				<view>
					<picker @change="bindPickerChange" :value="provinceSelectedIndex" :range="provinces">
						<input :disabled="true" type="text" v-model="userInfo.areaAddr" placeholder="请选择所在城市"
							placeholder-style="text-align:right" style="text-align: right;" />
					</picker>

				</view>
			</view>
		</view>

		<w-picker mode="selector" value="0" :visible.sync="showPicker" :options="sexDictsList"
			:defaultProps='{label:"text",value:"value"}' @confirm='pickerConfirm' @cancel='pickerCancel'>
		</w-picker>



		<view class="editUserProfile-btn">
			<view @click="save">
				保存
			</view>
		</view>
	</view>
</template>
<script setup>
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		getUserInfos,
		navTo,
		toUpPage,
		uploadImg
	} from "@/hooks";
	import {
		userStore
	} from "@/store";
	import {
		getSafeBottom
	} from "@/utils";
	import {
		computed,
		nextTick,
		ref
	} from "vue";
	let showPicker = ref(false)
	const users = userStore()
	const imgUrl = uni.env.IMAGE_URL
	const provinceSelectedIndex = ref(-1)
	const provinces = [
		"北京市",
		"天津市",
		"河北省",
		"山西省",
		"内蒙古自治区",
		"辽宁省",
		"吉林省",
		"黑龙江省",
		"上海市",
		"江苏省",
		"浙江省",
		"安徽省",
		"福建省",
		"江西省",
		"山东省",
		"河南省",
		"湖北省",
		"湖南省",
		"广东省",
		"广西壮族自治区",
		"海南省",
		"重庆市",
		"四川省",
		"贵州省",
		"云南省",
		"西藏自治区",
		"陕西省",
		"甘肃省",
		"青海省",
		"宁夏回族自治区",
		"新疆维吾尔自治区",
		"台湾省",
		"香港特别行政区",
		"澳门特别行政区"
	];

	let userInfo = ref({
		nickName: users.userInfo.nickName,
		headPortrait: users.userInfo.avatarUrl,
		sex: users.userInfo.sex || '0',
		areaAddr: users.userInfo.areaAddr
	})
	let sexDictsList = ref([])
	const userSex = computed(() => {
		return sexDictsList.value.find(i => i.value == userInfo.value.sex)?.text || '未知'
	})
	//保存
	async function save() {
		if (!userInfo.value.nickName) {
			uni.showToast({
				title: '请填写昵称',
				icon: "none"
			})
			return;
		}
		if (!userInfo.value.headPortrait) {
			uni.showToast({
				title: '请上传头像',
				icon: "none"
			})
			return;
		}
		uni.showLoading({
			mask: true
		})
		//旧头像
		const oldHeadPortrait = users.userInfo.avatarUrl
		if (oldHeadPortrait !== userInfo.value.headPortrait) {
			let data = await uploadImg(userInfo.value.headPortrait);
			userInfo.value.headPortrait = imgUrl + data.message;
			await nextTick()
		}
		await uni.http.post(uni.api.completeInformation, userInfo.value)
		getUserInfos();
		uni.showToast({
			icon: "none",
			title: '修改成功~'
		})
		setTimeout(() => {
			toUpPage()
		}, 500)
	}

	function bindPickerChange(e) {
		const idx = e.detail.value
		provinceSelectedIndex.value = idx
		userInfo.value.areaAddr = provinces[idx]
	}



	function changeShowPicker() {
		showPicker.value = true;
	}

	function pickerConfirm(e) {
		showPicker.value = false;
		userInfo.value.sex = e.obj.value
	}

	function pickerCancel() {
		showPicker.value = false;
	}
	//选择头像
	function onChooseAvatar(e) {
		userInfo.value.headPortrait = e.detail.avatarUrl
	}
	//获取性别字典
	async function getSexDicts() {
		//获取性别字典
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=sex`);
		sexDictsList.value = data.result || []
	}
	onLoad(() => {
		getUserInfos()
		getSexDicts()
	})
</script>
<style lang="scss">
	.editUserProfile {
		width: 750rpx;
		padding: 28rpx;

		button::after {
			border: none;
		}

		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}

		&-container {
			width: 100%;
			// padding: 0 28rpx;
			background: #FFFFFF;
			border-radius: 24rpx;

			>view,
			>button {
				width: 100%;
				height: 130rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				color: #333333;
				border-bottom: 2rpx solid #EEEEEE;
				padding: 0;
				background-color: white;


				>view:nth-child(2) {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					line-height: 0;
				}
			}

			>view:last-child {
				border-bottom: none;
			}
		}
	}
</style>
