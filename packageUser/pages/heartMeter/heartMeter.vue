<template>
	<view class="heartMeter">
		<view class="heartMeter-card primary" @click="navTo('/packageUser/pages/heartFeedback/heartFeedback')">
			<view class="heartMeter-card-header">
				<text class="heartMeter-card-title">当前助力值</text>
				<image class="heartMeter-card-arrow" src="@/static/right-arrow-white.png" mode="aspectFit"></image>
			</view>

			<text class="heartMeter-card-value">{{users.userInfo.balance || 0}}</text>

			<view class="heartMeter-card-actions">
				<view class="heartMeter-card-btn primary" hover-class="btn-hover" @click.stop="navTo('/packageRecharge/pages/recharge/recharge')">
					助力
				</view>
				<view class="heartMeter-card-btn outline" hover-class="btn-hover" @click.stop="navTo('/pages/withDraw/withDraw')">
					结算
				</view>
			</view>

			<image class="heartMeter-card-decoration" src="@/static/heartMeter/ic.png" mode="aspectFit"></image>
		</view>

		<view class="heartMeter-card secondary" hover-class="card-hover" @click="navTo('/pages/pendingHeartValueDetail/pendingHeartValueDetail')">
			<view class="heartMeter-card-header">
				<text class="heartMeter-card-title">待结算助力值</text>
				<image class="heartMeter-card-arrow" src="@/static/right-arrow-gray.png" mode="aspectFit"></image>
			</view>

			<text class="heartMeter-card-value">{{users.userInfo.accountFrozen || 0}}</text>

			<view class="heartMeter-card-info">
				<text class="heartMeter-card-info-text">结算时助力值会从待结算中扣除，进入可用助力值。</text>
			</view>
		</view>

		<!-- <view class="heartMeter-card secondary" hover-class="card-hover" @click="navTo('/pages/unavailableHeartValueDetail/unavailableHeartValueDetail')">
			<view class="heartMeter-card-header">
				<text class="heartMeter-card-title">不可用助力值</text>
				<image class="heartMeter-card-arrow" src="@/static/right-arrow-gray.png" mode="aspectFit"></image>
			</view>

			<text class="heartMeter-card-value">{{users.userInfo.unusableFrozen || 0}}</text>

			<view class="heartMeter-card-info">
				<text class="heartMeter-card-info-text">提现待审核、待打款的冻结助力值。</text>
			</view>
		</view> -->
	</view>
</template>

<script setup lang="ts">
	import {
		onShow,
		onLoad
	} from "@dcloudio/uni-app"
	import {
		userStore
	} from '@/store/index.js'
	import {
		getUserInfos,
		navTo
	} from '@/hooks/index.js'
	const users = userStore()
	onShow(() => {
		getUserInfos();
	})
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.heartMeter {
		padding: 30rpx;

		&-card {
			padding: 32rpx;
			border-radius: 20rpx;
			margin-bottom: 24rpx;
			position: relative;
			overflow: hidden;
			box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
			transition: transform 0.2s ease;

			&.primary {
				background: linear-gradient(135deg, #22a3ff, #1a86e0);
				color: white;
			}

			&.secondary {
				background: white;
				color: #333;
			}

			&-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 16rpx;
			}

			&-title {
				font-size: 30rpx;
				color: inherit;
			}

			&-arrow {
				width: 28rpx;
				height: 28rpx;
			}

			&-value {
				font-size: 56rpx;
				font-weight: 600;
				margin: 16rpx 0 32rpx;
				display: block;
				color: inherit;
			}

			&-actions {
				display: flex;
				gap: 20rpx;
			}

			&-btn {
				padding: 16rpx 32rpx;
				border-radius: 100rpx;
				font-size: 28rpx;
				min-width: 160rpx;
				text-align: center;
				display: flex;
				align-items: center;
				justify-content: center;

				&.primary {
					background: white;
					color: #22a3ff;
				}

				&.outline {
					border: 2rpx solid white;
					color: white;
				}
			}

			&-decoration {
				position: absolute;
				right: 32rpx;
				top: 50%;
				transform: translateY(-50%);
				width: 180rpx;
				height: 180rpx;
				opacity: 0.9;
			}

			&-info {
				background: #f5f7fa;
				border-radius: 12rpx;
				padding: 24rpx;
				margin-top: 24rpx;

				&-text {
					font-size: 26rpx;
					color: #666;
					line-height: 1.5;
				}
			}
		}
	}

	.card-hover {
		transform: scale(0.98);
		opacity: 0.9;
	}

	.btn-hover {
		opacity: 0.8;
	}
</style>
