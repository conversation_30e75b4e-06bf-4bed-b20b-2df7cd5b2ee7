<template>
	<view class="setting">
	<!-- 	<view class="setting-wrap" v-if="users.token">
			<view class="setting-line" @click="authPhone(1)">
				<view>
					交易密码
				</view>
				<view>
					<view style="color: #A2A2A2;">
						{{ users.userInfo.transactionPassword == 1 ? '已' : '未' }}设置
					</view>
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>

		</view> -->
		<view class="setting-wrap">
			<view class="setting-line" @click="navTo('/packageSearch/pages/agreement/agreement?path=userServiceAgreement')">
				<view>
					服务条款
				</view>
				<view>

					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>
			<view class="setting-line" @click="navTo('/packageSearch/pages/agreement/agreement?path=privacyAgreement')">
				<view>
					隐私协议
				</view>
				<view>
					<image src="@/static/right-arrow-black.png" mode=""></image>
				</view>
			</view>
		</view>
		<!-- <view class="setting-wrap">
			<view class="setting-line">
				<view>
					软件版本
				</view>
				<view>
					当前版本V{{version}}
				</view>
			</view>
		</view> -->
		<template v-if="users.token">

			<view class="setting-btn">
				<view @click="showLogoutPop">
					退出登录
				</view>
			</view>
		</template>

		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='温馨提示' content="您确定要退出登录吗?" :duration="2000"
				:before-close="false" @confirm="userLogout"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>
<script setup>
	import {
		userStore
	} from '@/store';
	import {
		getSafeBottom
	} from '@/utils';
	import {
		navToBeforeLogin,
		navTo,
		toUpPage,
		authPhone
	} from '@/hooks'
	import {
		ref
	} from "vue";
	const popup = ref()
	const users = userStore()

	//显示退出登录弹窗提示
	function showLogoutPop() {
		popup.value.open()
	}
	//退出登录
	function userLogout() {
		users.logOut()
		uni.showToast({
			title: '退出登录成功~',
			icon: 'none'
		})
		setTimeout(() => {
			toUpPage()
		}, 500)
	}
</script>
<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.setting {
		width: 750rpx;
		padding: 32rpx;

		&-placeholder {
			padding-bottom: v-bind(getSafeBottom());
		}

		&-btnOpe {
			padding-bottom: v-bind(getSafeBottom());
		}


		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}


		&-wrap {
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 0 32rpx;
			margin-bottom: 24rpx;
		}

		>view:last-child {
			margin-bottom: 0;
		}

		&-line {
			width: 100%;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			font-size: 30rpx;
			color: #000000;

			>view:nth-child(2) {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				image {
					width: 32rpx;
					height: 32rpx;
					margin-left: 8rpx;
				}
			}
		}
	}
</style>
