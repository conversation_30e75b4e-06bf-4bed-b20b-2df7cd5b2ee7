<template>
	<view class="heartFeedback">
		<!-- <view class="heartFeedback-tab">
			<view v-for="(item,index) in tabData" :key="index" @click="changeTabIndex(index)"
				:class="{selected:index === tabIndex}">
				{{item}}
			</view>

		</view> -->
		<uv-sticky bgColor="#f8f8f8">
			<view style="padding-bottom: 14rpx;">
				<uv-tabs :current='tabIndex' :scrollable='false' :list="tabData" lineWidth="45rpx" lineHeight='6rpx'
					lineColor="#1A69D1" :activeStyle="{
							color: '#1A69D1',
							fontSize:'28rpx',
					    	}" :inactiveStyle="{
							color: '#999999',
							fontSize:'28rpx'
						}" itemStyle="height:70rpx;" @click="changeTabIndex"></uv-tabs>
			</view>
		</uv-sticky>
		<view class="transaction-item"
			:class="item.goAndCome == '0' ? 'income-bg' : 'expense-bg'"
			hover-class="item-hover"
			@click="toDetail(item)"
			v-for="(item,index) in list"
			:key="index">
			<view class="transaction-content">
				<view class="transaction-left">
					<view class="transaction-type">{{item.payType}}</view>
					<view class="transaction-time">{{item.createTime}}</view>
				</view>

				<view class="transaction-right">
					<view class="transaction-amount" :class="item.goAndCome == '0' ? 'income' : 'expense'">
						{{item.amount}}
					</view>
					<view class="transaction-balance">{{item.balance}}</view>
				</view>
			</view>
		</view>
		<empty v-if="!list?.length"></empty>
	</view>
</template>

<script setup>
	import {
		computed,
		ref
	} from "vue";
	import {
		listGet,
		navTo,
	} from "@/hooks";
	const tabData = [{
		name: '全部'
	}, {
		name: '收入'
	}, {
		name: '支出'
	}];
	let tabIndex = ref(0)
	//改变状态索引
	function changeTabIndex(e) {
		const index = e.index
		tabIndex.value = index
	}
	const patternList = [2, 0, 1] //全部 收入 支出
	//请求参数
	let reqOptions = computed(() => {
		return {
			pattern: patternList[tabIndex.value]
		}
	})
	const {
		// eslint-disable-next-line no-unused-vars
		mode,
		list
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.findAccountCapitalByMemberId,
		pdIsLogin: true
	})
	const toDetail = (item) => {
		navTo(`/pages/heartFeedbackDetail/heartFeedbackDetail?info=${JSON.stringify(item)}`)
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.heartFeedback {
		padding: 30rpx;
		padding-top: 0;

		.selected {
			color: #333333 !important;
			border-bottom-color: #22A3FF !important;
		}

		&-tab {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 65rpx;
			color: #999999;
			font-size: 28rpx;
			margin-bottom: 30rpx;

			>view {
				border-bottom-color: transparent;
				border-bottom-width: 2rpx;
				border-bottom-style: solid;
				padding-bottom: 6rpx;
			}
		}
	}

	.transaction-item {
		margin-bottom: 16rpx;
		border-radius: 16rpx;
		background-color: #ffffff;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
		transition: transform 0.2s ease;

		&.income-bg {
			background-color: rgba(255, 51, 51, 0.03);
		}

		&.expense-bg {
			background-color: rgba(0, 175, 66, 0.03);
		}
	}

	.transaction-content {
		padding: 24rpx 30rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.transaction-left {
		flex: 1;
	}

	.transaction-type {
		font-size: 32rpx;
		color: #333333;
		font-weight: 500;
		margin-bottom: 8rpx;
	}

	.transaction-time {
		font-size: 24rpx;
		color: #999999;
	}

	.transaction-right {
		text-align: right;
	}

	.transaction-amount {
		font-size: 36rpx;
		font-weight: 600;
		margin-bottom: 8rpx;

		&.income {
			color: #FF3333;
		}

		&.expense {
			color: #00AF42;
		}
	}

	.transaction-balance {
		font-size: 24rpx;
		color: #999999;
	}

	.item-hover {
		transform: scale(0.98);
		opacity: 0.9;
	}
</style>
