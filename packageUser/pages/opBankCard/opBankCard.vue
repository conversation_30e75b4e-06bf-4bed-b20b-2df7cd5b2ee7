<template>
	<view class="opBankCard">
		<view class="opBankCard-container">
			<view class="opBankCard-line">
				<view>
					手机号
				</view>
				<view>
					{{users.userInfo.phone}}
				</view>
			</view>
			<view class="opBankCard-line">
				<view>
					银行卡号
				</view>
				<view>
					<input type="number" v-model="info.bankCard" placeholder="请输入银行卡号"
						placeholder-class="opBankCard-line-placeClass" />
				</view>
			</view>
			<view class="opBankCard-line">
				<view>
					户名
				</view>
				<view>
					<input type="text" v-model="info.cardholder" placeholder="请输入银行卡对应的户名"
						placeholder-class="opBankCard-line-placeClass" />
				</view>
			</view>
			<view class="opBankCard-line">
				<view>
					证件号
				</view>
				<view>
					<input type="idcard" v-model="info.identityNumber" placeholder="请输入身份证号码"
						placeholder-class="opBankCard-line-placeClass" />
				</view>
			</view>
			<view class="opBankCard-line">
				<view>
					开户行
				</view>
				<view @click="changeShowPicker">
					<template v-if="!info.bankName">
						<text class="opBankCard-line-placeClass"> 请选择收款银行</text>
					</template>
					<template v-else>
						<text>{{info.bankName}}</text>
					</template>
					<image style="width: 32rpx;height: 32rpx;margin-left: 8rpx;" src="@/static/right-arrow-gray.png"
						mode=""></image>
				</view>
			</view>
			<view class="opBankCard-line">
				<view>
					分支行
				</view>
				<view>
					<input v-model="info.openingBank" type="text" placeholder="请输入开户银行支行名称"
						placeholder-class="opBankCard-line-placeClass" />
				</view>
			</view>
			<view class="opBankCard-line">
				<view>
					是否默认
				</view>
				<view>
					<switch :checked='info.isDefault === "1"' @change="switchChange" />
				</view>
			</view>
		</view>
		<!-- <view class="tips">
			温馨提示：户名和证件号设置后无法更改，请如实填写。
		</view> -->

		<view class="opBankCard-btn">
			<view @click="showAtten">
				提交银行卡
			</view>
		</view>
		<w-picker v-if="bankCardList?.length" mode="selector" value="0" :visible.sync="showPicker"
			:options="bankCardList" :defaultProps='{label:"bankName",value:"bankCode"}' @confirm='pickerConfirm'
			@cancel='pickerCancel'>
		</w-picker>
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='温馨提示' content="确认信息无误,请点击确定" :duration="2000"
				:before-close="false" @confirm="submit"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>
<script setup>
	import {
		userStore
	} from '@/store';
	import {
		clone,
		getSafeBottom,
		trim
	} from '@/utils';
	import {
		onShow,
		onLoad
	} from "@dcloudio/uni-app"
	import {
		ref
	} from "vue";
	import {
		navTo
	} from '@/hooks';
	let options = ref({})
	onLoad((o) => {
		options.value = o
		refresh()
	})
	const users = userStore()
	//显示银行卡选择
	let showPicker = ref(false);
	let info = ref({
		bankCard: '', //银行卡号
		cardholder: '', //户名
		identityNumber: '', //证件号
		bankName: '', //开户行名称
		openingBank: '', //分支行名称
		bankCode: "", //银行卡代码
		carType: '0',
		phone: users.userInfo.phone,
		accountType: 0,
		isDefault: '0'
	});
	let bankCardList = ref([]);
	let popup = ref()
	async function refresh() {
		if (options.value?.id) {
			let {
				data
			} = await uni.http.post(uni.api.returnMemberBankCardInfo, options.value);
			if (data.result?.id) {
				for (let key in info.value) {
					if (key === 'phone' && !data.result[key]) {
						break
					} else {
						info.value[key] = data.result[key] || ''
					}
				}
				info.value.id = data.result.id
			}
		}


		let {
			data: bankCardData
		} = await uni.http.post(uni.api.getSysBankList);
		bankCardList.value = bankCardData.result;
	}

	const switchChange = (e) => {
		info.value.isDefault = e.detail.value ? '1' : '0'
	}
	//银行选择确认
	function pickerConfirm(e) {
		console.log(e.obj)
		showPicker.value = false;
		info.value.bankName = e.obj.bankName
		info.value.bankCode = e.obj.bankCode
	}
	//银行选择取消
	function pickerCancel() {
		showPicker.value = false;
	}
	//显示选择银行弹窗
	function changeShowPicker() {
		showPicker.value = true;
	}
	//显示温馨提示弹窗
	function showAtten() {
		popup.value.open()
	}

	//提交信息
	async function submit() {
		uni.showLoading({
			mask: true
		})
		let result = clone(info.value);
		result.bankCard = trim(result.bankCard)
		console.log(trim(result.bankCard))
		let {
			data
		} = await uni.http.post(uni.api.addMemberBankCard, result)
		uni.showToast({
			title: data.message || '绑定成功~',
			icon: 'none'
		})
		setTimeout(() => {
			uni.navigateBack()
		}, 500)
		console.log(data)
	}
</script>
<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.opBankCard {
		width: 750rpx;
		padding: 32rpx;



		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}

		.tips {
			font-size: 24rpx;
			color: #666666;
			text-align: center;
			margin-top: 32rpx;
		}

		&-container {
			width: 100%;
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 0 32rpx;
		}

		&-line {
			width: 100%;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 2rpx solid #EEEEEE;
			font-size: 30rpx;
			color: #000000;

			>view:nth-child(2) {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				flex: 1;

				input {
					width: 100%;
					text-align: right;
				}
			}

			&-placeClass {
				font-size: 28rpx;
				color: #A2A2A2;
			}
		}

		&-line:last-child {
			border-bottom: none;
		}
	}
</style>
