<template>
	<view class="myBankCard">
		<view class="myBankCard-line" v-for="(item,index) in list" :key="index"
			@click="navTo(`/packageUser/pages/opBankCard/opBankCard?id=${item.id}`)">
			<!-- <image class="myBankCard-line-icon" src="@/static/index/btn1.png"></image> -->
			<view class="myBankCard-line-right">
				<view>
					{{item.bankName}}
				</view>
				<view>
					{{item.bankCard}}
				</view>
			</view>
			<view class="myBankCard-line-default" v-if="item.isDefault === '1'">
				默认
			</view>
			<view class="myBankCard-line-operation">
				<view class="myBankCard-line-operation-fir" @click.stop="deleteCard(item)">
					删除
				</view>
				<view class="myBankCard-line-operation-sec" v-if="item.isDefault !== '1'"
					@click.stop="setDefault(item)">
					设置默认
				</view>
			</view>
		</view>
		<empty v-if="!list?.length"></empty>
		<view class="myBankCard-btn">
			<view @click="navTo('/packageUser/pages/opBankCard/opBankCard')">
				添加银行卡
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		navTo,
		listGet
	} from '@/hooks';
	const {
		mode,
		list,
		refresh
	} = listGet({
		apiUrl: uni.api.returnMemberBankCard,
		reqType: 1
	})

	function deleteCard(item) {
		uni.showModal({
			title: '删除银行卡',
			content: '确认删除当前银行卡吗',
			async success(res) {
				if (res.confirm) {
					await uni.http.post(uni.api.deleteMemberBankCard, {
						ids: item.id
					})
					uni.showToast({
						title: '删除成功~',
						icon: 'none'
					})
					setTimeout(() => {
						refresh()
					}, 500)
				}
			}
		})
	}

	async function setDefault(item) {
		let {
			data
		} = await uni.http.post(uni.api.addMemberBankCard, {
			...item,
			isDefault: '1'
		})
		setTimeout(() => {
			refresh()
		}, 500)
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.myBankCard {
		padding: 30rpx;

		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}


		&-line {
			background: #ffffff;
			border-radius: 16rpx;
			overflow: hidden;
			display: flex;
			padding: 30rpx;
			margin-bottom: 20rpx;
			position: relative;

			&-default {
				position: absolute;
				z-index: 1;
				width: 115rpx;
				height: 40rpx;
				background-color: #278aff;
				right: 0;
				top: 0;
				border-radius: 0 16rpx 0 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 26rpx;
			}

			&-operation {
				position: absolute;
				right: 30rpx;
				bottom: 30rpx;
				z-index: 1;
				display: flex;
				align-items: center;
				justify-content: flex-end;

				>view {
					height: 56rpx;
					line-height: 56rpx;
					padding: 0 16rpx;
					font-size: 28rpx;
					border-radius: 8rpx;

				}

				&-fir {
					border: 2rpx solid #999999;
					color: #999999;
					margin-right: 20rpx;
				}

				&-sec {
					background-color: #22A3FF;
					color: white;
				}
			}

			&-icon {
				width: 30rpx;
				height: 30rpx;
				margin-right: 14rpx;
			}

			&-right {
				>view:nth-child(1) {
					color: #333333;
					font-size: 30rpx;
					line-height: 30rpx;
					margin-bottom: 30rpx;
				}

				>view:nth-child(2) {
					font-size: 28rpx;
					color: #666666;
				}
			}
		}
	}
</style>
