<template>
	<view class="userProfile">
		<view class="userProfile-container">
			<view>
				<view>
					头像
				</view>
				<view>
					<image :src="users.userInfo.avatarUrl || '/static/missing-face.png'" mode=""
						style="width: 85rpx;height: 85rpx;border-radius: 50%;"></image>
				</view>
			</view>
			<view>
				<view>
					用户名
				</view>
				<view>
					{{users.userInfo.nickName}}
				</view>
			</view>
			<view>
				<view>
					性别
				</view>
				<view>
					{{userSex }}
				</view>
			</view>
			<view>
				<view>
					所在城市
				</view>
				<view>
					{{users.userInfo.areaAddr }}
				</view>
			</view>
			<view>
				<view>
					关联手机
				</view>
				<view>
					{{users.userInfo.phone }}
				</view>
			</view>
		</view>


		<view class="userProfile-btn">
			<view @click="navTo(`/packageUser/pages/editUserProfile/editUserProfile`)">
				修改资料
			</view>
		</view>

	</view>
</template>
<script setup>
	import {
		onShow,
		onLoad
	} from "@dcloudio/uni-app"
	import {
		getUserInfos,
		navTo
	} from "@/hooks";
	import {
		userStore
	} from "@/store";
	import {
		getSafeBottom
	} from "@/utils";
	import {
		computed,
		ref
	} from "vue";
	const users = userStore()
	let sexDictsList = ref([])
	const userSex = computed(() => {
		return sexDictsList.value.find(i => i.value == users.userInfo.sex)?.text || '未知'
	})
	onShow(() => {
		getUserInfos()
	})
	onLoad(async () => {
		//获取性别字典
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=sex`);
		sexDictsList.value = data.result || []
	})
</script>
<style lang="scss">
	page {
		background-color: white!important;
	}

	.userProfile {
		width: 750rpx;
		padding: 28rpx;

		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}



		&-container {
			width: 100%;
			// padding: 0 28rpx;
			background: #FFFFFF;
			border-radius: 24rpx;

			>view {
				width: 100%;
				height: 130rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				color: #333333;
				border-bottom: 2rpx solid #EEEEEE;

				>view:nth-child(2) {
					display: flex;
					align-items: center;
					justify-content: flex-end;
				}
			}

			>view:last-child {
				border-bottom: none;
			}
		}
	}
</style>
