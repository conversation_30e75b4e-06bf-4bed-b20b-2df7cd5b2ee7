<template>
	<view class="myPoster">
		<template>
			<view class="myPoster-desc">
				邀请好友得奖励，多邀多得~<br />
				保存并分享您的好友扫码即可加入。
			</view>
			<template v-if="posterBg">
				<view class="myPoster-container">
					<view class="myPoster-container-qrCode">
						<image class="myPoster-container-qrCode-img" :src="imgUrl + users.userInfo.address" mode="">
						</image>
						<view class="myPoster-container-qrCode-users">
							<image :src="users.userInfo.avatarUrl" mode=""></image>
							<view class="truncate line-clamp-1">
								{{users.userInfo.nickName}}
							</view>
						</view>
					</view>
				</view>

				<view class="myPoster-btn">
					<button :loading="!path" @click="savePic">
						保存我的海报
					</button>
				</view>

				<l-painter path-type='url' file-type='jpg' isCanvasToTempFilePath hidden @success="painterSuccess"
					v-if="posterBg" :css="posterStyle.painterWrap">
					<l-painter-view :css="posterStyle.container">
						<l-painter-image :src="imgUrl + info.distributionPosters" :css="posterStyle.posterBg">
						</l-painter-image>
						<l-painter-view :css="posterStyle.qrCode">
							<l-painter-image :src="imgUrl + users.userInfo.address" :css="posterStyle.qrCodeImg">
							</l-painter-image>
							<l-painter-view :css="posterStyle.qrCodeUsers">
								<l-painter-image :src="users.userInfo.avatarUrl"
									:css="posterStyle.qrCodeUsersAvatar"></l-painter-image>
								<l-painter-text :text="users.userInfo.nickName" :css="posterStyle.qrCodeUsersNickname">
								</l-painter-text>
							</l-painter-view>
						</l-painter-view>
					</l-painter-view>
				</l-painter>
			</template>
		</template>
		<!-- <loginBtn></loginBtn> -->
	</view>
</template>

<script setup>
	import {
		computed,
		ref
	} from "vue";
	import {
		userStore
	} from "@/store";
	import {
		navTo
	} from "@/hooks";
	let posterBg = ref('')
	let info = ref({})
	const imgUrl = uni.env.IMAGE_URL
	const users = userStore()
	let path = ref()
	//海报canvas样式相关
	const posterStyle = computed(() => {
		return {
			painterWrap: 'width:660rpx',
			container: `width: 660rpx; height: 1002rpx;`,
			posterBg: `width: 660rpx; height: 1002rpx;`,
			qrCode: `position: absolute;left: 50%;bottom: 24rpx;width:135rpx;height:185rpx;transform: translateX(-50%);`,
			qrCodeImg: `width: 135rpx;height: 135rpx;border-radius: 8rpx;margin-bottom: 15rpx;`,
			qrCodeUsers: `width: 135rpx;height: 35rpx;background-color: rgba(0, 0, 0, 0.55);border-radius: 15rpx;display: flex;align-items: center;padding: 0 10rpx;font-size: 24rpx;color: #FFFFFF;box-sizing:border-box`,
			qrCodeUsersAvatar: `width: 25rpx;height: 25rpx;margin-right: 4rpx;border-radius: 50%;`,
			qrCodeUsersNickname: `flex:1;line-clamp:1;`,
		}
	})
	//保存图片
	function savePic() {
		uni.saveImageToPhotosAlbum({
			filePath: path.value,
			success: function() {
				uni.showToast({
					title: '保存成功~',
					icon: "none"
				})
			}
		});
	}
	//画报生成成功回调
	function painterSuccess(e) {
		path.value = e
	}
	refresh()
	async function refresh() {
		if (users.token) {
			let {
				data
			} = await uni.http.get(uni.api.findMemberPromoter)
			posterBg.value = `url(${imgUrl + data.result.distributionPosters})`
			info.value = data.result
		}

	}
</script>

<style lang="scss">
	.myPoster {
		width: 750rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 40rpx;

		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view ,>button{
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}

		&-gl {
			width: 112rpx;
			height: 65rpx;
			background-image: linear-gradient(180deg, #FF4F4F 0%, #FF654C 100%);
			border-radius: 100rpx 0 0 100rpx;
			position: fixed;
			top: 16rpx;
			z-index: 2;
			right: 0;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
			line-height: 0;
		}

		&-button {
			width: 512rpx;
			height: 100rpx;
			background-image: linear-gradient(-69deg, #FF0000 0%, #FF7B7B 97%);
			border-radius: 54rpx;
			font-weight: 500;
			font-size: 32rpx;
			color: #FFFFFF;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		&-desc {
			font-size: 28rpx;
			line-height: 40rpx;
			color: #222222;
			text-align: center;
			margin-bottom: 40rpx;
		}

		&-container {
			width: 660rpx;
			height: 1002rpx;
			border-radius: 28rpx;
			background-image: v-bind(posterBg);
			background-size: 100% 100%;
			background-repeat: no-repeat;
			position: relative;
			margin-bottom: 40rpx;

			&-qrCode {
				position: absolute;
				left: 50%;
				bottom: 24rpx;
				transform: translateX(-50%);

				&-img {
					width: 135rpx;
					height: 135rpx;
					border-radius: 8rpx;
					margin-bottom: 15rpx;
				}

				&-users {
					width: 135rpx;
					height: 35rpx;
					background-color: rgba(0, 0, 0, 0.55);
					border-radius: 15rpx;
					display: flex;
					align-items: center;
					padding: 0 10rpx;
					font-size: 24rpx;
					color: #FFFFFF;

					>image:nth-child(1) {
						width: 25rpx;
						height: 25rpx;
						margin-right: 4rpx;
						border-radius: 50%;
					}

					>view:nth-child(2) {
						flex: 1;
					}
				}
			}


		}
	}
</style>
