<template>
	<view class="myTeam">
		<view class="myTeam-top">
			<view v-for="(item,index) in tabList" :key="index" :class="{sel:selectTab === index}"
				@click="changeSelectTab(index)">
				<template v-if="index != 2">
					{{item.text}}
				</template>
				<template v-else>
					{{selectTimeRange.startDate ? `${selectTimeRange.startDate} ${selectTimeRange.endDate}` : item.text}}
				</template>
			</view>
		</view>
		<view class="myTeam-container">
			<view class="myTeam-container-commonWrap" @click="navTo('/pages/revenueDetail/revenueDetail')">
				<view class="myTeam-container-commonWrap-top">
					<view class="myTeam-container-commonWrap-top-title">
						捐助明细
					</view>
					<view class="myTeam-container-commonWrap-top-right">
						<text style="color: #333333;">{{distributionCommissionData.totalOrderCount}}/</text>
						包裹(笔）
						<image class="myTeam-container-commonWrap-top-right-img" src="@/static/right-arrow-gray.png"
							mode=""></image>
					</view>
				</view>
				<view class="myTeam-container-commonWrap-bottom">
					<view class="myTeam-container-commonWrap-bottom-wrap">
						<view class="myTeam-container-commonWrap-bottom-wrap-first">
							品牌馆
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-second">
							{{distributionCommissionData.brandCommission}}
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-third">
							助力值
						</view>
					</view>
					<view class="myTeam-container-commonWrap-bottom-wrap">
						<view class="myTeam-container-commonWrap-bottom-wrap-first">
							生活馆
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-second">
							{{distributionCommissionData.lifeCommission}}
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-third">
							助力值
						</view>
					</view>
					<view class="myTeam-container-commonWrap-bottom-wrap">
						<view class="myTeam-container-commonWrap-bottom-wrap-first">
							创业馆
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-second">
							{{distributionCommissionData.entrepreneurCommission}}
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-third">
							助力值
						</view>
					</view>
				</view>

			</view>
			<view class="myTeam-container-commonWrap">
				<view class="myTeam-container-commonWrap-top">
					<view class="myTeam-container-commonWrap-top-title">
						总创业积分
					</view>
					<view class="myTeam-container-commonWrap-top-right">
						截止
						<text style="color: #333333;">{{getCurrentDate()}}</text>
					</view>
				</view>
				<view class="myTeam-container-commonWrap-bottom">
					<view class="myTeam-container-commonWrap-bottom-wrap">
						<view class="myTeam-container-commonWrap-bottom-wrap-second">
							{{businessPerformanceData.totalPerformance}}
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-third">
							总积分
						</view>
					</view>
					<view class="myTeam-container-commonWrap-bottom-wrap">
						<view class="myTeam-container-commonWrap-bottom-wrap-second">
							{{businessPerformanceData.directPerformance}}
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-third">
							直接积分
						</view>
					</view>
					<view class="myTeam-container-commonWrap-bottom-wrap">
						<view class="myTeam-container-commonWrap-bottom-wrap-second">
							{{businessPerformanceData.indirectPerformance}}
						</view>
						<view class="myTeam-container-commonWrap-bottom-wrap-third">
							间接积分
						</view>
					</view>

				</view>

			</view>

			<view class="myTeam-container-commonWrap">
				<view class="myTeam-container-commonWrap-top">
					<view class="myTeam-container-commonWrap-top-title">
						团队明细
					</view>
				</view>
				<view class="myTeam-container-commonWrap-subTitle">
					<view>
						昵称
					</view>
					<view>
						直接业绩
					</view>
					<view>
						分享佣金
					</view>
				</view>
				<template v-if="list?.length">
					<view class="myTeam-container-commonWrap-subTitle team-member-row"
						style="border-bottom: none;color: #666666;height: 80rpx;" v-for="(item,index) in list"
						:key="index">
						<view>
							{{item.nickName || '用户' + index}}
						</view>
						<view>
							{{item.directPerformance}}
						</view>
						<view>
							{{item.totalCommission}}
						</view>
					</view>
				</template>
				<empty v-else></empty>
			</view>
		</view>


		<mxDatepicker :show="showPicker" format="yyyy-mm-dd" type="rangetime" @confirm="datePickerConfirm"
			@cancel="showPicker = false" />


	</view>
</template>


<script setup>
	import {
		computed,
		ref,
		watch
	} from 'vue';
	import {
		listGet,
		navTo
	} from '@/hooks';
	const showPicker = ref(false)
	const distributionCommissionData = ref({})
	const businessPerformanceData = ref({})
	const tabList = [{
		text: '近7天',
		value: '7'
	}, {
		text: '近30天',
		value: '30'
	}, {
		text: '自定义',
		value: ''
	}]
	const selectTab = ref(0)

	const changeSelectTab = (i) => {
		if (i == 2) {
			showPicker.value = true
		} else {
			selectTab.value = i
			selectTimeRange.value = {
				dateRange: tabList[selectTab.value].value,
			}
		}
	}

	// 新增：获取当前日期并格式化为YYYY.MM.DD
	const getCurrentDate = () => {
		const now = new Date();
		const year = now.getFullYear();
		const month = String(now.getMonth() + 1).padStart(2, '0');
		const day = String(now.getDate()).padStart(2, '0');
		return `${year}.${month}.${day}`;
	}
	const selectTimeRange = ref({
		dateRange: tabList[selectTab.value].value,
	})
	const paramsOptions = computed(() => ({
		...selectTimeRange.value
	}))
	const refresh = async () => {
		const {
			data: {
				result: distributionCommissionDataResult
			}
		} = await uni.http.get(uni.api.distributionCommission, {
			params: paramsOptions.value
		})
		distributionCommissionData.value = distributionCommissionDataResult
		const {
			data: {
				result: businessPerformanceDataResult
			}
		} = await uni.http.get(uni.api.businessPerformance, {
			params: paramsOptions.value
		})
		businessPerformanceData.value = businessPerformanceDataResult
	}

	const {
		// mode 未使用，但保留解构以保持与 listGet 返回结构一致
		// eslint-disable-next-line no-unused-vars
		mode,
		list,
		refresh: listRefresh
	} = listGet({
		apiUrl: uni.api.teamDetail,
		options: paramsOptions,
		pdIsLogin: true,
		isReqTypeReq: false
	})
	const datePickerConfirm = (e) => {
		showPicker.value = false
		selectTimeRange.value = {
			startDate: e.value[0],
			endDate: e.value[1],
			dateRange: ""
		}
		selectTab.value = 2
	}


	watch(paramsOptions, () => {
		refresh()
		listRefresh()
	}, {
		immediate: true,
		deep: true
	})
</script>

<style lang="scss">
	page {
		background-color: #F2F2F2;
	}

	.team-member-row {
		transition: background-color 0.3s ease;
	}

	.team-member-row:nth-child(odd) {
		background-color: rgba(242, 242, 242, 0.3);
	}

	.myTeam {
		.sel {
			background-color: #22a3ff !important;
			color: white !important;
			box-shadow: 0 4rpx 8rpx rgba(34, 163, 255, 0.2);
		}

		&-container {
			padding: 30rpx;

			&-commonWrap {
				padding: 30rpx;
				background-color: white;
				border-radius: 16rpx;
				margin-bottom: 30rpx;
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

				&-subTitle {
					display: flex;
					align-items: center;
					justify-content: space-between;
					height: 70rpx;
					line-height: 70rpx;
					text-align: center;
					color: #333333;
					border-bottom: 1rpx solid #f5f5f5;
					font-size: 26rpx;
					font-weight: 500;


					>view {
						flex: 1;
						text-align: center;
					}


					// >view:nth-child(1) {
					// 	width: 200rpx;
					// }

					>view:nth-child(1) {
						flex: 2;
					}

					// >view:nth-child(3) {
					// 	flex: 1.5;
					// }
				}

				&-bottom {
					display: flex;
					align-items: center;

					&-wrap {
						flex: 1;
						height: 120rpx;
						display: flex;
						flex-direction: column;
						align-items: center;
						justify-content: center;
						border-right: 1rpx solid #e5e5e5;
						padding: 10rpx 0;

						&-first {
							font-size: 26rpx;
							color: #666666;
							line-height: 30rpx;
						}

						&-second {
							font-size: 36rpx;
							color: #488FF3;
							line-height: 42rpx;
							margin: 14rpx 0;
							font-weight: 500;
						}

						&-third {
							font-size: 24rpx;
							color: #999999;
							line-height: 28rpx;
						}
					}

					&-wrap:last-child {
						border-right: none;
					}
				}

				&-top {
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 36rpx;

					&-title {
						font-size: 32rpx;
						color: #333333;
						font-weight: 600;
						position: relative;
						padding-left: 20rpx;

						&::before {
							content: '';
							position: absolute;
							left: 0;
							top: 50%;
							transform: translateY(-50%);
							width: 8rpx;
							height: 32rpx;
							background-color: #22a3ff;
							border-radius: 4rpx;
						}
					}

					&-right {
						display: flex;
						align-items: center;
						justify-content: flex-end;
						font-size: 26rpx;
						color: #999999;
						background-color: #f9f9f9;
						padding: 6rpx 16rpx;
						border-radius: 20rpx;

						&-img {
							width: 20rpx;
							height: 20rpx;
							margin-left: 6rpx;
						}
					}
				}

			}
		}

		&-top {
			width: 750rpx;
			height: 100rpx;
			background-color: white;
			position: sticky;
			top: 0;
			left: 0;
			z-index: 5;
			display: flex;
			align-items: center;
			padding-left: 40rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			>view {
				height: 60rpx;
				line-height: 60rpx;
				background-color: #f6f6f8;
				border-radius: 30rpx;
				padding: 0 24rpx;
				font-size: 26rpx;
				color: #333333;
				margin-right: 20rpx;
				transition: all 0.3s ease;
			}
		}
	}
</style>
