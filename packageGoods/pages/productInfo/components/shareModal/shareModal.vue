<template>
	<uni-popup ref="popup" type="bottom">
		<view class="shareModal">
			<image class="shareModal-img" :src="imageUrl" mode="aspectFit"></image>
			<view class="shareModal-save" @click="savePoster" style="display: none;">
				保存海报
			</view>
			<view class="shareModal-ipt">
				<view class="shareModal-ipt-left">
					<view class="discount-label">分享优惠</view>
					<view class="max-discount-text">最高 <text class="highlight-rate">{{props.goodInfo.goodinfo.shareCommissionRate || 0}}%</text> 优惠</view>
				</view>
				<view class="shareModal-ipt-input-wrapper">
					<input class="shareModal-ipt-input" type="digit" :value="modelValue"
						@input="$emit('update:modelValue', $event.target.value)" placeholder="输入比例" />
					<text class="percent-symbol">%</text>
				</view>
			</view>
			<view class="shareModal-txt">
				<view class="shareModal-txt-top">
					<view class="txt-title">
						分享文案
					</view>
					<view class="copy-btn" @click="copy">
						复制文案
					</view>
				</view>
				<view class="shareModal-txt-cnt">
					每一次分享都是一次助力，每一份助力都有回报。好物相伴，创业路上我们互相扶持！
				</view>
			</view>
			<button class="shareModal-btn" open-type="share" v-if="modelValue !== undefined && modelValue <= (props.goodInfo.goodinfo.shareCommissionRate)" @tap.stop="checkShareRatio">
				分享最少可获 <text class="highlight-value">{{props.goodInfo.goodinfo.minShareCommission || 0}}</text> 助力值
			</button>
			<view v-else class="shareModal-btn disabled" @click="showShareErr">
				分享最少可获 <text class="highlight-value">{{props.goodInfo.goodinfo.minShareCommission || 0}}</text> 助力值
			</view>
		</view>

	</uni-popup>


</template>

<script lang="ts" setup>
	import { computed, ref } from 'vue';
	const props = defineProps({
		goodInfo: {
			type: Object,
			default: () => ({})
		},
		modelValue: {
			type: Number
		}
	})

	const emits = defineEmits(['update:modelValue', 'savePoster'])
	const popup = ref()
	const imgUrl = uni.env.IMAGE_URL;
	const open = () => {
		popup.value.open('bottom')
	}
	const close = () => {
		popup.value.close()
	}
	const imageUrl = computed(() => imgUrl + (props.goodInfo?.goodinfo?.mainPicture?.[0] || []))
	const shareText = '每一次分享都是一次助力，每一份助力都有回报。好物相伴，创业路上我们互相扶持！'
	const copy = () => {
		uni.setClipboardData({
			data: shareText
		})
	}
	const showShareErr = () => {
		uni.showToast({
			title: `分享优惠比例不得高于${props.goodInfo.goodinfo.shareCommissionRate}%`,
			icon: "none"
		})
	}
	const savePoster = () => {
		if (!(props.modelValue !== undefined && props.modelValue <= (props.goodInfo.goodinfo.shareCommissionRate))) {
			showShareErr()
			return;
		}
		emits('savePoster')
	}

	// 检查分享比例是否有效
	const checkShareRatio = (e: Event) => {
		if (!(props.modelValue !== undefined && props.modelValue <= (props.goodInfo.goodinfo.shareCommissionRate))) {
			e.stopPropagation()
			e.preventDefault()
			showShareErr()
			return false
		}
		return true
	}
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss">
	.shareModal {
		width: 750rpx;
		background-color: white;
		border-radius: 50rpx 50rpx 0 0;
		padding: 30rpx;
		display: flex;
		flex-direction: column;
		align-items: center;

		&-img {
			width: 400rpx;
			height: 400rpx;
			margin-bottom: 30rpx;
			border-radius: 12rpx;
			object-fit: contain;
			background-color: #f9f9f9;
		}

		&-save {
			text-align: center;
			color: #000000;
			font-size: 32rpx;
			margin-bottom: 20rpx;
		}

		&-btn {
			width: 536rpx;
			height: 88rpx;
			line-height: 88rpx;
			text-align: center;
			font-size: 32rpx;
			color: #FFFFFF;
			background: linear-gradient(99deg, #f46700 0%, #f42c00 100%);
			border-radius: 100rpx;
			box-shadow: 0 4rpx 8rpx rgba(244, 103, 0, 0.2);
			margin-top: 10rpx;

			&.disabled {
				background: linear-gradient(99deg, #999999 0%, #666666 100%);
				box-shadow: none;
			}

			.highlight-value {
				font-size: 36rpx;
				font-weight: bold;
				margin: 0 4rpx;
				color: #FFFF00;
			}
		}

		&-txt {
			width: 100%;
			background-color: #f9f9f9;
			border-radius: 12rpx;
			padding: 20rpx;
			margin-bottom: 30rpx;

			&-top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20rpx;

				.txt-title {
					font-size: 30rpx;
					color: #000000;
					font-weight: 500;
				}

				.copy-btn {
					width: 157rpx;
					height: 50rpx;
					line-height: 50rpx;
					text-align: center;
					border: 2rpx solid #f46700;
					border-radius: 100rpx;
					font-size: 26rpx;
					color: #f46700;
				}
			}

			&-cnt {
				color: #666666;
				font-size: 26rpx;
				line-height: 38rpx;
				margin-bottom: 10rpx;
				padding: 0 10rpx;
			}
		}

		&-ipt {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 88rpx;
			border: 2rpx solid #e2e2e2;
			border-radius: 12rpx;
			padding: 0 20rpx;
			margin-bottom: 30rpx;
			background-color: #ffffff;
			box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.05);
			min-width: 0;
			width: 100%;
			box-sizing: border-box;
			overflow: hidden;

			&-left {
				display: flex;
				align-items: center;
				flex-wrap: nowrap;
				white-space: nowrap;
				overflow: hidden;
				flex-shrink: 0;
				max-width: 65%;

				.discount-label {
					font-size: 28rpx;
					color: #333333;
					margin-right: 12rpx;
					font-weight: 500;
					white-space: nowrap;
					flex-shrink: 0;
				}

				.max-discount-text {
					font-size: 24rpx;
					color: #999999;
					font-weight: 500;
					white-space: nowrap;
					overflow: hidden;
					text-overflow: ellipsis;
				}

				.highlight-rate {
					color: #f46700;
					font-weight: bold;
					font-size: 28rpx;
				}
			}

			&-input-wrapper {
				display: flex;
				align-items: center;
				justify-content: flex-end;
				background-color: #f9f9f9;
				border-radius: 8rpx;
				padding: 6rpx 12rpx;
				flex-shrink: 0;
				min-width: 120rpx;
				margin-left: 10rpx;
			}

			&-input {
				font-size: 28rpx;
				text-align: right;
				width: auto;
				color: #f46700;
			}

			.percent-symbol {
				font-size: 28rpx;
				color: #f46700;
				margin-left: 4rpx;
				font-weight: bold;
			}
		}
	}
</style>