<template>
	<view class="productCollection">
		<view class="productCollection-top">
			<view @click="changeIsEdit">
				{{!isEdit ? '管理' : '完成' }}
			</view>

		</view>
		<view class="productCollection-cnt">
			<view class="productCollection-cnt-line" v-for="(item,index) in list" :key="index"
				@click="navToGoodDetail(item)">
				<view class="productCollection-cnt-line-circle" v-if="isEdit" @click.stop="recordId(item)">
					<view v-if="selectedId.indexOf(item.id) != -1"></view>
				</view>
				<image class="productCollection-cnt-line-img" :src="imgUrl + parseImgurl(item.mainPicture)?.[0]"
					mode=""></image>
				<view class="productCollection-cnt-line-right">
					<view class="productCollection-cnt-line-right-name">
						{{item.goodName}}
					</view>
					<view class="productCollection-cnt-line-right-lv">
						<image class="productCollection-cnt-line-right-lv-img" src="@/static/index/i_1.png" mode="">
						</image>
						<view>
							{{item.price}}
						</view>
					</view>
					<view class="productCollection-cnt-line-right-operation">
						<!-- <image style="margin-right: 30rpx;" src="@/static/index/btn1.png" mode=""></image> -->
						<image src="@/static/productCollection/delete.png" mode="" @click.stop="cancel(item.id)"></image>
					</view>
				</view>
			</view>
		    <empty v-if="!list?.length"></empty>
		
		</view>

		<view class="productCollection-bottom" v-if="isEdit">
			<view class="productCollection-bottom-left" @click="changeAllSel">
				<view class="productCollection-cnt-line-circle">
					<view v-if="isAllSelect"></view>
				</view>
				全选
			</view>
			<view class="productCollection-bottom-right">
				<!-- <view>
					加入购物车
				</view> -->
				<view @click="showCancelPop">
					删除
				</view>
			</view>
		</view>
		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='取消收藏' :content="'确定要取消收藏这' + selectedId.length + '种商品吗？'"
				:duration="2000" :before-close="false" @confirm="cancel"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script setup lang="ts">
	import {
		computed,
		ref
	} from "vue";
	import {
		parseImgurl
	} from "@/utils";
	import {
		listGet,
		navToGoodDetail
	} from "@/hooks";
	const tabList = ['默认', '降价', '活动', '有券'];
	let popup = ref();
	let selectIndex = ref(0);
	let isEdit = ref(false)
	const imgUrl = uni.env.IMAGE_URL
	//已勾选的id集合
	let selectedId = ref([]);
	//判断是否全选
	const isAllSelect = computed(() => {
		if (list.value.length <= 0) return false
		return list.value.every(i => selectedId.value.indexOf(i.id) != -1);
	})

	//选中tab
	function selectTab(index) {
		selectIndex.value = index
	}
	//改变编辑/完成状态
	function changeIsEdit() {
		isEdit.value = !isEdit.value
	}
	//点击勾选改变勾选状态
	function recordId(item) {
		let sy = selectedId.value.indexOf(item.id);
		if (sy == -1) {
			selectedId.value.push(item.id)
		} else {
			selectedId.value.splice(sy, 1)
		}
	}

	//点击全选
	function changeAllSel() {
		//是否是全选
		if (isAllSelect.value) {
			selectedId.value = []
		} else {
			selectedId.value = list.value.map(i => i.id);
		}
	}

	//确认取消收藏
	async function cancel(id = '') {
		uni.showLoading({
			mask: true
		})
		let ids = selectedId.value.join(',')
		if (id) {
			ids = id
		}
		await uni.http.post(uni.api.delMemberGoodsCollectionByIds, {
			ids
		})
		uni.showToast({
			title: '操作成功~',
			icon: "none"
		})
		setTimeout(() => {
			load()
		}, 500)
	}
	//打开取消收藏提示弹窗
	function showCancelPop() {
		if (selectedId.value.length <= 0) {
			uni.showToast({
				icon: "none",
				title: "请先选择商品~"
			})
			return;
		}
		popup.value.open();
	}
	//请求参数
	let reqOptions = computed(() => {
		return {
			pattern: selectIndex.value + 1 //1：默认；2：降价；3：活动；4：有券(Integer)
		}
	})
	const {
		mode,
		list,
		total,
		refresh
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.findMemberGoodsCollections,
		pdIsLogin: true
	})

	function load() {
		selectedId.value = []
		isEdit.value = false
		refresh()
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.productCollection {
		padding-bottom: 140rpx;

		&-top {
			height: 100rpx;
			background-color: white;
			color: #333333;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			padding-left: 30rpx;
			position: sticky;
			top: 0;
			z-index: 10;
		}

		&-bottom {
			position: fixed;
			bottom: 0;
			left: 0;
			background-color: white;
			width: 750rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			z-index: 5;

			&-left {
				display: flex;
				align-items: center;
				color: #999999;
				font-size: 26rpx;
			}

			&-right {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				>view {
					margin-left: 20rpx;
					height: 68rpx;
					line-height: 68rpx;
					text-align: center;
					background-color: #27A5FF;
					color: white;
					border-radius: 16rpx;
					font-size: 28rpx;
					padding: 0 30rpx;
				}
			}
		}

		&-cnt {
			padding: 30rpx;

			&-line {
				border-radius: 16rpx;
				background-color: white;
				padding: 20rpx;
				margin-bottom: 20rpx;
				display: flex;
				align-items: center;

				&-circle {
					width: 30rpx;
					height: 30rpx;
					border: 2rpx solid #999999;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					margin-right: 30rpx;

					>view {
						width: 14rpx;
						height: 14rpx;
						background-color: #28a7ff;
						border-radius: 50%;
					}
				}

				&-img {
					width: 160rpx;
					height: 160rpx;
					margin-right: 20rpx;
				}

				&-right {
					flex: 1;
					display: flex;
					flex-direction: column;
					justify-content: space-between;
					height: 160rpx;
					position: relative;

					&-name {
						color: #666666;
						font-size: 26rpx;
						line-height: 40rpx;
						display: -webkit-box;
						/* 使用弹性盒子模型 */
						-webkit-box-orient: vertical;
						/* 垂直排列子元素 */
						overflow: hidden;
						/* 隐藏超出部分 */
						-webkit-line-clamp: 2;
						/* 限制显示两行 */
						text-overflow: ellipsis;
						/* 超出部分显示省略号 */
					}

					&-lv {
						display: flex;
						align-items: center;
						color: #E6A600;
						font-size: 28rpx;

						&-img {
							width: 30rpx;
							height: 30rpx;
							margin-right: 16rpx;
						}
					}

					&-operation {
						position: absolute;
						z-index: 2;
						right: 0;
						bottom: 0;
						display: flex;
						align-items: center;
						justify-content: flex-end;

						>image {
							width: 43rpx;
							height: 43rpx;
						}
					}

				}
			}
		}

	}
</style>