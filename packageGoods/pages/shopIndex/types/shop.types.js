/**
 * 店铺相关类型定义
 */

/**
 * 店铺信息类型
 * @typedef {Object} ShopInfo
 * @property {string} id - 店铺ID
 * @property {string} storeName - 店铺名称
 * @property {string} logoAddr - 店铺头像地址
 * @property {string} introduce - 店铺介绍
 * @property {string} storeLevel - 店铺等级
 * @property {string} storeStraight - 是否企业店铺 (1: 企业, 0: 个人)
 * @property {number} totalPerformance - 创业积分
 * @property {number} goodsCount - 商品数量
 * @property {number} salesVolume - 商品销量
 * @property {string} storePicture - 店铺主图
 * @property {string} storePropagandaImages - 宣传图片 (逗号分隔)
 * @property {number} isAttention - 是否关注 (1: 已关注, 0: 未关注)
 */

/**
 * 商品信息类型
 * @typedef {Object} GoodsInfo
 * @property {string} id - 商品ID
 * @property {string} goodName - 商品名称
 * @property {string} mainPicture - 商品主图
 * @property {string} price - 商品价格
 * @property {string} smallPrice - 小程序价格
 * @property {string} salePrice - 销售价格
 * @property {number} stock - 库存数量
 * @property {number} salesVolume - 销量
 * @property {string} isSpecification - 是否有规格 (1: 有规格, 0: 无规格)
 * @property {string} subtitle - 商品副标题
 * @property {string} description - 商品描述
 */

/**
 * 商品分类类型
 * @typedef {Object} CategoryInfo
 * @property {string} id - 分类ID
 * @property {string} name - 分类名称
 * @property {CategoryInfo[]} chlidGoodType - 子分类列表
 */

/**
 * 购物车项类型
 * @typedef {Object} CartItem
 * @property {string} goodId - 商品ID
 * @property {string} goodName - 商品名称
 * @property {string} price - 商品价格
 * @property {number} quantity - 数量
 * @property {string} specification - 规格
 * @property {string} mainPicture - 商品图片
 * @property {string} cartItemId - 购物车项ID
 */

/**
 * 规格信息类型
 * @typedef {Object} SpecificationInfo
 * @property {string} id - 规格ID
 * @property {string} name - 规格名称
 * @property {string} price - 规格价格
 * @property {number} stock - 规格库存
 * @property {string} image - 规格图片
 */

/**
 * Tab导航类型
 * @typedef {Object} TabInfo
 * @property {string} label - Tab标签
 * @property {string} value - Tab值
 * @property {boolean} active - 是否激活
 */

/**
 * 店铺统计数据类型
 * @typedef {Object} ShopStats
 * @property {number} totalPerformance - 创业积分
 * @property {number} goodsCount - 商品数量
 * @property {number} salesVolume - 商品销量
 */

/**
 * API响应类型
 * @typedef {Object} ApiResponse
 * @property {number} code - 响应码
 * @property {string} message - 响应消息
 * @property {any} result - 响应数据
 */

/**
 * 分页信息类型
 * @typedef {Object} PageInfo
 * @property {number} current - 当前页码
 * @property {number} size - 每页大小
 * @property {number} total - 总数量
 * @property {number} pages - 总页数
 */

/**
 * 商品搜索参数类型
 * @typedef {Object} SearchParams
 * @property {string} keyword - 搜索关键词
 * @property {string} sysUserId - 店铺ID
 * @property {number} current - 当前页码
 * @property {number} size - 每页大小
 */

/**
 * 排序参数类型
 * @typedef {Object} SortParams
 * @property {string} sortField - 排序字段 (salesVolume, price, createTime)
 * @property {string} sortType - 排序类型 (up, down)
 */

/**
 * 弹窗状态类型
 * @typedef {Object} ModalState
 * @property {boolean} visible - 是否显示
 * @property {any} data - 弹窗数据
 * @property {boolean} loading - 是否加载中
 */

/**
 * 组件事件类型
 * @typedef {Object} ComponentEvents
 * @property {Function} onTabChange - Tab切换事件
 * @property {Function} onCategorySelect - 分类选择事件
 * @property {Function} onGoodsClick - 商品点击事件
 * @property {Function} onAddToCart - 添加购物车事件
 * @property {Function} onSpecSelect - 规格选择事件
 */

/**
 * 错误信息类型
 * @typedef {Object} ErrorInfo
 * @property {string} message - 错误消息
 * @property {string} code - 错误码
 * @property {any} details - 错误详情
 */

/**
 * 加载状态类型
 * @typedef {Object} LoadingState
 * @property {boolean} loading - 是否加载中
 * @property {string} text - 加载文本
 * @property {boolean} error - 是否有错误
 */

// 导出类型定义（用于JSDoc）
export const ShopTypes = {
  ShopInfo: {},
  GoodsInfo: {},
  CategoryInfo: {},
  CartItem: {},
  SpecificationInfo: {},
  TabInfo: {},
  ShopStats: {},
  ApiResponse: {},
  PageInfo: {},
  SearchParams: {},
  SortParams: {},
  ModalState: {},
  ComponentEvents: {},
  ErrorInfo: {},
  LoadingState: {}
}

// 常量定义
export const SHOP_CONSTANTS = {
  // 店铺等级
  STORE_LEVELS: {
    LEVEL_1: '1',
    LEVEL_2: '2',
    LEVEL_3: '3',
    LEVEL_4: '4',
    LEVEL_5: '5'
  },

  // 店铺类型
  STORE_TYPES: {
    PERSONAL: '0',
    ENTERPRISE: '1'
  },

  // 商品规格
  SPECIFICATION_TYPES: {
    NO_SPEC: '0',
    HAS_SPEC: '1'
  },

  // 关注状态
  ATTENTION_STATUS: {
    NOT_ATTENTION: 0,
    ATTENTION: 1
  },

  // 排序类型
  SORT_TYPES: {
    ASC: 'up',
    DESC: 'down'
  },

  // 排序字段
  SORT_FIELDS: {
    SALES_VOLUME: 'salesVolume',
    PRICE: 'price',
    CREATE_TIME: 'createTime'
  },

  // Tab类型
  TAB_TYPES: {
    HOME: 0,
    CATEGORY: 1,
    GOODS: 2,
    NEW_GOODS: 3
  }
}
