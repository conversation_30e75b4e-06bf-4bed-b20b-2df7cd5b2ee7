// 修复规格选择弹窗中的重复显示问题
.uni-popup .specificationWrap {
  .specificationWrap-proTitle {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .specificationWrap-top {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  .specificationWrap-num {
    display: none !important;
    visibility: hidden !important;
    height: 0 !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

// shopIndex 页面样式优化
.shopIndex {
  background: linear-gradient(180deg, #f8f9fa 0%, #ffffff 100%);
  min-height: 100vh;
  padding-bottom: 200rpx;

  .shopIndex-cnt {
    background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
    margin-bottom: 16rpx;
    border-radius: 20rpx;
    box-shadow:
      0 4rpx 16rpx rgba(0, 0, 0, 0.04),
      0 2rpx 8rpx rgba(0, 0, 0, 0.02);
    border: 1rpx solid rgba(0, 0, 0, 0.02);
    overflow: hidden;

    &.shopIndex-pds {
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      padding: 16rpx;
      gap: 12rpx;
    }


  }

  // 添加成功提示样式
  .add-success-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(0, 0, 0, 0.8);
    border-radius: 16rpx;
    padding: 32rpx 48rpx;
    animation: fadeInOut 2s ease-in-out;

    .add-success-content {
      display: flex;
      flex-direction: column;
      align-items: center;

      .add-success-icon {
        width: 64rpx;
        height: 64rpx;
        margin-bottom: 16rpx;
      }

      .add-success-text {
        color: #ffffff;
        font-size: 28rpx;
      }
    }
  }

  @keyframes fadeInOut {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
    20% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    80% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
  }
}

// 响应式设计
@media screen and (max-width: 750rpx) {
  .shopIndex {
    .shopIndex-cnt {
      &.shopIndex-pds {
        padding: 16rpx;
      }
    }
  }
}
