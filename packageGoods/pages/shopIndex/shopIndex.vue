<template>
	<view class="shopIndex">
		<!-- 使用新的Tab导航组件 -->
		<ShopTabNavigation
			:tabs="tabList"
			:active-index="tabSelectedIndex"
			:secondary-tabs="tabListSecond"
			:secondary-active-index="tabSecondSelectedIndex"
			:sort-type="sortType"
			:is-attention="infos.isAttention == 1"
			@tab-change="handleTabChange"
			@secondary-tab-change="handleSecondaryTabChange"
			@attention-click="attentionStore" />

		<view class="shopIndex-cnt" v-if="tabSelectedIndex === 0">
			<!-- 使用新的店铺信息卡片组件 -->
			<ShopInfoCard
				:shop-info="infos"
				@logo-click="navigateToStoreCredentials" />
		</view>
		<!-- 使用新的分类容器组件 -->
		<view class="shopIndex-cnt" v-if="tabSelectedIndex === 1">
			<CategoryContainer
				:categories="typeAllList"
				:active-category-index="productTypeSelectedIndex"
				:active-sub-category-index="productBottomLeftSelectedIndex"
				:goods-list="commodityList"
				:loading="false"
				:cart-count-map="cartCountMap"
				@category-select="handleCategorySelect"
				@sub-category-select="handleSubCategorySelect"
				@more-categories="showMoreCategories"
				@goods-click="navToGoodDetail"
				@spec-select="handleSpecSelect"
				@quantity-change="handleQuantityChange" />
		</view>
		<view class="shopIndex-cnt shopIndex-pds" v-if="tabSelectedIndex === 2">
			<view v-for="(item,index) in bbList" :key="index">
				<productDetail :info='item' width="340rpx" height="460rpx" sizeType='normal'></productDetail>
			</view>
		</view>
		<view class="shopIndex-cnt shopIndex-new" v-if="tabSelectedIndex === 3">
			<view class="shopIndex-new-date">
				01月01日
			</view>
			<view class="shopIndex-new-wrap">
				<image class="shopIndex-new-wrap-img"
					src="https://cdn.zebraui.com/zebra-ui/images/swipe-demo/swipe1.jpg" mode="widthFix"></image>
				<view class="shopIndex-new-wrap-name">
					这里是产品名称这里是商品名称这里是产品名称这里是商
				</view>
				<view class="shopIndex-new-wrap-lv">
					<view>
						<image src="@/static/index/i_1.png" mode=""></image>
						<view>
							1234
						</view>
					</view>
					<view>
						已售 1234
					</view>
				</view>
			</view>
		</view>

		<!-- 购物车浮动按钮 - 不再显示，改用快捷结算栏 -->
		<!-- <cartAction v-if="tabSelectedIndex === 1"></cartAction> -->

		<!-- 快捷结算栏 - 在有购物车商品时显示，不限制Tab页面 -->
		<quickCheckoutBar
			:show="showQuickCheckout"
			:totalCount="quickCheckoutCount"
			:totalAmount="quickCheckoutTotal"
			:currentPageItems="currentPageCartItems"
			@checkout="handleQuickCheckout">
		</quickCheckoutBar>

		<!-- 添加成功提示 -->
		<view
			v-if="showAddSuccess"
			class="add-success-toast">
			<view class="add-success-content">
				<image src="/static/cart/cart.png" class="add-success-icon"></image>
				<text class="add-success-text">已添加到购物车</text>
			</view>
		</view>

		<!-- 使用新的规格选择弹窗组件 -->
		<SpecificationModal
			:goods-info="selectInfo"
			:visible="showSpecModal"
			@close="handleSpecModalClose"
			@add-to-cart="handleNewSpecAddToCart"
			@buy-now="handleSpecBuyNow"
			@specification-change="handleSpecificationChange" />

		<!-- 使用新的更多分类弹窗组件 -->
		<CategoryMoreModal
			:categories="typeAllList?.[productTypeSelectedIndex]?.chlidGoodType || []"
			:active-index="productBottomLeftSelectedIndex"
			:visible="showMoreCategoriesModal"
			@close="handleMoreCategoriesClose"
			@category-select="handleMoreCategorySelect"
			@confirm="handleMoreCategoryConfirm" />

	</view>
</template>

<script setup>
	import {
		computed,
		nextTick,
		ref,
		watch,
		onMounted
	} from 'vue';
	import {
		onShow,
		onLoad,
		onReachBottom,
		onUnload
	} from '@dcloudio/uni-app';
	import productDetail from '@/components/productDetail/productDetail.vue';
	import quickCheckoutBar from '@/components/quickCheckoutBar/quickCheckoutBar.vue';
	// 导入新的组件
	import ShopTabNavigation from './components/ShopHeader/ShopTabNavigation.vue';
	import ShopInfoCard from './components/ShopHeader/ShopInfoCard.vue';
	import CategoryContainer from './components/ShopCategory/CategoryContainer.vue';
	import SpecificationModal from './components/ShopModals/SpecificationModal.vue';
	import CategoryMoreModal from './components/ShopModals/CategoryMoreModal.vue';
	// 导入业务逻辑 - 暂时注释未使用的
	// import { useCategoryNavigation } from './composables/useCategoryNavigation.js';
	// import { useCartManagement } from './composables/useCartManagement.js';
	// import { useShopData } from './composables/useShopData.js';
	// import { useGoodsOperations } from './composables/useGoodsOperations.js';
		import {
		listGet,
		navToGoodDetail,
		saleChannelDealAbout,
		specificationPopPub
	} from '@/hooks'
	import {
		cartCountHooks
	} from '@/hooks/getCartCount.js'
	const tabList = [
		'首页',
		'分类',
		'宝贝'
	]
	const tabListSecond = [
		'综合',
		'销量',
		'助力值',
	]
	const tabSelectedIndex = ref(0)
	const productTypeSelectedIndex = ref(0)
	const productBottomLeftSelectedIndex = ref(0)
	const tabSecondSelectedIndex = ref(0)
	const sortType = ref('up')
	let options = ref({});
	let typeAllList = ref([])
	let infos = ref({})

	// 购物车相关状态
	const showAddSuccess = ref(false)

	// 使用现有的购物车数量 hooks
	const { cartCountRefresh } = cartCountHooks()

	// 规格选择弹窗相关 - 只保留使用的
	const {
		selectInfo,
		addCart,
		toOrder,
		showSpecificationWrap,
		specificationWrapRef,
	} = specificationPopPub()

	// 弹窗状态管理
	const showSpecModal = ref(false)
	const showMoreCategoriesModal = ref(false)

	// 定义 sysUserId 计算属性
	const sysUserId = computed(() => options.value?.sysUserId || '')

	// 注释掉未使用的 Composables，保留核心功能
	// const {
	//   shopInfo: shopInfoData,
	//   loading: shopLoading,
	//   isAttention,
	//   propagandaImages: shopPropagandaImages,
	//   shopStats,
	//   fetchShopInfo,
	//   toggleAttention,
	//   viewStoreCredentials
	// } = useShopData(sysUserId)

	// const {
	//   newGoodsList,
	//   allGoodsList,
	//   fetchNewGoods,
	//   fetchAllGoods,
	//   navigateToGoodsDetail: navToGoodsDetail,
	//   formatGoodsPrice,
	//   getGoodsMainImage
	// } = useGoodsOperations(sysUserId)

	// 添加购物车数量管理
	const cartCountMap = ref({}) // 存储商品ID和对应的购物车数量
	const cartItemIdMap = ref({}) // 存储商品ID和对应的购物车项ID的映射

	// 删除未使用的 showQuantityControl

	// 快捷结算相关状态 - 改用响应式对象
	const currentPageCartItems = ref({}) // 当前页面添加的购物车商品 {goodId: {quantity, price, cartItemId}}

	// 快捷结算计算属性
	const showQuickCheckout = computed(() => {
		const itemCount = Object.keys(currentPageCartItems.value).length
		console.log('showQuickCheckout计算:', itemCount, currentPageCartItems.value)
		console.log('当前tab:', tabSelectedIndex.value)
		return itemCount > 0
	})
	const quickCheckoutCount = computed(() => {
		const count = Object.values(currentPageCartItems.value)
			.reduce((total, item) => total + item.quantity, 0)
		console.log('quickCheckoutCount计算:', count)
		return count
	})
	const quickCheckoutTotal = computed(() => {
		const total = Object.values(currentPageCartItems.value)
			.reduce((total, item) => total + (item.quantity * parseFloat(item.price || 0)), 0)
		console.log('quickCheckoutTotal计算:', total)
		return total
	})

	// 获取商品在购物车中的数量
	const getCartCount = (goodId) => {
		return cartCountMap.value[goodId] || 0
	}

	// 获取商品对应的购物车项ID
	const getCartItemId = (goodId) => {
		return cartItemIdMap.value[goodId]
	}

	// 删除未使用的 handleFirstAdd 方法

	// 更新购物车数量 - 优化版本
	const handleUpdateCart = async (item, delta) => {
		try {
			const currentCount = getCartCount(item.id)
			const cartItemId = getCartItemId(item.id)

			// 如果是减少且当前数量为0，直接返回
			if (delta < 0 && currentCount <= 0) return

			const newCount = Math.max(0, currentCount + delta)

			// 显示加载提示
			uni.showLoading({
				title: delta > 0 ? '添加中...' : '更新中...',
				mask: true
			})

			if (delta > 0 && !cartItemId) {
				// 首次添加到购物车
				const { data: addResult } = await uni.http.post(uni.api.addGoodToShoppingCart, {
					goodId: item.id,
					specification: '无',
					isPlatform: 0,
					quantity: 1 // 每次添加1个
				})

				if (!addResult?.success) {
					throw new Error(addResult?.message || '添加失败')
				}

				// 保存返回的购物车项ID
				cartItemIdMap.value[item.id] = addResult.result
				cartCountMap.value[item.id] = 1

				// 更新当前页面购物车商品记录
				updateCurrentPageCartItem(item, 1, addResult.result)

			} else if (cartItemId) {
				// 已有购物车项，直接更新数量
				await uni.http.post(uni.api.updateCarGood, {
					id: cartItemId,
					quantity: newCount // 直接传入目标数量
				})

				// 更新本地数量，如果为0则清除ID映射
				if (newCount === 0) {
					delete cartItemIdMap.value[item.id]
					delete cartCountMap.value[item.id]
					// 从当前页面记录中移除
					delete currentPageCartItems.value[item.id]
				} else {
					cartCountMap.value[item.id] = newCount
					// 更新当前页面购物车商品记录
					updateCurrentPageCartItem(item, newCount, cartItemId)
				}
			}

			// 刷新全局购物车数量
			await cartCountRefresh()

			// 显示成功提示
			if (delta > 0) {
				showAddSuccess.value = true
				setTimeout(() => {
					showAddSuccess.value = false
				}, 2000)
			}

		} catch (error) {
			console.error('更新购物车失败:', error)
			uni.showToast({
				title: error.message || '操作失败',
				icon: 'none'
			})

			// 发生错误时，重新同步购物车状态
			await syncCartState()

		} finally {
			uni.hideLoading()
		}
	}

	// 更新当前页面购物车商品记录
	const updateCurrentPageCartItem = (item, quantity, cartItemId, specPrice = null) => {
		console.log('updateCurrentPageCartItem调用:', {
			goodId: item.id,
			quantity,
			cartItemId,
			basePrice: item.price || item.smallPrice || item.salePrice || 0,
			specPrice: specPrice
		})

		if (quantity > 0) {
			// 优先使用规格价格，其次使用商品基础价格
			const finalPrice = specPrice || item.price || item.smallPrice || item.salePrice || 0

			currentPageCartItems.value[item.id] = {
				quantity: quantity,
				price: finalPrice,
				cartItemId: cartItemId,
				goodName: item.goodName,
				mainPicture: item.mainPicture
			}
		} else {
			delete currentPageCartItems.value[item.id]
		}

		console.log('更新后的currentPageCartItems:', currentPageCartItems.value)
		console.log('showQuickCheckout应该是:', Object.keys(currentPageCartItems.value).length > 0)
	}

	// 同步购物车状态
	const syncCartState = async () => {
		try {
			const { data: cartData } = await uni.http.get(uni.api.getCarGoodByMemberId)
			if (cartData?.result?.storeGoods) {
				const newCartCountMap = {}
				const newCartItemIdMap = {}
				const newCurrentPageCartItems = {}

				cartData.result.storeGoods.forEach(store => {
					if (store.myStoreGoods) {
						store.myStoreGoods.forEach(cartItem => {
							if (cartItem.goodStoreListId) {
								const goodId = cartItem.goodStoreListId
								newCartCountMap[goodId] = (newCartCountMap[goodId] || 0) + cartItem.quantity
								// 保存购物车项ID映射（取最新的一个）
								newCartItemIdMap[goodId] = cartItem.id

								// 修复：快捷结算栏应该显示购物车中的所有商品，不受当前页面商品列表影响
								// 如果商品已经在快捷结算栏中，保持显示；如果是新的购物车商品，也要添加
								if (currentPageCartItems.value[goodId] || cartItem.quantity > 0) {
									newCurrentPageCartItems[goodId] = {
										quantity: cartItem.quantity,
										price: cartItem.price || currentPageCartItems.value[goodId]?.price || '0',
										cartItemId: cartItem.id,
										goodName: cartItem.goodName || currentPageCartItems.value[goodId]?.goodName || '商品',
										mainPicture: cartItem.mainPicture || currentPageCartItems.value[goodId]?.mainPicture || ''
									}
								}
							}
						})
					}
				})

				cartCountMap.value = newCartCountMap
				cartItemIdMap.value = newCartItemIdMap

				// 更新快捷结算数据：直接使用购物车中的所有商品
				currentPageCartItems.value = { ...newCurrentPageCartItems }

				console.log('同步购物车状态完成:', {
					cartCountMap: cartCountMap.value,
					currentPageCartItems: currentPageCartItems.value,
					showQuickCheckout: showQuickCheckout.value
				})
			}
		} catch (error) {
			console.error('同步购物车状态失败:', error)
		}
	}

	// 生命周期钩子
	onMounted(() => {
		cartCountRefresh();
		syncCartState();
	});

	onShow(() => {
		cartCountRefresh();
		syncCartState();
	});

	// 页面卸载时清空快捷结算栏
	onUnload(() => {
		console.log('页面卸载，清空快捷结算栏')
		currentPageCartItems.value = {}
	});

	// 处理店铺宣传图片
	const propagandaImages = computed(() => {
		if (infos.value?.storePropagandaImages) {
			return infos.value.storePropagandaImages.split(',');
		}
		return [];
	})

	async function refresh() {
		try {
			uni.showLoading({
				mask: true
			})

			// 1. 获取店铺基本信息
			const { data } = await uni.http.post(uni.api.storeManageIndex, options.value)
			if (!data || !data.result) {
				throw new Error('获取店铺信息失败')
			}

			infos.value = data.result
			uni.setNavigationBarTitle({
				title: data.result.storeName
			})

			// 2. 获取商品分类
			const { data: allList } = await uni.http.get(uni.api.findMoreGoodType, {
				params: {
					goodTypeId: options.value.sysUserId,
					isPlatform: 0,
					type: 0
				}
			})

			if (!allList || !allList.result) {
				throw new Error('获取商品分类失败')
			}

			typeAllList.value = allList.result

			// 3. 确保有分类数据后再加载商品
			await nextTick()
			if (typeAllList.value && typeAllList.value.length > 0) {
				reloadCommodityList()

				// 强制刷新商品数据
				setTimeout(() => {
					console.log('强制刷新商品数据')
					getCommodity()
				}, 500)
			}

			// 4. 获取宝贝数据
			getBbData()

		} catch (error) {
			console.error('refresh error:', error)
			uni.showToast({
				title: error.message || '加载失败',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	}

	// 优化请求参数计算
	const reqOptions = computed(() => {
		try {
			// 检查是否有分类数据
			if (!typeAllList.value || !typeAllList.value.length) {
				console.warn('没有分类数据，返回空参数阻止商品加载')
				return null // 返回null阻止商品加载
			}

			// 获取当前选中的分类
			const currentCategory = typeAllList.value?.[productTypeSelectedIndex.value]
			if (!currentCategory) {
				console.warn('未找到当前选中的分类')
				return null // 返回null阻止商品加载
			}

			// 确定使用哪个分类ID
			let goodTypeId
			if (currentCategory.chlidGoodType?.length > 0) {
				// 有二级分类，使用二级分类ID
				goodTypeId = currentCategory.chlidGoodType[productBottomLeftSelectedIndex.value]?.id
			}

			// 如果没有二级分类ID，使用一级分类ID
			if (!goodTypeId) {
				goodTypeId = currentCategory.id
			}

			return {
				isPlatform: 0,
				pattern: 0,
				isWholesale: '0',
				isSelectedProducts: '0',
				goodTypeId: String(goodTypeId) // 确保 ID 为字符串类型
			}
		} catch (error) {
			console.error('计算请求参数错误:', error)
			return null // 返回null阻止商品加载
		}
	})

	watch(() => ({
		productTypeSelectedIndex: productTypeSelectedIndex.value,
		productBottomLeftSelectedIndex: productBottomLeftSelectedIndex.value
	}), () => {
		reloadCommodityList()
	})



	// Tab切换事件处理
	const handleTabChange = (index) => {
		tabSelectedIndex.value = index
	}

	// 二级Tab切换事件处理
	const handleSecondaryTabChange = (index) => {
		if (tabSecondSelectedIndex.value === 2 && tabSecondSelectedIndex.value === index) {
			sortType.value = sortType.value === 'up' ? 'down' : 'up'
		} else {
			tabSecondSelectedIndex.value = index
		}
	}

	// 保留原有方法以兼容其他地方的调用
	const tabSecondSelectedIndexChange = (index) => {
		handleSecondaryTabChange(index)
	}

	// 新增的组件事件处理方法
	const handleCategorySelect = (index) => {
		productTypeSelectedIndex.value = index
		productBottomLeftSelectedIndex.value = 0 // 重置二级分类
		// 触发商品列表刷新
		getCommodity()
	}

	const handleSubCategorySelect = (index) => {
		productBottomLeftSelectedIndex.value = index
		// 触发商品列表刷新
		getCommodity()
	}

	const handleSpecSelect = (goods) => {
		selectInfo.value = goods
		showSpecModal.value = true
	}

	const handleQuantityChange = (goods, delta) => {
		handleUpdateCart(goods, delta)
	}

	// 新弹窗事件处理方法
	const handleSpecModalClose = () => {
		showSpecModal.value = false
	}

	const handleNewSpecAddToCart = async (cartData) => {
		try {
			// 直接调用后端接口，传递正确的规格数据
			uni.showLoading({
				title: '添加中...',
				mask: true
			})

			const { data: addResult } = await uni.http.post(uni.api.addGoodToShoppingCart, {
				goodId: cartData.goods.id,
				specification: cartData.specification, // 现在是正确的字符串格式
				isPlatform: 0,
				quantity: cartData.quantity
			})

			if (!addResult?.success) {
				throw new Error(addResult?.message || '添加失败')
			}

			// 更新本地状态
			cartItemIdMap.value[cartData.goods.id] = addResult.result
			cartCountMap.value[cartData.goods.id] = cartData.quantity

			// 更新当前页面购物车商品记录
			updateCurrentPageCartItem(cartData.goods, cartData.quantity, addResult.result, cartData.price)

			// 刷新全局购物车数量
			await cartCountRefresh()

			// 显示成功提示
			showAddSuccess.value = true
			setTimeout(() => {
				showAddSuccess.value = false
			}, 2000)

			showSpecModal.value = false

		} catch (error) {
			console.error('规格商品添加购物车失败:', error)
			uni.showToast({
				title: error.message || '添加失败',
				icon: 'none'
			})
		} finally {
			uni.hideLoading()
		}
	}

	const handleSpecBuyNow = (orderData) => {
		// 立即购买逻辑
		toOrder()
		showSpecModal.value = false
	}

	const handleSpecificationChange = (specData) => {
		currentSpecification.value = specData
	}

	const handleMoreCategoriesClose = () => {
		showMoreCategoriesModal.value = false
	}

	const handleMoreCategorySelect = (index) => {
		// 临时选择，不立即应用
	}

	const handleMoreCategoryConfirm = (index) => {
		productBottomLeftSelectedIndex.value = index
		showMoreCategoriesModal.value = false
		getCommodity() // 刷新商品列表
	}

	// 先定义commodityList，确保在watch监听器之前初始化
	const {
		mode,
		list: commodityList = ref([]),
		refresh: getCommodity
	} = listGet({
		options: computed(() => {
			// 如果reqOptions为null，返回一个标记对象阻止请求
			if (reqOptions.value === null) {
				return { _skipRequest: true }
			}
			return reqOptions.value
		}),
		apiUrl: uni.api.findGoodListByGoodType,
		isPullDownRefresh: false,
		isWatchOptions: true,
		isReqTypeReq: false,
		openLoadingShow: false,
		isReachBottom: false
	})

	// 监听商品列表变化，获取购物车数量 - 在commodityList定义之后
	watch(() => commodityList?.value, async (newVal) => {
		if (!newVal || !Array.isArray(newVal) || newVal.length === 0) return;

		// 同步购物车状态
		await syncCartState();
	}, { immediate: true, deep: true });

	const {
		list: bbList,
		refresh: getBbData
	} = listGet({
		options: computed(() => {
			let res = {
				pattern: tabSecondSelectedIndex.value,
				// sysUserId: infos.value?.sysUserId,
				isPlatform: 0
			}
			if (tabSecondSelectedIndex.value === 2) {
				if (sortType.value === 'up') {
					res.pattern = 4
				} else {
					res.pattern = 3
				}
			}
			return res
		}),
		apiUrl: uni.api.searchGoodList,
		isReachBottom: false,
		isPullDownRefresh: false,
		isWatchOptions: false,
		isReqTypeReq: false,
		openLoadingShow: false
	})

	watch(() => ({
		tabSecondSelectedIndex: tabSecondSelectedIndex.value,
		sortType: sortType.value
	}), () => {
		getBbData()
	})

	// 重新获取商品列表
	async function reloadCommodityList() {
		try {
			console.log('开始重新加载商品列表')
			console.log('typeAllList:', typeAllList.value)
			console.log('当前一级分类索引:', productTypeSelectedIndex.value)
			console.log('当前二级分类索引:', productBottomLeftSelectedIndex.value)

			// 检查分类数据
			if (!typeAllList.value || !typeAllList.value.length) {
				console.warn('分类列表为空，显示暂无数据')
				// 清空商品列表
				commodityList.value = []
				return
			}

			// 确保选择的分类索引有效
			if (productTypeSelectedIndex.value >= typeAllList.value.length) {
				console.warn('分类索引超出范围，重置为0')
				productTypeSelectedIndex.value = 0
			}

			const currentCategory = typeAllList.value[productTypeSelectedIndex.value]
			if (!currentCategory) {
				console.warn('当前选中的一级分类不存在，显示暂无数据')
				commodityList.value = []
				return
			}

			// 检查是否有二级分类
			const hasSubCategory = currentCategory.chlidGoodType?.length > 0
			console.log('是否有二级分类:', hasSubCategory)

			if (hasSubCategory) {
				// 有二级分类，检查二级分类索引是否有效
				if (productBottomLeftSelectedIndex.value >= currentCategory.chlidGoodType.length) {
					console.warn('二级分类索引超出范围，重置为0')
					productBottomLeftSelectedIndex.value = 0
				}

				// 有二级分类，检查二级分类是否存在
				const subCategory = currentCategory.chlidGoodType[productBottomLeftSelectedIndex.value]
				if (!subCategory) {
					console.warn('当前选中的二级分类不存在，显示暂无数据')
					commodityList.value = []
					return
				}
				console.log('使用二级分类ID获取商品:', subCategory.id)
			} else {
				console.log('使用一级分类ID获取商品:', currentCategory.id)
			}

			// 调用获取商品列表接口
			await getCommodity()
			console.log('商品列表加载完成')

			// 检查加载结果
			if (!commodityList.value || commodityList.value.length === 0) {
				console.log('商品列表为空，可能是当前分类下没有商品')
			}

		} catch (error) {
			console.error('重新加载商品列表失败:', error)
			uni.showToast({
				title: '加载商品失败',
				icon: 'none'
			})
			// 出错时清空商品列表
			commodityList.value = []
		}
	}

	// 关注店铺
	const attentionStore = async () => {
		uni.showLoading({
			mask: true
		})
		let apiUrl = uni.api.addMemberAttentionStore
		if (infos.value.isAttention == 1) {
			apiUrl = uni.api.delMemberAttentionStore
		}
		await uni.http.post(apiUrl, {
			ids: infos.value.matId
		})
		uni.showToast({
			title: infos.value.isAttention == 1 ? '取消关注成功~' : '关注成功~',
			icon: 'none'
		})
		setTimeout(() => {
			refresh()
		}, 250)

	}

	saleChannelDealAbout(computed(() => {
		return infos.value?.sysUserId || ''
	}), 2)


	onLoad((o) => {
		try {
			console.log('页面加载参数:', o)

			// 验证必要参数
			if (!o || !o.sysUserId) {
				throw new Error('缺少必要参数：店铺ID')
			}

			// 初始化页面参数
			options.value = {
				...o,
				sysUserId: String(o.sysUserId) // 确保 sysUserId 为字符串类型
			}

			console.log('初始化页面参数:', options.value)

			// 重置页面状态
			productTypeSelectedIndex.value = 0
			productBottomLeftSelectedIndex.value = 0
			tabSelectedIndex.value = 0

			// 加载页面数据
			refresh()

			// 获取购物车数量
			cartCountRefresh()

		} catch (error) {
			console.error('页面加载错误:', error)
			uni.showToast({
				title: error.message || '页面加载失败',
				icon: 'none',
				duration: 2000
			})

			// 延迟返回上一页
			setTimeout(() => {
				uni.navigateBack({
					delta: 1
				})
			}, 2000)
		}
	})

	onReachBottom(() => {
		console.log(tabSelectedIndex.value)
		if (tabSelectedIndex.value === 1) {
			getCommodity(2)
		} else if (tabSelectedIndex.value === 2) {
			getBbData(2)
		}
	})

	// 导航到店铺证件信息页面
	const navigateToStoreCredentials = () => {
		// 只有当店铺主体类型是企业（storeStraight=1）时才跳转
		// 处理API返回的可能是字符串"1"或数字1的情况
		console.log('店铺类型值:', infos.value.storeStraight, '类型:', typeof infos.value.storeStraight);
		// 将storeStraight转换为字符串进行比较，更安全
		if (String(infos.value.storeStraight) === "1") {
			console.log('判断为企业店铺，准备跳转到证件页面');
			// 将店铺信息传递给证件信息页面
			const storeInfoStr = encodeURIComponent(JSON.stringify(infos.value));
			uni.navigateTo({
				url: `/packageTest/storeCredentials/storeCredentials?storeInfo=${storeInfoStr}`
			});
		} else {
			console.log('非企业店铺，不跳转');
		}
	}

	// 购物车相关方法
	const handleAddToCart = async (item) => {
		try {
			// 检查商品是否有规格
			if (item.isSpecification === '1') {
				// 有规格的商品，显示规格选择弹窗
				showSpecificationWrap(item)
				return
			}

			// 无规格商品，直接添加到购物车
			await handleUpdateCart(item, 1)

		} catch (error) {
			console.error('添加购物车失败:', error)
			uni.showToast({
				title: '添加失败',
				icon: 'none'
			})
		}
	}

	// 规格选择后添加到购物车
	const handleSpecAddToCart = async () => {
		try {
			await addCart({}, async (result) => {
				console.log('规格添加购物车回调函数被调用:', result)

				// 添加成功后的回调
				showAddSuccess.value = true
				setTimeout(() => {
					showAddSuccess.value = false
				}, 2000)

				// 刷新购物车数量
				await cartCountRefresh()

				// 更新当前页面购物车商品记录（规格商品）
				if (result && selectInfo.value) {
					const specRef = specificationWrapRef.value
					if (specRef) {
						console.log('规格商品添加到购物车成功:', {
							selectInfo: selectInfo.value,
							quality: specRef.quality,
							result: result
						})

						// 获取购物车项ID - result可能直接是ID
						const cartItemId = result.id || result.cartItemId || result

						// 更新购物车项ID映射
						cartItemIdMap.value[selectInfo.value.id] = cartItemId

						// 累加数量而不是覆盖
						const currentQuantity = cartCountMap.value[selectInfo.value.id] || 0
						const newQuantity = currentQuantity + (specRef.quality || 1)
						cartCountMap.value[selectInfo.value.id] = newQuantity

						// 获取规格价格
						const specPrice = specRef.resultInfo?.price || selectInfo.value?.price || null

						// 更新当前页面购物车商品记录 - 使用新的总数量
						updateCurrentPageCartItem(
							selectInfo.value,
							newQuantity,
							cartItemId,
							specPrice
						)

						console.log('规格商品更新快捷结算数据:', {
							cartItemId,
							newQuantity,
							currentPageCartItems: currentPageCartItems.value,
							showQuickCheckout: showQuickCheckout.value,
							quickCheckoutCount: quickCheckoutCount.value
						})
					}
				}
			})
		} catch (error) {
			console.error('添加购物车失败:', error)
		}
	}

	// 更多分类弹窗方法
	const showMoreCategories = () => {
		showMoreCategoriesModal.value = true
	}

	const closeMoreCategories = () => {
		showMoreCategoriesModal.value = false
	}

	const selectMoreCategory = (index) => {
		productBottomLeftSelectedIndex.value = index
		closeMoreCategories()
	}

	const moreCategoriesPopChange = (e) => {
		// 弹窗状态变化处理
	}

	// 获取店铺等级文本
	const getLevelText = (level) => {
		const levelMap = {
			'1': 'LV1',
			'2': 'LV2',
			'3': 'LV3',
			'4': 'LV4',
			'5': 'LV5'
		}
		return levelMap[level] || `LV${level}`
	}

	// 获取店铺等级名称
	const getLevelName = (level) => {
		const levelNameMap = {
			'1': '新手商家',
			'2': '成长商家',
			'3': '优质商家',
			'4': '金牌商家',
			'5': '钻石商家'
		}
		return levelNameMap[level] || '普通商家'
	}

	// 获取店铺等级描述
	const getLevelDescription = (level) => {
		const levelDescMap = {
			'1': '刚起步的新商家，充满潜力',
			'2': '正在成长中的商家，值得关注',
			'3': '服务优质的可靠商家',
			'4': '经验丰富的金牌商家',
			'5': '顶级品质的钻石商家'
		}
		return levelDescMap[level] || '努力经营中的商家'
	}

	// 显示库存不足提示
	const showStockTip = () => {
		uni.showToast({
			title: '库存不足',
			icon: 'none',
			duration: 1500
		})
	}

	// 处理快捷结算
	const handleQuickCheckout = (selectedItems) => {
		try {
			// 将选中的商品信息存储到本地，供购物车页面使用
			const checkoutData = {
				selectedItems: selectedItems,
				fromQuickCheckout: true,
				timestamp: Date.now()
			}

			uni.setStorageSync('quickCheckoutData', checkoutData)

			// 跳转到购物车页面
			uni.navigateTo({
				url: '/pages/cart/cart?fromQuickCheckout=1'
			})

		} catch (error) {
			console.error('快捷结算跳转失败:', error)
			uni.showToast({
				title: '跳转失败',
				icon: 'none'
			})
		}
	}

	// 页面切换时的处理 - 修复：分类切换不应该清空快捷结算栏
	watch(() => tabSelectedIndex.value, (newVal, oldVal) => {
		console.log('Tab切换:', oldVal, '->', newVal)
		// 注释掉清空逻辑，快捷结算栏应该在整个店铺浏览过程中保持
		// 只有离开整个店铺页面时才清空（在onUnload中处理）
		// if (oldVal === 1 && newVal !== 1) {
		//   currentPageCartItems.value = {}
		//   console.log('页面切换，清空currentPageCartItems')
		// }
	})

	// 删除重复的方法定义，使用前面已定义的方法
</script>

<style lang="scss" scoped>
@import './styles/shop-index.scss';
</style>