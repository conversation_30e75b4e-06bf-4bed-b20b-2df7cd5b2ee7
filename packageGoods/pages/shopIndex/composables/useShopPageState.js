import { ref, computed, watch } from 'vue'

/**
 * 店铺页面状态管理 Composable
 * 统一管理页面的所有状态和交互逻辑
 */
export function useShopPageState(shopId) {
  // ===== 基础状态 =====
  const tabSelectedIndex = ref(0)
  const tabSecondSelectedIndex = ref(0)
  const sortType = ref('up')
  
  // ===== 分类相关状态 =====
  const productTypeSelectedIndex = ref(0)
  const productBottomLeftSelectedIndex = ref(0)
  const typeAllList = ref([])
  
  // ===== 弹窗状态 =====
  const showSpecModal = ref(false)
  const showMoreCategoriesModal = ref(false)
  const selectInfo = ref({})
  
  // ===== 购物车状态 =====
  const cartCountMap = ref({})
  const cartItemIdMap = ref({})
  const currentPageCartItems = ref({})
  const showAddSuccess = ref(false)
  
  // ===== 商品数据 =====
  const commodityList = ref([])
  const bbList = ref([])
  const infos = ref({})
  
  // ===== 计算属性 =====
  const showQuickCheckout = computed(() => {
    return Object.keys(currentPageCartItems.value).length > 0
  })
  
  const quickCheckoutCount = computed(() => {
    return Object.values(currentPageCartItems.value)
      .reduce((total, item) => total + item.quantity, 0)
  })
  
  const quickCheckoutTotal = computed(() => {
    return Object.values(currentPageCartItems.value)
      .reduce((total, item) => total + (item.quantity * parseFloat(item.price || 0)), 0)
  })
  
  // ===== Tab相关方法 =====
  const handleTabChange = (index) => {
    tabSelectedIndex.value = index
  }
  
  const handleSecondaryTabChange = (index) => {
    if (tabSecondSelectedIndex.value === 2 && tabSecondSelectedIndex.value === index) {
      sortType.value = sortType.value === 'up' ? 'down' : 'up'
    } else {
      tabSecondSelectedIndex.value = index
    }
  }
  
  // ===== 分类相关方法 =====
  const handleCategorySelect = (index) => {
    productTypeSelectedIndex.value = index
    productBottomLeftSelectedIndex.value = 0
  }
  
  const handleSubCategorySelect = (index) => {
    productBottomLeftSelectedIndex.value = index
  }
  
  // ===== 弹窗相关方法 =====
  const showMoreCategories = () => {
    showMoreCategoriesModal.value = true
  }
  
  const handleMoreCategoriesClose = () => {
    showMoreCategoriesModal.value = false
  }
  
  const handleMoreCategoryConfirm = (index) => {
    productBottomLeftSelectedIndex.value = index
    showMoreCategoriesModal.value = false
  }
  
  const handleSpecSelect = (goods) => {
    selectInfo.value = goods
    showSpecModal.value = true
  }
  
  const handleSpecModalClose = () => {
    showSpecModal.value = false
  }
  
  // ===== 购物车相关方法 =====
  const getCartCount = (goodId) => {
    return cartCountMap.value[goodId] || 0
  }
  
  const updateCurrentPageCartItem = (item, quantity, cartItemId, specPrice = null) => {
    if (quantity > 0) {
      const finalPrice = specPrice || item.price || item.smallPrice || item.salePrice || 0
      currentPageCartItems.value[item.id] = {
        quantity,
        price: finalPrice,
        cartItemId,
        goodName: item.goodName,
        mainPicture: item.mainPicture
      }
    } else {
      delete currentPageCartItems.value[item.id]
    }
  }
  
  const showSuccessToast = () => {
    showAddSuccess.value = true
    setTimeout(() => {
      showAddSuccess.value = false
    }, 2000)
  }
  
  // ===== 数据同步方法 =====
  const syncCartState = async () => {
    try {
      const { data: cartData } = await uni.http.get(uni.api.getCarGoodByMemberId)
      if (cartData?.result?.storeGoods) {
        const newCartCountMap = {}
        const newCartItemIdMap = {}
        
        cartData.result.storeGoods.forEach(store => {
          if (store.myStoreGoods) {
            store.myStoreGoods.forEach(cartItem => {
              if (cartItem.goodStoreListId) {
                const goodId = cartItem.goodStoreListId
                newCartCountMap[goodId] = (newCartCountMap[goodId] || 0) + cartItem.quantity
                newCartItemIdMap[goodId] = cartItem.id
              }
            })
          }
        })
        
        cartCountMap.value = newCartCountMap
        cartItemIdMap.value = newCartItemIdMap
      }
    } catch (error) {
      console.error('同步购物车状态失败:', error)
    }
  }
  
  // ===== 重置方法 =====
  const resetPageState = () => {
    tabSelectedIndex.value = 0
    tabSecondSelectedIndex.value = 0
    productTypeSelectedIndex.value = 0
    productBottomLeftSelectedIndex.value = 0
    showSpecModal.value = false
    showMoreCategoriesModal.value = false
    currentPageCartItems.value = {}
    showAddSuccess.value = false
  }
  
  return {
    // 状态
    tabSelectedIndex,
    tabSecondSelectedIndex,
    sortType,
    productTypeSelectedIndex,
    productBottomLeftSelectedIndex,
    typeAllList,
    showSpecModal,
    showMoreCategoriesModal,
    selectInfo,
    cartCountMap,
    cartItemIdMap,
    currentPageCartItems,
    showAddSuccess,
    commodityList,
    bbList,
    infos,
    
    // 计算属性
    showQuickCheckout,
    quickCheckoutCount,
    quickCheckoutTotal,
    
    // 方法
    handleTabChange,
    handleSecondaryTabChange,
    handleCategorySelect,
    handleSubCategorySelect,
    showMoreCategories,
    handleMoreCategoriesClose,
    handleMoreCategoryConfirm,
    handleSpecSelect,
    handleSpecModalClose,
    getCartCount,
    updateCurrentPageCartItem,
    showSuccessToast,
    syncCartState,
    resetPageState
  }
}
