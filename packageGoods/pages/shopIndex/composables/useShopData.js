import { ref, computed } from 'vue'

/**
 * 店铺数据管理 Composable
 * @param {string} shopId - 店铺ID
 */
export function useShopData(shopId) {
  // 响应式状态
  const shopInfo = ref({})
  const loading = ref(false)
  const error = ref(null)
  const isAttention = ref(false)

  // 计算属性
  const propagandaImages = computed(() => {
    if (shopInfo.value?.storePropagandaImages) {
      return shopInfo.value.storePropagandaImages.split(',')
    }
    return []
  })

  const shopStats = computed(() => ({
    totalPerformance: shopInfo.value.totalPerformance || 0,
    goodsCount: shopInfo.value.goodsCount || 0,
    salesVolume: shopInfo.value.salesVolume || 0
  }))

  const isEnterpriseStore = computed(() => {
    return String(shopInfo.value.storeStraight) === '1'
  })

  // 获取店铺信息
  const fetchShopInfo = async () => {
    try {
      loading.value = true
      error.value = null

      const { data } = await uni.http.post(uni.api.storeManageIndex, {
        sysUserId: shopId
      })

      if (data.code === 200) {
        shopInfo.value = data.result || {}
        isAttention.value = data.result?.isAttention === 1
        return data.result
      } else {
        throw new Error(data.message || '获取店铺信息失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('获取店铺信息失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 关注/取消关注店铺
  const toggleAttention = async () => {
    try {
      loading.value = true
      error.value = null

      const { data } = await uni.http.post(uni.api.attentionStore, {
        sysUserId: shopId,
        isAttention: isAttention.value ? 0 : 1
      })

      if (data.code === 200) {
        isAttention.value = !isAttention.value
        
        // 显示成功提示
        uni.showToast({
          title: isAttention.value ? '关注成功' : '取消关注成功',
          icon: 'success',
          duration: 1500
        })

        return isAttention.value
      } else {
        throw new Error(data.message || '操作失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('关注操作失败:', err)
      
      uni.showToast({
        title: err.message || '操作失败',
        icon: 'none',
        duration: 2000
      })
      
      throw err
    } finally {
      loading.value = false
    }
  }

  // 查看店铺证件（企业店铺）
  const viewStoreCredentials = () => {
    if (!isEnterpriseStore.value) {
      uni.showToast({
        title: '该店铺不是企业店铺',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 跳转到店铺证件页面
    uni.navigateTo({
      url: `/packageGoods/pages/storeCredentials/storeCredentials?sysUserId=${shopId}`
    })
  }

  // 刷新店铺信息
  const refreshShopInfo = async () => {
    return await fetchShopInfo()
  }

  // 重置状态
  const resetState = () => {
    shopInfo.value = {}
    loading.value = false
    error.value = null
    isAttention.value = false
  }

  return {
    // 状态
    shopInfo: computed(() => shopInfo.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),
    isAttention: computed(() => isAttention.value),

    // 计算属性
    propagandaImages,
    shopStats,
    isEnterpriseStore,

    // 方法
    fetchShopInfo,
    toggleAttention,
    viewStoreCredentials,
    refreshShopInfo,
    resetState
  }
}
