import { ref, computed, reactive } from 'vue'

/**
 * 购物车管理 Composable
 */
export function useCartManagement() {
  // 响应式状态
  const cartCounts = ref(new Map()) // goodId -> quantity
  const quickCheckoutItems = ref(new Map()) // goodId -> item info
  const loading = ref(false)

  // 计算属性
  const totalCount = computed(() => {
    return Array.from(cartCounts.value.values()).reduce((sum, count) => sum + count, 0)
  })

  const totalAmount = computed(() => {
    return Array.from(quickCheckoutItems.value.values())
      .reduce((sum, item) => sum + (item.quantity * parseFloat(item.price || 0)), 0)
      .toFixed(2)
  })

  const cartCountMap = computed(() => {
    const map = {}
    cartCounts.value.forEach((count, goodId) => {
      map[goodId] = count
    })
    return map
  })

  // 获取商品的购物车数量
  const getCartCount = (goodId) => {
    return cartCounts.value.get(goodId) || 0
  }

  // 添加商品到购物车
  const addToCart = async (goods, quantity = 1, specification = '无') => {
    try {
      loading.value = true

      const { data } = await uni.http.post(uni.api.addGoodToShoppingCart, {
        goodId: goods.id,
        specification,
        isPlatform: 0,
        quantity
      })

      if (data.code === 200) {
        // 更新本地购物车状态
        const currentCount = getCartCount(goods.id)
        const newCount = currentCount + quantity
        cartCounts.value.set(goods.id, newCount)

        // 更新快捷结算商品
        updateQuickCheckoutItem(goods, newCount)

        // 显示成功提示
        uni.showToast({
          title: '添加成功',
          icon: 'success',
          duration: 1500
        })

        return data.result
      } else {
        throw new Error(data.message || '添加购物车失败')
      }
    } catch (error) {
      console.error('添加购物车失败:', error)
      uni.showToast({
        title: error.message || '添加失败',
        icon: 'none',
        duration: 2000
      })
      throw error
    } finally {
      loading.value = false
    }
  }

  // 更新购物车商品数量
  const updateCartQuantity = async (goods, delta) => {
    const currentCount = getCartCount(goods.id)
    const newCount = Math.max(0, currentCount + delta)

    if (newCount === 0) {
      // 移除商品
      await removeFromCart(goods.id)
    } else if (delta > 0) {
      // 增加数量
      await addToCart(goods, delta)
    } else {
      // 减少数量
      await decreaseCartQuantity(goods, Math.abs(delta))
    }
  }

  // 减少购物车商品数量
  const decreaseCartQuantity = async (goods, quantity = 1) => {
    try {
      loading.value = true

      const { data } = await uni.http.post(uni.api.reduceGoodFromShoppingCart, {
        goodId: goods.id,
        quantity
      })

      if (data.code === 200) {
        const currentCount = getCartCount(goods.id)
        const newCount = Math.max(0, currentCount - quantity)

        if (newCount === 0) {
          cartCounts.value.delete(goods.id)
          quickCheckoutItems.value.delete(goods.id)
        } else {
          cartCounts.value.set(goods.id, newCount)
          updateQuickCheckoutItem(goods, newCount)
        }
      } else {
        throw new Error(data.message || '更新购物车失败')
      }
    } catch (error) {
      console.error('减少购物车数量失败:', error)
      uni.showToast({
        title: error.message || '操作失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      loading.value = false
    }
  }

  // 从购物车移除商品
  const removeFromCart = async (goodId) => {
    try {
      loading.value = true

      const { data } = await uni.http.post(uni.api.removeGoodFromShoppingCart, {
        goodId
      })

      if (data.code === 200) {
        cartCounts.value.delete(goodId)
        quickCheckoutItems.value.delete(goodId)
      } else {
        throw new Error(data.message || '移除商品失败')
      }
    } catch (error) {
      console.error('移除商品失败:', error)
      uni.showToast({
        title: error.message || '操作失败',
        icon: 'none',
        duration: 2000
      })
    } finally {
      loading.value = false
    }
  }

  // 更新快捷结算商品信息
  const updateQuickCheckoutItem = (goods, quantity) => {
    if (quantity > 0) {
      quickCheckoutItems.value.set(goods.id, {
        id: goods.id,
        name: goods.goodName,
        price: goods.price || goods.smallPrice || goods.salePrice || '0',
        quantity,
        image: goods.mainPicture,
        specification: '无'
      })
    } else {
      quickCheckoutItems.value.delete(goods.id)
    }
  }

  // 同步购物车状态
  const syncCartState = async () => {
    try {
      loading.value = true

      const { data } = await uni.http.post(uni.api.getShoppingCartList)

      if (data.code === 200) {
        const cartItems = data.result || []

        // 清空当前状态
        cartCounts.value.clear()
        quickCheckoutItems.value.clear()

        // 重建状态
        cartItems.forEach(item => {
          cartCounts.value.set(item.goodId, item.quantity)
          quickCheckoutItems.value.set(item.goodId, {
            id: item.goodId,
            name: item.goodName,
            price: item.price,
            quantity: item.quantity,
            image: item.mainPicture,
            specification: item.specification || '无'
          })
        })
      }
    } catch (error) {
      console.error('同步购物车状态失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 清空购物车
  const clearCart = () => {
    cartCounts.value.clear()
    quickCheckoutItems.value.clear()
  }

  // 完整的购物车更新逻辑
  const handleUpdateCart = async (item, delta, cartCountMap, cartItemIdMap, updateCurrentPageCartItem, showSuccessToast) => {
    try {
      const currentCount = cartCountMap.value[item.id] || 0
      const cartItemId = cartItemIdMap.value[item.id]

      if (delta < 0 && currentCount <= 0) return

      const newCount = Math.max(0, currentCount + delta)

      uni.showLoading({ title: delta > 0 ? '添加中...' : '更新中...', mask: true })

      if (delta > 0 && !cartItemId) {
        // 首次添加
        const { data: addResult } = await uni.http.post(uni.api.addGoodToShoppingCart, {
          goodId: item.id,
          specification: '无',
          isPlatform: 0,
          quantity: 1
        })

        if (!addResult?.success) {
          throw new Error(addResult?.message || '添加失败')
        }

        cartItemIdMap.value[item.id] = addResult.result
        cartCountMap.value[item.id] = 1
        updateCurrentPageCartItem(item, 1, addResult.result)

      } else if (cartItemId) {
        // 更新数量
        await uni.http.post(uni.api.updateCarGood, {
          id: cartItemId,
          quantity: newCount
        })

        if (newCount === 0) {
          delete cartItemIdMap.value[item.id]
          delete cartCountMap.value[item.id]
          updateCurrentPageCartItem(item, 0)
        } else {
          cartCountMap.value[item.id] = newCount
          updateCurrentPageCartItem(item, newCount, cartItemId)
        }
      }

      if (delta > 0) showSuccessToast()

    } catch (error) {
      console.error('更新购物车失败:', error)
      uni.showToast({ title: error.message || '操作失败', icon: 'none' })
    } finally {
      uni.hideLoading()
    }
  }

  return {
    // 状态
    cartCounts: computed(() => cartCounts.value),
    quickCheckoutItems: computed(() => quickCheckoutItems.value),
    loading: computed(() => loading.value),

    // 计算属性
    totalCount,
    totalAmount,
    cartCountMap,

    // 方法
    getCartCount,
    addToCart,
    updateCartQuantity,
    removeFromCart,
    syncCartState,
    clearCart,
    handleUpdateCart
  }
}
