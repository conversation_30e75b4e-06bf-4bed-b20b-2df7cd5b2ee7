import { ref, computed } from 'vue'

/**
 * 商品操作管理 Composable
 * @param {string} shopId - 店铺ID
 */
export function useGoodsOperations(shopId) {
  // 响应式状态
  const newGoodsList = ref([])
  const allGoodsList = ref([])
  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const hasNewGoods = computed(() => {
    return newGoodsList.value.length > 0
  })

  const hasAllGoods = computed(() => {
    return allGoodsList.value.length > 0
  })

  // 获取新品列表
  const fetchNewGoods = async () => {
    try {
      loading.value = true
      error.value = null

      const { data } = await uni.http.post(uni.api.storeManageNewGoodList, {
        sysUserId: shopId
      })

      if (data.code === 200) {
        newGoodsList.value = data.result || []
        return data.result
      } else {
        throw new Error(data.message || '获取新品列表失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('获取新品列表失败:', err)
      newGoodsList.value = []
      throw err
    } finally {
      loading.value = false
    }
  }

  // 获取全部商品列表（宝贝页面）
  const fetchAllGoods = async (sortType = 'up', sortField = 'salesVolume') => {
    try {
      loading.value = true
      error.value = null

      const params = {
        sysUserId: shopId,
        sortType,
        sortField
      }

      const { data } = await uni.http.post(uni.api.storeManageAllGoodList, params)

      if (data.code === 200) {
        allGoodsList.value = data.result || []
        return data.result
      } else {
        throw new Error(data.message || '获取商品列表失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('获取商品列表失败:', err)
      allGoodsList.value = []
      throw err
    } finally {
      loading.value = false
    }
  }

  // 跳转到商品详情页
  const navigateToGoodsDetail = (goods) => {
    if (!goods || !goods.id) {
      uni.showToast({
        title: '商品信息错误',
        icon: 'none',
        duration: 2000
      })
      return
    }

    uni.navigateTo({
      url: `/packageGoods/pages/goodDetail/goodDetail?goodId=${goods.id}&sysUserId=${shopId}`
    })
  }

  // 商品搜索
  const searchGoods = async (keyword) => {
    if (!keyword || keyword.trim() === '') {
      return []
    }

    try {
      loading.value = true
      error.value = null

      const { data } = await uni.http.post(uni.api.searchStoreGoods, {
        sysUserId: shopId,
        keyword: keyword.trim()
      })

      if (data.code === 200) {
        return data.result || []
      } else {
        throw new Error(data.message || '搜索失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('商品搜索失败:', err)
      
      uni.showToast({
        title: err.message || '搜索失败',
        icon: 'none',
        duration: 2000
      })
      
      return []
    } finally {
      loading.value = false
    }
  }

  // 获取商品详细信息
  const fetchGoodsDetail = async (goodId) => {
    try {
      loading.value = true
      error.value = null

      const { data } = await uni.http.post(uni.api.goodDetail, {
        goodId,
        sysUserId: shopId
      })

      if (data.code === 200) {
        return data.result
      } else {
        throw new Error(data.message || '获取商品详情失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('获取商品详情失败:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 检查商品库存
  const checkGoodsStock = (goods) => {
    const stock = goods.stock || 0
    return {
      hasStock: stock > 0,
      isLowStock: stock > 0 && stock < 10,
      stockCount: stock
    }
  }

  // 格式化商品价格
  const formatGoodsPrice = (goods) => {
    const price = goods.price || goods.smallPrice || goods.salePrice || '0'
    return parseFloat(price).toFixed(2)
  }

  // 获取商品主图
  const getGoodsMainImage = (goods) => {
    if (!goods.mainPicture) return ''
    
    // 解析图片URL
    const parseImgurl = (imgStr) => {
      if (!imgStr) return []
      try {
        return imgStr.split(',').filter(url => url.trim())
      } catch {
        return []
      }
    }

    const imageUrl = parseImgurl(goods.mainPicture)?.[0]
    return imageUrl ? uni.env.IMAGE_URL + imageUrl : ''
  }

  // 刷新商品列表
  const refreshGoodsList = async (type = 'all') => {
    switch (type) {
      case 'new':
        return await fetchNewGoods()
      case 'all':
        return await fetchAllGoods()
      default:
        return []
    }
  }

  // 重置状态
  const resetState = () => {
    newGoodsList.value = []
    allGoodsList.value = []
    loading.value = false
    error.value = null
  }

  return {
    // 状态
    newGoodsList: computed(() => newGoodsList.value),
    allGoodsList: computed(() => allGoodsList.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // 计算属性
    hasNewGoods,
    hasAllGoods,

    // 方法
    fetchNewGoods,
    fetchAllGoods,
    navigateToGoodsDetail,
    searchGoods,
    fetchGoodsDetail,
    checkGoodsStock,
    formatGoodsPrice,
    getGoodsMainImage,
    refreshGoodsList,
    resetState
  }
}
