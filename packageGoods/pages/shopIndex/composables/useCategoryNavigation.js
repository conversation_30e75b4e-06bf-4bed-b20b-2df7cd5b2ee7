import { ref, computed, watch } from 'vue'

/**
 * 分类导航管理 Composable
 * @param {string} shopId - 店铺ID
 */
export function useCategoryNavigation(shopId) {
  // 响应式状态
  const categories = ref([])
  const activeCategoryIndex = ref(0)
  const activeSubCategoryIndex = ref(0)
  const goodsList = ref([])
  const loading = ref(false)
  const error = ref(null)

  // 计算属性
  const currentCategory = computed(() => {
    return categories.value[activeCategoryIndex.value] || {}
  })

  const currentSubCategories = computed(() => {
    return currentCategory.value.chlidGoodType || []
  })

  const hasSubCategories = computed(() => {
    return currentSubCategories.value.length > 0
  })

  const currentSubCategory = computed(() => {
    return currentSubCategories.value[activeSubCategoryIndex.value] || {}
  })

  // 获取分类数据
  const fetchCategories = async () => {
    try {
      loading.value = true
      error.value = null

      const { data } = await uni.http.post(uni.api.storeManageGoodType, {
        sysUserId: shopId
      })

      if (data.code === 200) {
        categories.value = data.result || []
        // 如果有分类，默认选择第一个
        if (categories.value.length > 0) {
          activeCategoryIndex.value = 0
          activeSubCategoryIndex.value = 0
          await fetchGoodsList()
        }
      } else {
        throw new Error(data.message || '获取分类失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('获取分类失败:', err)
    } finally {
      loading.value = false
    }
  }

  // 获取商品列表
  const fetchGoodsList = async () => {
    try {
      loading.value = true
      error.value = null

      const currentCat = currentCategory.value
      const currentSubCat = currentSubCategory.value

      if (!currentCat.id) {
        goodsList.value = []
        return
      }

      const params = {
        sysUserId: shopId,
        goodTypeId: currentCat.id
      }

      // 如果有二级分类，添加二级分类ID
      if (currentSubCat.id) {
        params.goodTypeChildId = currentSubCat.id
      }

      const { data } = await uni.http.post(uni.api.storeManageGoodList, params)

      if (data.code === 200) {
        goodsList.value = data.result || []
      } else {
        throw new Error(data.message || '获取商品列表失败')
      }
    } catch (err) {
      error.value = err.message || '网络错误'
      console.error('获取商品列表失败:', err)
      goodsList.value = []
    } finally {
      loading.value = false
    }
  }

  // 选择分类
  const selectCategory = async (index) => {
    if (index === activeCategoryIndex.value) return

    activeCategoryIndex.value = index
    activeSubCategoryIndex.value = 0 // 重置二级分类
    await fetchGoodsList()
  }

  // 选择二级分类
  const selectSubCategory = async (index) => {
    if (index === activeSubCategoryIndex.value) return

    activeSubCategoryIndex.value = index
    await fetchGoodsList()
  }

  // 刷新当前分类的商品列表
  const refreshGoodsList = async () => {
    await fetchGoodsList()
  }

  // 监听分类变化
  watch([activeCategoryIndex, activeSubCategoryIndex], () => {
    // 可以在这里添加额外的逻辑，比如埋点统计
    console.log('分类切换:', {
      categoryIndex: activeCategoryIndex.value,
      subCategoryIndex: activeSubCategoryIndex.value,
      categoryName: currentCategory.value.name,
      subCategoryName: currentSubCategory.value.name
    })
  })

  return {
    // 状态
    categories: computed(() => categories.value),
    activeCategoryIndex,
    activeSubCategoryIndex,
    goodsList: computed(() => goodsList.value),
    loading: computed(() => loading.value),
    error: computed(() => error.value),

    // 计算属性
    currentCategory,
    currentSubCategories,
    hasSubCategories,
    currentSubCategory,

    // 方法
    fetchCategories,
    fetchGoodsList,
    selectCategory,
    selectSubCategory,
    refreshGoodsList
  }
}
