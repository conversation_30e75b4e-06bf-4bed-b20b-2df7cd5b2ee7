<template>
  <view class="cart-quantity-control">
    <view class="goods-cart-control">
      <!-- 减少按钮 -->
      <view
        class="cart-btn minus"
        :class="{ 'disabled': quantity <= 0 }"
        @click.stop="handleDecrease">
        <text>-</text>
      </view>

      <!-- 数量输入框 -->
      <input
        class="cart-count"
        type="number"
        :value="quantity"
        @input="handleQuantityInput"
        @blur="validateQuantity"
        @click.stop
        :disabled="maxStock <= 0" />

      <!-- 添加按钮 -->
      <view
        class="cart-btn plus"
        :class="{ 'disabled': quantity >= maxStock }"
        @click.stop="handleIncrease">
        <text>+</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  goods: {
    type: Object,
    required: true
  },
  quantity: {
    type: Number,
    default: 0
  }
})

// Events定义
const emit = defineEmits(['quantity-change'])

// 计算属性
const maxStock = computed(() => {
  return props.goods.stock || 0
})

// 事件处理
const handleDecrease = () => {
  if (props.quantity > 0) {
    emit('quantity-change', -1)
  }
}

const handleIncrease = () => {
  if (props.quantity < maxStock.value) {
    emit('quantity-change', 1)
  } else {
    // 库存不足提示
    uni.showToast({
      title: '库存不足',
      icon: 'none',
      duration: 1500
    })
  }
}

// 处理直接输入数量
const handleQuantityInput = (e) => {
  const inputValue = e.detail.value || e.target.value
  const newQuantity = parseInt(inputValue) || 0

  // 不在输入过程中立即验证，等待blur事件
  if (newQuantity >= 0 && newQuantity <= maxStock.value) {
    const delta = newQuantity - props.quantity
    if (delta !== 0) {
      emit('quantity-change', delta)
    }
  }
}

// 输入框失焦时验证数量
const validateQuantity = (e) => {
  const inputValue = e.detail.value || e.target.value
  let newQuantity = parseInt(inputValue) || 0

  // 确保数量在有效范围内
  if (newQuantity < 0) {
    newQuantity = 0
  } else if (newQuantity > maxStock.value) {
    newQuantity = maxStock.value
    uni.showToast({
      title: `最大库存为${maxStock.value}`,
      icon: 'none',
      duration: 1500
    })
  }

  // 如果数量发生变化，触发更新
  const delta = newQuantity - props.quantity
  if (delta !== 0) {
    emit('quantity-change', delta)
  }
}
</script>

<style lang="scss" scoped>
.cart-quantity-control {
  display: flex;
  align-items: center;

  .goods-cart-control {
    display: flex;
    align-items: center;
    height: 52rpx;
    background: rgba(248, 248, 248, 0.9);
    border-radius: 26rpx;
    padding: 0 6rpx;
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    box-shadow:
      0 2rpx 8rpx rgba(0, 0, 0, 0.04),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10rpx);

    .cart-btn {
      width: 44rpx;
      height: 44rpx;
      border-radius: 22rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        transition: left 0.5s ease;
      }

      &.minus {
        background: linear-gradient(135deg, #ffffff 0%, #f8f8f8 100%);
        border: 1rpx solid rgba(0, 0, 0, 0.08);
        box-shadow:
          0 2rpx 4rpx rgba(0, 0, 0, 0.06),
          inset 0 1rpx 0 rgba(255, 255, 255, 0.9);

        &.disabled {
          opacity: 0.4;
          pointer-events: none;
          background: #f0f0f0;
        }

        &:active:not(.disabled) {
          background: linear-gradient(135deg, #f0f0f0 0%, #e8e8e8 100%);
          transform: scale(0.92);
          box-shadow:
            0 1rpx 2rpx rgba(0, 0, 0, 0.1),
            inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        }

        text {
          color: #555555;
          font-size: 26rpx;
          font-weight: 700;
          line-height: 1;
        }
      }

      &.plus {
        background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
        border: 1rpx solid rgba(34, 163, 255, 0.3);
        box-shadow:
          0 4rpx 12rpx rgba(34, 163, 255, 0.25),
          inset 0 1rpx 0 rgba(255, 255, 255, 0.2);

        &.disabled {
          opacity: 0.4;
          pointer-events: none;
          background: #cccccc;
        }

        &:active:not(.disabled) {
          background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
          transform: scale(0.92);
          box-shadow:
            0 2rpx 6rpx rgba(34, 163, 255, 0.4),
            inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);

          &::before {
            left: 100%;
          }
        }

        text {
          color: #ffffff;
          font-size: 26rpx;
          font-weight: 700;
          line-height: 1;
          text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
        }
      }
    }

    .cart-count {
      min-width: 44rpx;
      width: 44rpx;
      text-align: center;
      font-size: 26rpx;
      color: #333333;
      font-weight: 600;
      margin: 0 4rpx;
      line-height: 1;
      background: transparent;
      border: none;
      outline: none;

      &:disabled {
        opacity: 0.6;
        background: #f5f5f5;
      }

      &:focus {
        background: rgba(34, 163, 255, 0.05);
        border-radius: 4rpx;
      }
    }
  }
}
</style>
