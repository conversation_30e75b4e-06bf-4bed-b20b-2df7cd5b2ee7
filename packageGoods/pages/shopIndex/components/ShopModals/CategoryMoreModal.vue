<template>
  <uni-popup ref="moreCategoriesPop" type="top" @change="handlePopupChange">
    <view class="more-categories-modal">
      <!-- 头部 -->
      <view class="more-categories-header">
        <text class="more-categories-title">选择分类</text>
        <view class="more-categories-close" @click="closeModal">
          <text>✕</text>
        </view>
      </view>

      <!-- 分类网格 -->
      <view class="more-categories-content">
        <view
          v-for="(item, index) in categories"
          :key="index"
          class="more-category-item"
          :class="{ 'more-category-item-active': activeIndex === index }"
          @click="handleCategorySelect(index)">
          <text class="category-name">{{ item.name }}</text>
          <view v-if="activeIndex === index" class="category-check">
            <text>✓</text>
          </view>
        </view>
      </view>

      <!-- 底部确认按钮 -->
      <view class="more-categories-footer">
        <view class="confirm-button" @click="handleConfirm">
          <text>确认选择</text>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, watch } from 'vue'

// Props定义
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  activeIndex: {
    type: Number,
    default: 0
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'close',
  'category-select',
  'confirm'
])

// 响应式状态
const moreCategoriesPop = ref(null)
const selectedIndex = ref(0)

// 监听props变化
watch(() => props.activeIndex, (newVal) => {
  selectedIndex.value = newVal
}, { immediate: true })

watch(() => props.visible, (newVal) => {
  if (newVal) {
    openModal()
  } else {
    closeModal()
  }
})

// 方法
const openModal = () => {
  if (moreCategoriesPop.value) {
    moreCategoriesPop.value.open()
    selectedIndex.value = props.activeIndex
  }
}

const closeModal = () => {
  if (moreCategoriesPop.value) {
    moreCategoriesPop.value.close()
  }
  emit('close')
}

const handlePopupChange = (e) => {
  if (!e.show) {
    emit('close')
  }
}

const handleCategorySelect = (index) => {
  selectedIndex.value = index
  emit('category-select', index)
}

const handleConfirm = () => {
  emit('confirm', selectedIndex.value)
  closeModal()
}

// 暴露方法给父组件
defineExpose({
  openModal,
  closeModal
})
</script>

<style lang="scss" scoped>
.more-categories-modal {
  background: #ffffff;
  border-radius: 0 0 24rpx 24rpx;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

.more-categories-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;

  .more-categories-title {
    font-size: 36rpx;
    color: #333333;
    font-weight: 600;
  }

  .more-categories-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 24rpx;

    text {
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.more-categories-content {
  flex: 1;
  overflow-y: auto;
  padding: 24rpx 32rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;

  .more-category-item {
    position: relative;
    background: #f8f8f8;
    border-radius: 16rpx;
    padding: 24rpx 16rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120rpx;
    transition: all 0.3s ease;
    border: 2rpx solid transparent;

    &.more-category-item-active {
      background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
      border-color: #FF6B35;
      transform: scale(1.05);

      .category-name {
        color: #ffffff;
        font-weight: 600;
      }

      .category-check {
        position: absolute;
        top: 8rpx;
        right: 8rpx;
        width: 32rpx;
        height: 32rpx;
        background: #ffffff;
        border-radius: 16rpx;
        display: flex;
        align-items: center;
        justify-content: center;

        text {
          font-size: 20rpx;
          color: #FF6B35;
          font-weight: 600;
        }
      }
    }

    .category-name {
      font-size: 28rpx;
      color: #333333;
      text-align: center;
      line-height: 1.3;
      transition: color 0.3s ease;
    }

    &:active {
      transform: scale(0.95);
      opacity: 0.8;
    }
  }
}

.more-categories-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 2rpx solid #f0f0f0;

  .confirm-button {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
    border-radius: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 16rpx rgba(34, 163, 255, 0.3);
    transition: all 0.3s ease;

    text {
      font-size: 32rpx;
      color: #ffffff;
      font-weight: 500;
    }

    &:active {
      transform: scale(0.98);
      opacity: 0.9;
    }
  }
}
</style>
