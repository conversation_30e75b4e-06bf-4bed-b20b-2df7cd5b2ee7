<template>
  <uni-popup ref="specificationPop" type="bottom" @change="handlePopupChange">
    <view class="specification-modal">
      <!-- 商品信息头部 -->
      <view class="spec-header">
        <image
          class="spec-goods-image"
          :src="goodsImageUrl"
          mode="aspectFill">
        </image>
        <view class="spec-goods-info">
          <view class="spec-goods-name">{{ goodsInfo.goodName }}</view>
          <view class="spec-goods-price">
            <image src="@/static/index/i_1.png" class="price-icon"></image>
            <text class="price-text">{{ formatPrice(currentPrice) }}</text>
          </view>
          <view class="spec-goods-stock">库存: {{ currentStock }}</view>
          <view v-if="selectedSpecsText" class="spec-selected">
            已选择: {{ selectedSpecsText }}
          </view>
        </view>
        <view class="spec-close" @click="closeModal">
          <text class="close-icon">✕</text>
        </view>
      </view>

      <!-- 规格选择区域 -->
      <view class="spec-content">
        <!-- 自定义规格选择，不使用 specificationWrap 组件 -->
        <view v-if="specificationOptions.length > 0" class="spec-options">
          <view
            v-for="(specGroup, groupIndex) in specificationOptions"
            :key="groupIndex"
            class="spec-group"
          >
            <view class="spec-group-title">{{ specGroup.name }}</view>
            <view class="spec-group-options">
              <view
                v-for="(option, optionIndex) in specGroup.options"
                :key="optionIndex"
                class="spec-option"
                :class="{
                  'spec-option-active': isSpecSelected(groupIndex, optionIndex),
                  'spec-option-disabled': !option.available
                }"
                @click="selectSpec(groupIndex, optionIndex)"
              >
                <view class="spec-option-content">
                  <text class="spec-option-text">{{ option.name }}</text>
                  <view v-if="option.price" class="spec-option-price">
                    <image src="@/static/index/i_1.png" class="spec-price-icon"></image>
                    <text class="spec-price-text">{{ option.price }}</text>
                  </view>
                  <view v-if="option.stock !== undefined" class="spec-option-stock">
                    库存: {{ option.stock }}
                  </view>
                </view>
                <view v-if="!option.available" class="spec-option-mask">
                  <text class="mask-text">缺货</text>
                </view>
              </view>
            </view>
          </view>
        </view>

        <!-- 无规格商品提示 -->
        <view v-else class="no-spec-tip">
          <text class="tip-text">该商品无需选择规格</text>
        </view>
      </view>

      <!-- 底部操作按钮 -->
      <view class="spec-footer">
        <view class="spec-quantity-control">
          <text class="quantity-label">数量</text>
          <view class="quantity-controls">
            <view
              class="quantity-btn minus-btn"
              :class="{ disabled: quantity <= 1 }"
              @click="decreaseQuantity">
              <text class="btn-text">-</text>
            </view>
            <input
              class="quantity-input"
              type="number"
              v-model="quantity"
              @input="handleQuantityInput"
              @blur="validateQuantity"
              :disabled="loading">
            <view
              class="quantity-btn plus-btn"
              :class="{ disabled: quantity >= currentStock || loading }"
              @click="increaseQuantity">
              <text class="btn-text">+</text>
            </view>
          </view>
        </view>

        <view class="spec-buttons">
          <view
            class="spec-button add-to-cart"
            :class="{ disabled: loading || !canAddToCart }"
            @click="handleAddToCart">
            <text class="button-text">{{ loading ? '添加中...' : '加入购物车' }}</text>
          </view>
          <view
            class="spec-button buy-now"
            :class="{ disabled: loading || !canAddToCart }"
            @click="handleBuyNow">
            <text class="button-text">{{ loading ? '处理中...' : '立即购买' }}</text>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { parseImgurl } from '@/utils'

// Props定义
const props = defineProps({
  goodsInfo: {
    type: Object,
    default: () => ({})
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'close',
  'add-to-cart',
  'buy-now',
  'specification-change'
])

// 响应式状态
const specificationPop = ref(null)
const quantity = ref(1)
const loading = ref(false)
const selectedSpecs = ref([]) // 选中的规格索引数组 [groupIndex, optionIndex]
const currentSpecification = ref({}) // 当前选中的规格详情

// 计算属性
const goodsImageUrl = computed(() => {
  if (!props.goodsInfo.mainPicture) return ''
  const imageUrl = parseImgurl(props.goodsInfo.mainPicture)?.[0]
  return imageUrl ? uni.env.IMAGE_URL + imageUrl : ''
})

// 解析规格选项
const specificationOptions = computed(() => {
  if (!props.goodsInfo.specification || props.goodsInfo.specification === '无') {
    return []
  }

  try {
    const specData = JSON.parse(props.goodsInfo.specification)
    return specData.map(group => ({
      name: group.CommodityStyle,
      options: group.classification.map(item => ({
        name: item.value,
        price: getSpecPrice(item.value),
        stock: getSpecStock(item.value),
        available: getSpecStock(item.value) > 0
      }))
    }))
  } catch (error) {
    console.error('解析规格数据失败:', error)
    return []
  }
})

// 当前选中的规格文本
const selectedSpecsText = computed(() => {
  if (selectedSpecs.value.length === 0) return ''

  return selectedSpecs.value.map((specIndex, groupIndex) => {
    const group = specificationOptions.value[groupIndex]
    if (group && group.options[specIndex]) {
      return group.options[specIndex].name
    }
    return ''
  }).filter(Boolean).join(', ')
})

const currentPrice = computed(() => {
  // 如果有选中的规格价格，使用规格价格，否则使用商品基础价格
  return currentSpecification.value.price ||
         props.goodsInfo.price ||
         props.goodsInfo.smallPrice ||
         props.goodsInfo.salePrice ||
         '0'
})

const currentStock = computed(() => {
  // 如果有选中的规格库存，使用规格库存，否则使用商品基础库存
  return currentSpecification.value.stock ||
         currentSpecification.value.repertory ||
         props.goodsInfo.stock ||
         props.goodsInfo.repertory ||
         0
})

const canAddToCart = computed(() => {
  return currentStock.value > 0 && quantity.value > 0 && quantity.value <= currentStock.value
})

// 格式化价格显示
const formatPrice = (price) => {
  if (!price) return '0.00'
  return parseFloat(price).toFixed(2)
}

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    openModal()
  } else {
    closeModal()
  }
})

// 方法
const openModal = () => {
  if (specificationPop.value) {
    specificationPop.value.open()
    // 重置状态
    quantity.value = 1
    currentSpecification.value = {}
  }
}

const closeModal = () => {
  if (specificationPop.value) {
    specificationPop.value.close()
  }
  emit('close')
}

const handlePopupChange = (e) => {
  if (!e.show) {
    emit('close')
  }
}

// 规格选择相关方法
const isSpecSelected = (groupIndex, optionIndex) => {
  return selectedSpecs.value[groupIndex] === optionIndex
}

const selectSpec = (groupIndex, optionIndex) => {
  const option = specificationOptions.value[groupIndex]?.options[optionIndex]
  if (!option || !option.available) return

  // 更新选中的规格
  selectedSpecs.value[groupIndex] = optionIndex

  // 更新当前规格信息
  updateCurrentSpecification()
}

const updateCurrentSpecification = () => {
  if (selectedSpecs.value.length === 0 || !props.goodsInfo.specifications) {
    currentSpecification.value = {}
    return
  }

  // 构建规格字符串
  const specString = selectedSpecs.value.map((specIndex, groupIndex) => {
    const group = specificationOptions.value[groupIndex]
    if (group && group.options[specIndex]) {
      return group.options[specIndex].name
    }
    return ''
  }).filter(Boolean).join(',')

  // 查找匹配的规格详情
  const matchedSpec = props.goodsInfo.specifications?.find(spec =>
    spec.specification === specString
  )

  if (matchedSpec) {
    currentSpecification.value = matchedSpec
    emit('specification-change', matchedSpec)
  }
}

// 获取规格价格的辅助函数
const getSpecPrice = (specName) => {
  if (!props.goodsInfo.specifications?.length) return null

  const matchingSpecs = props.goodsInfo.specifications.filter(spec =>
    spec.specification && spec.specification.includes(specName)
  )

  if (matchingSpecs.length === 0) return null
  if (matchingSpecs.length === 1) return matchingSpecs[0].price

  // 多个匹配时显示价格范围
  const prices = matchingSpecs.map(spec => parseFloat(spec.price)).sort((a, b) => a - b)
  const minPrice = prices[0]
  const maxPrice = prices[prices.length - 1]

  return minPrice === maxPrice ? minPrice.toFixed(2) : `${minPrice.toFixed(2)}-${maxPrice.toFixed(2)}`
}

// 获取规格库存的辅助函数
const getSpecStock = (specName) => {
  if (!props.goodsInfo.specifications?.length) return 0

  const matchingSpecs = props.goodsInfo.specifications.filter(spec =>
    spec.specification && spec.specification.includes(specName)
  )

  if (matchingSpecs.length === 0) return 0

  return matchingSpecs.reduce((total, spec) => {
    const stock = parseInt(spec.repertory) || 0
    return total + stock
  }, 0)
}

// 获取规格字符串 - 后端接口需要的格式
const getSpecificationString = () => {
  if (selectedSpecs.value.length === 0 || specificationOptions.value.length === 0) {
    return '无'
  }

  // 构建规格字符串，格式：颜色,尺寸 (例如：红色,L)
  const specString = selectedSpecs.value.map((specIndex, groupIndex) => {
    const group = specificationOptions.value[groupIndex]
    if (group && group.options[specIndex]) {
      return group.options[specIndex].name
    }
    return ''
  }).filter(Boolean).join(',')

  return specString || '无'
}

// 数量验证
const validateQuantity = () => {
  if (quantity.value < 1) {
    quantity.value = 1
  } else if (quantity.value > currentStock.value) {
    quantity.value = currentStock.value
  }
}

const handleQuantityInput = (e) => {
  const value = parseInt(e.detail.value) || 1
  quantity.value = Math.max(1, Math.min(value, currentStock.value))
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const increaseQuantity = () => {
  if (quantity.value < currentStock.value && !loading.value) {
    quantity.value++
  }
}

const handleAddToCart = async () => {
  if (!canAddToCart.value || loading.value) return

  try {
    loading.value = true

    // 构建规格字符串 - 后端需要的是字符串格式，不是对象
    const specificationString = getSpecificationString()

    const cartData = {
      goods: props.goodsInfo,
      quantity: quantity.value,
      specification: specificationString,
      specificationId: currentSpecification.value.id || '',
      price: currentPrice.value
    }

    emit('add-to-cart', cartData)

    // 添加成功后关闭弹窗
    setTimeout(() => {
      closeModal()
    }, 500)

  } catch (error) {
    console.error('添加购物车失败:', error)
  } finally {
    loading.value = false
  }
}

const handleBuyNow = async () => {
  if (!canAddToCart.value || loading.value) return

  try {
    loading.value = true

    // 构建规格字符串 - 后端需要的是字符串格式，不是对象
    const specificationString = getSpecificationString()

    const orderData = {
      goods: props.goodsInfo,
      quantity: quantity.value,
      specification: specificationString,
      specificationId: currentSpecification.value.id || '',
      price: currentPrice.value
    }

    emit('buy-now', orderData)

  } catch (error) {
    console.error('立即购买失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  openModal,
  closeModal
})
</script>

<style lang="scss" scoped>
.specification-modal {
  background: #ffffff;
  border-radius: 32rpx 32rpx 0 0;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.spec-header {
  display: flex;
  align-items: flex-start;
  padding: 40rpx 32rpx 32rpx;
  border-bottom: 2rpx solid #f8f8f8;
  position: relative;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);

  .spec-goods-image {
    width: 140rpx;
    height: 140rpx;
    border-radius: 16rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
    border: 2rpx solid #f0f0f0;
  }

  .spec-goods-info {
    flex: 1;
    padding-top: 8rpx;

    .spec-goods-name {
      font-size: 34rpx;
      color: #1a1a1a;
      font-weight: 600;
      line-height: 1.4;
      margin-bottom: 20rpx;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .spec-goods-price {
      display: flex;
      align-items: center;
      margin-bottom: 16rpx;

      .price-icon {
        width: 32rpx;
        height: 32rpx;
        margin-right: 8rpx;
      }

      .price-text {
        font-size: 40rpx;
        color: #FF6B35;
        font-weight: 700;
        text-shadow: 0 2rpx 4rpx rgba(255, 107, 53, 0.2);
      }
    }

    .spec-goods-stock {
      font-size: 26rpx;
      color: #666666;
      margin-bottom: 12rpx;
    }

    .spec-selected {
      font-size: 24rpx;
      color: #22A3FF;
      background: rgba(34, 163, 255, 0.1);
      padding: 8rpx 16rpx;
      border-radius: 20rpx;
      display: inline-block;
      border: 1rpx solid rgba(34, 163, 255, 0.2);
    }
  }

  .spec-close {
    width: 56rpx;
    height: 56rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(0, 0, 0, 0.05);
    border-radius: 28rpx;
    transition: all 0.3s ease;

    &:active {
      transform: scale(0.9);
      background: rgba(0, 0, 0, 0.1);
    }

    .close-icon {
      font-size: 32rpx;
      color: #666666;
      font-weight: 600;
    }
  }
}

.spec-content {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx;
  background: #fafafa;

  .spec-options {
    .spec-group {
      margin-bottom: 40rpx;

      .spec-group-title {
        font-size: 30rpx;
        color: #333333;
        font-weight: 600;
        margin-bottom: 24rpx;
        padding-left: 8rpx;
        position: relative;

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 50%;
          transform: translateY(-50%);
          width: 4rpx;
          height: 20rpx;
          background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
          border-radius: 2rpx;
        }
      }

      .spec-group-options {
        display: flex;
        flex-wrap: wrap;
        gap: 16rpx;

        .spec-option {
          position: relative;
          background: #ffffff;
          border: 2rpx solid #e8e8e8;
          border-radius: 16rpx;
          padding: 20rpx 24rpx;
          min-width: 120rpx;
          transition: all 0.3s ease;
          cursor: pointer;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

          .spec-option-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8rpx;

            .spec-option-text {
              font-size: 28rpx;
              color: #333333;
              font-weight: 500;
              text-align: center;
            }

            .spec-option-price {
              display: flex;
              align-items: center;
              gap: 4rpx;

              .spec-price-icon {
                width: 20rpx;
                height: 20rpx;
              }

              .spec-price-text {
                font-size: 22rpx;
                color: #FF6B35;
                font-weight: 600;
              }
            }

            .spec-option-stock {
              font-size: 20rpx;
              color: #999999;
            }
          }

          &.spec-option-active {
            border-color: #22A3FF;
            background: linear-gradient(135deg, rgba(34, 163, 255, 0.1) 0%, rgba(79, 195, 247, 0.1) 100%);
            box-shadow: 0 4rpx 16rpx rgba(34, 163, 255, 0.2);

            .spec-option-text {
              color: #22A3FF;
              font-weight: 600;
            }
          }

          &.spec-option-disabled {
            opacity: 0.5;
            pointer-events: none;
            background: #f5f5f5;

            .spec-option-mask {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background: rgba(255, 255, 255, 0.8);
              border-radius: 16rpx;
              display: flex;
              align-items: center;
              justify-content: center;

              .mask-text {
                font-size: 24rpx;
                color: #ff4757;
                font-weight: 600;
              }
            }
          }

          &:active:not(.spec-option-disabled) {
            transform: scale(0.95);
          }
        }
      }
    }
  }

  .no-spec-tip {
    text-align: center;
    padding: 80rpx 0;
    color: #999999;

    .tip-text {
      font-size: 28rpx;
    }
  }
}

.spec-footer {
  padding: 32rpx;
  border-top: 2rpx solid #f8f8f8;
  background: #ffffff;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);

  .spec-quantity-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 32rpx;
    padding: 24rpx;
    background: #fafafa;
    border-radius: 20rpx;

    .quantity-label {
      font-size: 30rpx;
      color: #333333;
      font-weight: 600;
    }

    .quantity-controls {
      display: flex;
      align-items: center;
      background: #ffffff;
      border-radius: 28rpx;
      padding: 4rpx;
      border: 2rpx solid #e8e8e8;
      box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);

      .quantity-btn {
        width: 56rpx;
        height: 56rpx;
        border-radius: 28rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        transition: all 0.3s ease;

        &.minus-btn {
          border: 2rpx solid #e8e8e8;

          &:active:not(.disabled) {
            background: #f0f0f0;
            border-color: #d0d0d0;
          }
        }

        &.plus-btn {
          border: 2rpx solid #22A3FF;
          background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);

          .btn-text {
            color: #ffffff !important;
          }

          &:active:not(.disabled) {
            background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
          }
        }

        &.disabled {
          opacity: 0.4;
          pointer-events: none;
        }

        .btn-text {
          font-size: 32rpx;
          color: #333333;
          font-weight: 700;
        }
      }

      .quantity-input {
        width: 100rpx;
        text-align: center;
        font-size: 32rpx;
        color: #333333;
        font-weight: 600;
        background: transparent;
        border: none;
        outline: none;
        margin: 0 8rpx;
      }
    }
  }

  .spec-buttons {
    display: flex;
    gap: 20rpx;

    .spec-button {
      flex: 1;
      height: 96rpx;
      border-radius: 48rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 34rpx;
      font-weight: 600;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;

      &.add-to-cart {
        background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
        color: #ffffff;
        box-shadow: 0 8rpx 24rpx rgba(255, 107, 53, 0.4);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:active:not(.disabled)::before {
          left: 100%;
        }
      }

      &.buy-now {
        background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
        color: #ffffff;
        box-shadow: 0 8rpx 24rpx rgba(34, 163, 255, 0.4);

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:active:not(.disabled)::before {
          left: 100%;
        }
      }

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
        box-shadow: none;
      }

      &:active:not(.disabled) {
        transform: scale(0.96);
      }

      .button-text {
        position: relative;
        z-index: 1;
      }
    }
  }
}
</style>
