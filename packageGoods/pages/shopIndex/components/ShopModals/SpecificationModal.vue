<template>
  <uni-popup ref="specificationPop" type="bottom" @change="handlePopupChange">
    <view class="specification-modal">
      <!-- 商品信息头部 -->
      <view class="spec-header">
        <image 
          class="spec-goods-image" 
          :src="goodsImageUrl" 
          mode="aspectFill">
        </image>
        <view class="spec-goods-info">
          <view class="spec-goods-name">{{ goodsInfo.goodName }}</view>
          <view class="spec-goods-price">
            <image src="@/static/index/i_1.png" class="price-icon"></image>
            <text class="price-text">{{ currentPrice }}</text>
          </view>
          <view class="spec-goods-stock">库存: {{ currentStock }}</view>
        </view>
        <view class="spec-close" @click="closeModal">
          <text>✕</text>
        </view>
      </view>

      <!-- 规格选择区域 -->
      <view class="spec-content">
        <specificationWrap
          ref="specificationWrapRef"
          :info="goodsInfo"
          :hasRequest="false"
          @specification-change="handleSpecificationChange">
        </specificationWrap>
      </view>

      <!-- 底部操作按钮 -->
      <view class="spec-footer">
        <view class="spec-quantity-control">
          <text class="quantity-label">数量:</text>
          <view class="quantity-controls">
            <view 
              class="quantity-btn minus"
              :class="{ disabled: quantity <= 1 }"
              @click="decreaseQuantity">
              <text>-</text>
            </view>
            <input 
              class="quantity-input" 
              type="number" 
              v-model="quantity"
              @input="handleQuantityInput"
              :disabled="loading">
            <view 
              class="quantity-btn plus"
              :class="{ disabled: quantity >= currentStock || loading }"
              @click="increaseQuantity">
              <text>+</text>
            </view>
          </view>
        </view>

        <view class="spec-buttons">
          <view 
            class="spec-button add-to-cart"
            :class="{ disabled: loading || !canAddToCart }"
            @click="handleAddToCart">
            <text>{{ loading ? '添加中...' : '加入购物车' }}</text>
          </view>
          <view 
            class="spec-button buy-now"
            :class="{ disabled: loading || !canAddToCart }"
            @click="handleBuyNow">
            <text>{{ loading ? '处理中...' : '立即购买' }}</text>
          </view>
        </view>
      </view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import specificationWrap from '@/components/specificationWrap/specificationWrap.vue'
import { parseImgurl } from '@/utils'

// Props定义
const props = defineProps({
  goodsInfo: {
    type: Object,
    default: () => ({})
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'close',
  'add-to-cart',
  'buy-now',
  'specification-change'
])

// 响应式状态
const specificationPop = ref(null)
const specificationWrapRef = ref(null)
const quantity = ref(1)
const loading = ref(false)
const currentSpecification = ref({})

// 计算属性
const goodsImageUrl = computed(() => {
  if (!props.goodsInfo.mainPicture) return ''
  const imageUrl = parseImgurl(props.goodsInfo.mainPicture)?.[0]
  return imageUrl ? uni.env.IMAGE_URL + imageUrl : ''
})

const currentPrice = computed(() => {
  // 如果有选中的规格价格，使用规格价格，否则使用商品基础价格
  return currentSpecification.value.price || 
         props.goodsInfo.price || 
         props.goodsInfo.smallPrice || 
         props.goodsInfo.salePrice || 
         '0'
})

const currentStock = computed(() => {
  // 如果有选中的规格库存，使用规格库存，否则使用商品基础库存
  return currentSpecification.value.stock || props.goodsInfo.stock || 0
})

const canAddToCart = computed(() => {
  return currentStock.value > 0 && quantity.value > 0 && quantity.value <= currentStock.value
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  if (newVal) {
    openModal()
  } else {
    closeModal()
  }
})

// 方法
const openModal = () => {
  if (specificationPop.value) {
    specificationPop.value.open()
    // 重置状态
    quantity.value = 1
    currentSpecification.value = {}
  }
}

const closeModal = () => {
  if (specificationPop.value) {
    specificationPop.value.close()
  }
  emit('close')
}

const handlePopupChange = (e) => {
  if (!e.show) {
    emit('close')
  }
}

const handleSpecificationChange = (specData) => {
  currentSpecification.value = specData
  emit('specification-change', specData)
}

const handleQuantityInput = (e) => {
  const value = parseInt(e.detail.value) || 1
  quantity.value = Math.max(1, Math.min(value, currentStock.value))
}

const decreaseQuantity = () => {
  if (quantity.value > 1) {
    quantity.value--
  }
}

const increaseQuantity = () => {
  if (quantity.value < currentStock.value && !loading.value) {
    quantity.value++
  }
}

const handleAddToCart = async () => {
  if (!canAddToCart.value || loading.value) return

  try {
    loading.value = true
    
    const cartData = {
      goods: props.goodsInfo,
      quantity: quantity.value,
      specification: currentSpecification.value,
      price: currentPrice.value
    }

    emit('add-to-cart', cartData)
    
    // 添加成功后关闭弹窗
    setTimeout(() => {
      closeModal()
    }, 500)

  } catch (error) {
    console.error('添加购物车失败:', error)
  } finally {
    loading.value = false
  }
}

const handleBuyNow = async () => {
  if (!canAddToCart.value || loading.value) return

  try {
    loading.value = true
    
    const orderData = {
      goods: props.goodsInfo,
      quantity: quantity.value,
      specification: currentSpecification.value,
      price: currentPrice.value
    }

    emit('buy-now', orderData)

  } catch (error) {
    console.error('立即购买失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  openModal,
  closeModal
})
</script>

<style lang="scss" scoped>
.specification-modal {
  background: #ffffff;
  border-radius: 24rpx 24rpx 0 0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
}

.spec-header {
  display: flex;
  align-items: flex-start;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
  position: relative;

  .spec-goods-image {
    width: 120rpx;
    height: 120rpx;
    border-radius: 12rpx;
    margin-right: 24rpx;
    flex-shrink: 0;
  }

  .spec-goods-info {
    flex: 1;

    .spec-goods-name {
      font-size: 32rpx;
      color: #333333;
      font-weight: 500;
      line-height: 1.4;
      margin-bottom: 16rpx;
    }

    .spec-goods-price {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;

      .price-icon {
        width: 28rpx;
        height: 28rpx;
        margin-right: 8rpx;
      }

      .price-text {
        font-size: 36rpx;
        color: #FF6B35;
        font-weight: 600;
      }
    }

    .spec-goods-stock {
      font-size: 24rpx;
      color: #666666;
    }
  }

  .spec-close {
    width: 48rpx;
    height: 48rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    border-radius: 24rpx;

    text {
      font-size: 28rpx;
      color: #666666;
    }
  }
}

.spec-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 32rpx;
}

.spec-footer {
  padding: 24rpx 32rpx 32rpx;
  border-top: 2rpx solid #f0f0f0;

  .spec-quantity-control {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 24rpx;

    .quantity-label {
      font-size: 28rpx;
      color: #333333;
      font-weight: 500;
    }

    .quantity-controls {
      display: flex;
      align-items: center;
      background: #f5f5f5;
      border-radius: 24rpx;
      padding: 8rpx;

      .quantity-btn {
        width: 48rpx;
        height: 48rpx;
        border-radius: 24rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        
        &.disabled {
          opacity: 0.5;
          pointer-events: none;
        }

        text {
          font-size: 28rpx;
          color: #333333;
          font-weight: 600;
        }
      }

      .quantity-input {
        width: 80rpx;
        text-align: center;
        font-size: 28rpx;
        color: #333333;
        background: transparent;
        border: none;
        outline: none;
      }
    }
  }

  .spec-buttons {
    display: flex;
    gap: 16rpx;

    .spec-button {
      flex: 1;
      height: 88rpx;
      border-radius: 44rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &.add-to-cart {
        background: linear-gradient(135deg, #FF6B35 0%, #FF8F6B 100%);
        color: #ffffff;
        box-shadow: 0 8rpx 16rpx rgba(255, 107, 53, 0.3);
      }

      &.buy-now {
        background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
        color: #ffffff;
        box-shadow: 0 8rpx 16rpx rgba(34, 163, 255, 0.3);
      }

      &.disabled {
        opacity: 0.5;
        pointer-events: none;
      }

      &:active:not(.disabled) {
        transform: scale(0.98);
        opacity: 0.9;
      }
    }
  }
}
</style>
