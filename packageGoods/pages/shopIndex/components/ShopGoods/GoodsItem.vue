<template>
  <view class="goods-item" @click="handleGoodsClick">
    <!-- 商品图片 -->
    <image
      class="goods-image"
      :src="goodsImageUrl"
      mode="aspectFill"
      @error="handleImageError">
    </image>

    <!-- 商品信息 -->
    <view class="goods-info">
      <view class="goods-title">{{ goods.goodName }}</view>
      <view class="goods-subtitle" v-if="goods.subtitle || goods.description">
        {{ goods.subtitle || goods.description }}
      </view>

      <!-- 销量库存信息 -->
      <GoodsSalesStock
        :sales="goods.salesVolume || goods.sales || 0"
        :stock="goods.stock || 0" />

      <!-- 价格和购物车 -->
      <view class="goods-bottom">
        <GoodsPrice :price="goodsPrice" />

        <!-- 购物车操作区域 -->
        <view class="goods-cart-wrapper">
          <!-- 库存为0时显示缺货 -->
          <view
            v-if="isOutOfStock"
            class="goods-cart out-of-stock">
            <text>缺货</text>
          </view>

          <!-- 有规格的商品显示选规格按钮 -->
          <view
            v-else-if="hasSpecification"
            class="goods-cart spec-select"
            @click.stop="handleSpecSelect">
            <text>选规格</text>
          </view>

          <!-- 无规格商品的购物车控制 -->
          <CartQuantityControl
            v-else
            :goods="goods"
            :quantity="cartQuantity"
            @quantity-change="handleQuantityChange" />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import GoodsPrice from './GoodsPrice.vue'
import GoodsSalesStock from './GoodsSalesStock.vue'
import CartQuantityControl from '../ShopCart/CartQuantityControl.vue'
import { parseImgurl } from '@/utils'

// Props定义
const props = defineProps({
  goods: {
    type: Object,
    required: true
  },
  cartQuantity: {
    type: Number,
    default: 0
  }
})

// Events定义
const emit = defineEmits([
  'goods-click',
  'spec-select',
  'quantity-change'
])

// 计算属性
const goodsImageUrl = computed(() => {
  if (!props.goods.mainPicture) return ''
  const imageUrl = parseImgurl(props.goods.mainPicture)?.[0]
  return imageUrl ? uni.env.IMAGE_URL + imageUrl : ''
})

const goodsPrice = computed(() => {
  return props.goods.price || props.goods.smallPrice || props.goods.salePrice || '0'
})

const isOutOfStock = computed(() => {
  return (props.goods.stock || 0) <= 0
})

const hasSpecification = computed(() => {
  return props.goods.isSpecification === '1'
})

// 事件处理
const handleGoodsClick = () => {
  emit('goods-click', props.goods)
}

const handleSpecSelect = () => {
  emit('spec-select', props.goods)
}

const handleQuantityChange = (delta) => {
  emit('quantity-change', props.goods, delta)
}

const handleImageError = () => {
  console.warn('商品图片加载失败:', props.goods.goodName)
}
</script>

<style lang="scss" scoped>
.goods-item {
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow:
    0 4rpx 16rpx rgba(0, 0, 0, 0.04),
    0 2rpx 8rpx rgba(0, 0, 0, 0.02);
  margin-bottom: 16rpx;
  display: flex;
  align-items: flex-start;
  padding: 20rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1rpx solid rgba(0, 0, 0, 0.02);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1rpx;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  }

  &:active {
    transform: scale(0.98) translateY(2rpx);
    box-shadow:
      0 2rpx 8rpx rgba(0, 0, 0, 0.08),
      0 1rpx 4rpx rgba(0, 0, 0, 0.04);
  }

  .goods-image {
    width: 130rpx;
    height: 130rpx;
    border-radius: 16rpx;
    margin-right: 20rpx;
    flex-shrink: 0;
    background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
    border: 1rpx solid rgba(0, 0, 0, 0.04);
    box-shadow:
      0 2rpx 8rpx rgba(0, 0, 0, 0.04),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.8);
  }

  .goods-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 130rpx;

    .goods-title {
      font-size: 30rpx;
      color: #1a1a1a;
      margin-bottom: 8rpx;
      font-weight: 600;
      line-height: 1.4;
      word-wrap: break-word;
      word-break: break-all;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.02);
    }

    .goods-subtitle {
      font-size: 22rpx;
      color: #888888;
      margin-bottom: 6rpx;
      line-height: 1.3;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 1;
      overflow: hidden;
    }

    .goods-bottom {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 12rpx;

      .goods-cart-wrapper {
        display: flex;
        align-items: center;
        margin-left: 12rpx;
      }
    }
  }
}

.goods-cart {
  &.spec-select {
    background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
    box-shadow:
      0 4rpx 12rpx rgba(34, 163, 255, 0.25),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
    padding: 0 20rpx;
    height: 52rpx;
    border-radius: 26rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1rpx solid rgba(34, 163, 255, 0.3);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s ease;
    }

    text {
      color: #ffffff;
      font-size: 22rpx;
      font-weight: 600;
      text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
      position: relative;
      z-index: 1;
    }

    &:active {
      transform: scale(0.94);
      background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
      box-shadow:
        0 2rpx 6rpx rgba(34, 163, 255, 0.4),
        inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);

      &::before {
        left: 100%;
      }
    }
  }

  &.out-of-stock {
    background: linear-gradient(135deg, #f8f8f8 0%, #f0f0f0 100%);
    border: 1rpx solid rgba(0, 0, 0, 0.08);
    padding: 0 20rpx;
    height: 52rpx;
    border-radius: 26rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    pointer-events: none;
    position: relative;
    overflow: hidden;
    box-shadow: inset 0 1rpx 2rpx rgba(0, 0, 0, 0.05);

    &::after {
      content: "";
      position: absolute;
      top: 50%;
      left: 10%;
      right: 10%;
      height: 1rpx;
      background: linear-gradient(90deg, transparent, #cccccc, transparent);
      transform: translateY(-50%);
      opacity: 0.6;
    }

    text {
      color: #999999;
      font-size: 22rpx;
      font-weight: 500;
      position: relative;
      z-index: 1;
      display: flex;
      align-items: center;

      &::before {
        content: "⚠️";
        margin-right: 6rpx;
        font-size: 18rpx;
        opacity: 0.8;
      }
    }
  }
}
</style>
