<template>
  <view class="goods-price">
    <image src="@/static/index/i_1.png" class="price-icon"></image>
    <text class="price-text">{{ formattedPrice }}</text>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  price: {
    type: [String, Number],
    default: '0'
  },
  currency: {
    type: String,
    default: ''
  }
})

// 计算属性
const formattedPrice = computed(() => {
  const price = parseFloat(props.price) || 0
  return price.toFixed(2)
})
</script>

<style lang="scss" scoped>
.goods-price {
  display: flex;
  align-items: center;

  .price-icon {
    width: 28rpx;
    height: 28rpx;
    margin-right: 8rpx;
  }

  .price-text {
    font-size: 36rpx;
    color: #FF6B35;
    font-weight: 600;
  }
}
</style>
