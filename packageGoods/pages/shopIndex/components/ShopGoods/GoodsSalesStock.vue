<template>
  <view class="goods-sales-stock">
    <text class="goods-sales">已售 {{ sales }}</text>
    <text class="goods-stock" :class="stockClass">
      库存 {{ stock }}
    </text>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  sales: {
    type: [String, Number],
    default: 0
  },
  stock: {
    type: [String, Number],
    default: 0
  }
})

// 计算属性
const stockClass = computed(() => {
  const stockNum = Number(props.stock)
  return {
    'low-stock': stockNum < 10 && stockNum > 0,
    'no-stock': stockNum === 0
  }
})
</script>

<style lang="scss" scoped>
.goods-sales-stock {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12rpx;
  padding: 8rpx 12rpx;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 8rpx;

  .goods-sales {
    font-size: 22rpx;
    color: #666666;
    display: flex;
    align-items: center;

    &::before {
      content: "📊";
      margin-right: 4rpx;
      font-size: 18rpx;
    }
  }

  .goods-stock {
    font-size: 22rpx;
    font-weight: 600;
    padding: 2rpx 8rpx;
    border-radius: 6rpx;
    display: flex;
    align-items: center;
    background: rgba(34, 163, 255, 0.1);
    color: #22A3FF;

    &::before {
      content: "📦";
      margin-right: 4rpx;
      font-size: 18rpx;
    }

    &.low-stock {
      background: rgba(255, 143, 0, 0.1);
      color: #FF8F00;

      &::before {
        content: "⚠️";
      }
    }

    &.no-stock {
      background: rgba(255, 71, 87, 0.1);
      color: #FF4757;

      &::before {
        content: "❌";
      }
    }
  }
}
</style>
