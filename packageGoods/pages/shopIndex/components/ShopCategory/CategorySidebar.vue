<template>
  <view class="category-sidebar">
    <view
      v-for="(item, index) in categories"
      :key="index"
      class="category-sidebar-item"
      :class="{ 'category-sidebar-item-active': activeIndex === index }"
      @click="handleCategorySelect(index)">
      <text class="category-sidebar-text">{{ item.name }}</text>
    </view>
  </view>
</template>

<script setup>
// Props定义
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  activeIndex: {
    type: Number,
    default: 0
  }
})

// Events定义
const emit = defineEmits(['category-select'])

// 事件处理
const handleCategorySelect = (index) => {
  emit('category-select', index)
}
</script>

<style lang="scss" scoped>
.category-sidebar {
  width: 180rpx;
  background-color: #f0f0f0;

  .category-sidebar-item {
    height: 88rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: #f0f0f0;
    transition: background-color 0.3s ease;

    &.category-sidebar-item-active {
      background-color: #ffffff;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 4rpx;
        background-color: #FF6B35;
      }

      .category-sidebar-text {
        color: #333333;
        font-weight: 600;
      }
    }

    .category-sidebar-text {
      font-size: 26rpx;
      color: #666666;
      text-align: center;
      line-height: 1.2;
      padding: 0 16rpx;
      font-weight: 500;
      transition: color 0.3s ease;
    }

    &:active {
      background-color: rgba(255, 255, 255, 0.8);
    }
  }
}
</style>
