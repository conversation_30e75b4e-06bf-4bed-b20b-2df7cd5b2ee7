<template>
  <view class="category-sidebar">
    <view
      v-for="(item, index) in categories"
      :key="index"
      class="category-sidebar-item"
      :class="{ 'category-sidebar-item-active': activeIndex === index }"
      @click="handleCategorySelect(index)">
      <text class="category-sidebar-text">{{ item.name }}</text>
    </view>
  </view>
</template>

<script setup>
// Props定义
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  activeIndex: {
    type: Number,
    default: 0
  }
})

// Events定义
const emit = defineEmits(['category-select'])

// 事件处理
const handleCategorySelect = (index) => {
  emit('category-select', index)
}
</script>

<style lang="scss" scoped>
.category-sidebar {
  width: 200rpx;
  background-color: #f8f8f8;

  .category-sidebar-item {
    height: 100rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background-color: #f8f8f8;
    transition: background-color 0.2s ease;
    border-bottom: 1rpx solid #f0f0f0;

    &.category-sidebar-item-active {
      background-color: #ffffff;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        bottom: 0;
        width: 6rpx;
        background-color: #22A3FF;
        border-radius: 0 3rpx 3rpx 0;
      }

      .category-sidebar-text {
        color: #22A3FF;
        font-weight: 600;
      }
    }

    .category-sidebar-text {
      font-size: 28rpx;
      color: #666666;
      text-align: center;
      line-height: 1.3;
      padding: 0 20rpx;
      font-weight: 500;
      transition: color 0.2s ease;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }
}
</style>
