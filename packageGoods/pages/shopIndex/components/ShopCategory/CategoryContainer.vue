<template>
  <view class="category-container">
    <!-- 分类导航和商品列表 -->
    <view class="category-main">
      <!-- 左侧分类导航 -->
      <CategorySidebar
        :categories="categories"
        :active-index="activeCategoryIndex"
        @category-select="handleCategorySelect" />

      <!-- 右侧内容区域 -->
      <view class="category-content">
        <!-- 二级分类标签 -->
        <CategorySubTabs
          v-if="hasSubCategories"
          :sub-categories="currentSubCategories"
          :active-index="activeSubCategoryIndex"
          @sub-category-select="handleSubCategorySelect"
          @more-categories="handleMoreCategories" />

        <!-- 商品列表 -->
        <CategoryGoodsList
          :goods-list="goodsList"
          :loading="loading"
          :cart-count-map="cartCountMap"
          :has-categories="categories.length > 0"
          @goods-click="handleGoodsClick"
          @spec-select="handleSpecSelect"
          @quantity-change="handleQuantityChange" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'
import CategorySidebar from './CategorySidebar.vue'
import CategorySubTabs from './CategorySubTabs.vue'
import CategoryGoodsList from './CategoryGoodsList.vue'

// Props定义
const props = defineProps({
  categories: {
    type: Array,
    default: () => []
  },
  activeCategoryIndex: {
    type: Number,
    default: 0
  },
  activeSubCategoryIndex: {
    type: Number,
    default: 0
  },
  goodsList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  cartCountMap: {
    type: Object,
    default: () => ({})
  }
})

// Events定义
const emit = defineEmits([
  'category-select',
  'sub-category-select',
  'more-categories',
  'goods-click',
  'spec-select',
  'quantity-change'
])

// 计算属性
const currentCategory = computed(() => {
  return props.categories[props.activeCategoryIndex] || {}
})

const currentSubCategories = computed(() => {
  return currentCategory.value.chlidGoodType || []
})

const hasSubCategories = computed(() => {
  return currentSubCategories.value.length > 0
})

// 事件处理
const handleCategorySelect = (index) => {
  emit('category-select', index)
}

const handleSubCategorySelect = (index) => {
  emit('sub-category-select', index)
}

const handleMoreCategories = () => {
  emit('more-categories')
}

const handleGoodsClick = (goods) => {
  emit('goods-click', goods)
}

const handleSpecSelect = (goods) => {
  emit('spec-select', goods)
}

const handleQuantityChange = (goods, delta) => {
  emit('quantity-change', goods, delta)
}
</script>

<style lang="scss" scoped>
.category-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; // 确保flex子元素正确收缩

  .category-main {
    flex: 1;
    display: flex;
    min-height: 0; // 确保flex子元素正确收缩

    .category-content {
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #ffffff;
      min-height: 0; // 确保flex子元素正确收缩
      overflow: hidden; // 防止内容溢出
    }
  }
}
</style>
