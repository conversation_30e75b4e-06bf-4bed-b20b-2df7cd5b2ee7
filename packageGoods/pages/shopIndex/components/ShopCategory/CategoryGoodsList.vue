<template>
  <scroll-view scroll-y="true" class="category-goods-list">
    <!-- 无分类数据状态 -->
    <view v-if="!hasCategories" class="empty-state">
      <view class="empty-icon">📂</view>
      <view class="empty-title">暂无分类数据</view>
      <view class="empty-desc">该店铺还没有设置商品分类</view>
    </view>

    <!-- 无商品数据状态 -->
    <view v-else-if="goodsList.length === 0" class="empty-state">
      <view class="empty-icon">📦</view>
      <view class="empty-title">暂无商品</view>
      <view class="empty-desc">当前分类下还没有商品</view>
    </view>

    <!-- 商品列表 -->
    <GoodsItem
      v-for="(item, index) in goodsList"
      :key="index"
      :goods="item"
      :cart-quantity="getCartCount(item.id)"
      @goods-click="handleGoodsClick"
      @spec-select="handleSpecSelect"
      @quantity-change="handleQuantityChange" />
  </scroll-view>
</template>

<script setup>
import GoodsItem from '../ShopGoods/GoodsItem.vue'

// Props定义
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  cartCountMap: {
    type: Object,
    default: () => ({})
  },
  hasCategories: {
    type: Boolean,
    default: true
  }
})

// Events定义
const emit = defineEmits([
  'goods-click',
  'spec-select',
  'quantity-change'
])

// 方法
const getCartCount = (goodId) => {
  return props.cartCountMap[goodId] || 0
}

// 事件处理
const handleGoodsClick = (goods) => {
  emit('goods-click', goods)
}

const handleSpecSelect = (goods) => {
  emit('spec-select', goods)
}

const handleQuantityChange = (goods, delta) => {
  emit('quantity-change', goods, delta)
}
</script>

<style lang="scss" scoped>
.category-goods-list {
  flex: 1;
  padding: 16rpx;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  min-height: 0; // 确保flex子元素正确收缩

  .empty-state {
    min-height: 400rpx;
    padding: 80rpx 32rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: #fafafa;
    border-radius: 16rpx;
    margin: 20rpx;
    border: 1rpx solid #f0f0f0;
    position: relative;

    .empty-icon {
      font-size: 80rpx;
      margin-bottom: 24rpx;
      opacity: 0.7;
      line-height: 1;
    }

    .empty-title {
      color: #333333;
      font-size: 28rpx;
      font-weight: 600;
      margin-bottom: 12rpx;
      text-align: center;
      line-height: 1.4;
    }

    .empty-desc {
      color: #888888;
      font-size: 24rpx;
      text-align: center;
      line-height: 1.5;
      max-width: 300rpx;
      word-wrap: break-word;
    }

    .empty-image {
      width: 160rpx;
      height: 160rpx;
      margin-bottom: 20rpx;
      opacity: 0.7;
    }

    .empty-text {
      color: #999999;
      font-size: 26rpx;
      text-align: center;
      line-height: 1.4;
    }
  }

  // 响应式设计
  @media screen and (max-width: 750rpx) {
    .empty-state {
      min-height: 300rpx;
      padding: 60rpx 24rpx;
      margin: 16rpx;

      .empty-icon {
        font-size: 64rpx;
        margin-bottom: 20rpx;
      }

      .empty-title {
        font-size: 26rpx;
        margin-bottom: 10rpx;
      }

      .empty-desc {
        font-size: 22rpx;
        max-width: 280rpx;
      }

      .empty-image {
        width: 120rpx;
        height: 120rpx;
        margin-bottom: 16rpx;
      }

      .empty-text {
        font-size: 24rpx;
      }
    }
  }
}
</style>
