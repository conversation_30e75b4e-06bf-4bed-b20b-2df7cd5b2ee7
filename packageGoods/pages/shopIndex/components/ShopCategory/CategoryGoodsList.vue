<template>
  <scroll-view scroll-y="true" class="category-goods-list">
    <!-- 无分类数据状态 -->
    <view v-if="!hasCategories" class="empty-state">
      <image src="/static/empty.png" class="empty-image" mode="widthFix"></image>
      <text class="empty-text">该店铺还没有设置商品分类</text>
    </view>

    <!-- 无商品数据状态 -->
    <view v-else-if="goodsList.length === 0" class="empty-state">
      <image src="/static/empty.png" class="empty-image" mode="widthFix"></image>
      <text class="empty-text">当前分类下还没有商品</text>
    </view>

    <!-- 商品列表 -->
    <GoodsItem
      v-for="(item, index) in goodsList"
      :key="index"
      :goods="item"
      :cart-quantity="getCartCount(item.id)"
      @goods-click="handleGoodsClick"
      @spec-select="handleSpecSelect"
      @quantity-change="handleQuantityChange" />
  </scroll-view>
</template>

<script setup>
import GoodsItem from '../ShopGoods/GoodsItem.vue'

// Props定义
const props = defineProps({
  goodsList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  cartCountMap: {
    type: Object,
    default: () => ({})
  },
  hasCategories: {
    type: Boolean,
    default: true
  }
})

// Events定义
const emit = defineEmits([
  'goods-click',
  'spec-select',
  'quantity-change'
])

// 方法
const getCartCount = (goodId) => {
  return props.cartCountMap[goodId] || 0
}

// 事件处理
const handleGoodsClick = (goods) => {
  emit('goods-click', goods)
}

const handleSpecSelect = (goods) => {
  emit('spec-select', goods)
}

const handleQuantityChange = (goods, delta) => {
  emit('quantity-change', goods, delta)
}
</script>

<style lang="scss" scoped>
.category-goods-list {
  flex: 1;
  padding: 20rpx 16rpx;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  min-height: 0;
  background-color: #ffffff;

  .empty-state {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;

    .empty-image {
      width: 200rpx;
      margin-bottom: 24rpx;
      opacity: 0.6;
    }

    .empty-text {
      font-size: 28rpx;
      color: #999999;
      text-align: center;
      line-height: 1.4;
    }
  }


}
</style>
