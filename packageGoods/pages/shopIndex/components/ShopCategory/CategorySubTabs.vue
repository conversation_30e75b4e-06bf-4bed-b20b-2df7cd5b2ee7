<template>
  <scroll-view
    v-if="subCategories.length > 0"
    scroll-x="true"
    class="category-sub-tabs"
    :show-scrollbar="false">
    <view class="category-sub-tabs-container">
      <!-- 显示前4个分类 -->
      <view
        v-for="(item, index) in displayCategories"
        :key="index"
        class="category-sub-tab"
        :class="{ 'category-sub-tab-active': activeIndex === index }"
        @click="handleSubCategorySelect(index)">
        {{ item.name }}
      </view>

      <!-- 当分类超过4个时显示"更多"按钮 -->
      <view
        v-if="subCategories.length > 4"
        class="category-sub-tab category-more-btn"
        @click="handleMoreCategories">
        更多
        <text class="more-arrow">▼</text>
      </view>
    </view>
  </scroll-view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  subCategories: {
    type: Array,
    default: () => []
  },
  activeIndex: {
    type: Number,
    default: 0
  },
  maxDisplay: {
    type: Number,
    default: 4
  }
})

// Events定义
const emit = defineEmits(['sub-category-select', 'more-categories'])

// 计算属性
const displayCategories = computed(() => {
  return props.subCategories.slice(0, props.maxDisplay)
})

// 事件处理
const handleSubCategorySelect = (index) => {
  emit('sub-category-select', index)
}

const handleMoreCategories = () => {
  emit('more-categories')
}
</script>

<style lang="scss" scoped>
.category-sub-tabs {
  background: #ffffff;
  padding: 16rpx 0;

  .category-sub-tabs-container {
    display: flex;
    padding: 0 20rpx;
    align-items: center;
    gap: 12rpx;

    .category-sub-tab {
      flex-shrink: 0;
      padding: 12rpx 20rpx;
      background: #f8f8f8;
      border-radius: 20rpx;
      font-size: 24rpx;
      color: #666666;
      white-space: nowrap;
      font-weight: 500;
      transition: all 0.2s ease;

      &.category-sub-tab-active {
        background: #22A3FF;
        color: #ffffff;
        font-weight: 600;
      }

      &.category-more-btn {
        background: #e8e8e8;
        color: #888888;
        display: flex;
        align-items: center;
        font-size: 22rpx;

        .more-arrow {
          font-size: 16rpx;
          margin-left: 6rpx;
        }

        &:active {
          background: #d0d0d0;
        }
      }
    }
  }
}
</style>
