<template>
  <view class="shop-stats">
    <view class="stat-item">
      <text class="stat-value">{{ stats.totalPerformance || 0 }}</text>
      <text class="stat-label">创业积分</text>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <text class="stat-value">{{ stats.goodsCount || 0 }}</text>
      <text class="stat-label">商品数量</text>
    </view>
    <view class="stat-divider"></view>
    <view class="stat-item">
      <text class="stat-value">{{ stats.salesVolume || 0 }}</text>
      <text class="stat-label">商品销量</text>
    </view>
  </view>
</template>

<script setup>
// Props定义
const props = defineProps({
  stats: {
    type: Object,
    default: () => ({
      totalPerformance: 0,
      goodsCount: 0,
      salesVolume: 0
    })
  }
})
</script>

<style lang="scss" scoped>
.shop-stats {
  display: flex;
  align-items: center;
  justify-content: space-around;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16rpx;
  padding: 24rpx 0;

  .stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;

    .stat-value {
      font-size: 32rpx;
      color: #FF6B35;
      font-weight: 600;
      margin-bottom: 8rpx;
    }

    .stat-label {
      font-size: 24rpx;
      color: #999999;
    }
  }

  .stat-divider {
    width: 2rpx;
    height: 40rpx;
    background-color: #e8e8e8;
  }
}
</style>
