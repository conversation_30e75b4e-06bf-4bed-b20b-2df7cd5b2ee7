<template>
  <view class="shop-info-card">
    <!-- 店铺头部信息 -->
    <view class="store-header">
      <ShopLogo 
        :logo-url="shopInfo.logoAddr"
        :is-enterprise="String(shopInfo.storeStraight) === '1'"
        @logo-click="handleLogoClick" />
      
      <view class="store-basic-info">
        <view class="store-name-row">
          <text class="store-name">{{ shopInfo.storeName }}</text>
          <view v-if="shopInfo.storeLevel" class="store-level">
            <text class="level-text">{{ getLevelText(shopInfo.storeLevel) }}</text>
          </view>
        </view>
        
        <view class="store-intro">
          {{ shopInfo.introduce }}
        </view>
      </view>
    </view>

    <!-- 店铺数据统计 -->
    <ShopStats :stats="shopStats" />
    
    <!-- 宣传图片 -->
    <ShopPropagandaImages 
      :images="propagandaImages" 
      :main-picture="shopInfo.storePicture" />
  </view>
</template>

<script setup>
import { computed } from 'vue'
import ShopLogo from './ShopLogo.vue'
import ShopStats from './ShopStats.vue'
import ShopPropagandaImages from './ShopPropagandaImages.vue'

// Props定义
const props = defineProps({
  shopInfo: {
    type: Object,
    default: () => ({})
  }
})

// Events定义
const emit = defineEmits(['logo-click'])

// 计算属性
const shopStats = computed(() => ({
  totalPerformance: props.shopInfo.totalPerformance || 0,
  goodsCount: props.shopInfo.goodsCount || 0,
  salesVolume: props.shopInfo.salesVolume || 0
}))

const propagandaImages = computed(() => {
  if (props.shopInfo?.storePropagandaImages) {
    return props.shopInfo.storePropagandaImages.split(',')
  }
  return []
})

// 方法
const getLevelText = (level) => {
  const levelMap = {
    '1': 'LV1',
    '2': 'LV2', 
    '3': 'LV3',
    '4': 'LV4',
    '5': 'LV5'
  }
  return levelMap[level] || `LV${level}`
}

const handleLogoClick = () => {
  emit('logo-click')
}
</script>

<style lang="scss" scoped>
.shop-info-card {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.06);
}

.store-header {
  display: flex;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.store-basic-info {
  flex: 1;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-left: 24rpx;

  .store-name-row {
    display: flex;
    align-items: center;
    margin-bottom: 16rpx;

    .store-name {
      font-size: 36rpx;
      color: #333333;
      font-weight: 600;
      margin-right: 16rpx;
      flex: 1;
    }

    .store-level {
      background: linear-gradient(45deg, #FFD700, #FFA500);
      border-radius: 16rpx;
      padding: 6rpx 16rpx;
      box-shadow: 0 2rpx 8rpx rgba(255, 215, 0, 0.3);

      .level-text {
        font-size: 22rpx;
        color: #ffffff;
        font-weight: 600;
      }
    }
  }

  .store-intro {
    font-size: 26rpx;
    color: #666666;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
  }
}
</style>
