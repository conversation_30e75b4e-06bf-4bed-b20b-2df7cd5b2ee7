<template>
  <view class="shop-tab-navigation">
    <!-- 主要Tab导航 -->
    <view class="tab-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ 'tab-active': activeIndex === index }"
        @click="handleTabClick(index)">
        {{ tab }}
        <view class="tab-indicator"></view>
      </view>

      <!-- 关注按钮 -->
      <view class="attention-button" @click="handleAttentionClick">
        {{ attentionText }}
      </view>
    </view>

    <!-- 二级Tab导航 (宝贝页面) -->
    <view v-if="showSecondaryTabs" class="secondary-tab-container">
      <view
        v-for="(tab, index) in secondaryTabs"
        :key="index"
        class="secondary-tab-item"
        :class="{ 'secondary-tab-active': secondaryActiveIndex === index }"
        @click="handleSecondaryTabClick(index)">
        {{ tab }}

        <!-- 排序箭头 (助力值) -->
        <view v-if="index === 2" class="sort-arrows">
          <view
            class="arrow arrow-up"
            :class="{ 'arrow-active': sortType === 'up' && secondaryActiveIndex === 2 }">
            ▲
          </view>
          <view
            class="arrow arrow-down"
            :class="{ 'arrow-active': sortType === 'down' && secondaryActiveIndex === 2 }">
            ▼
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 主要Tab列表
  tabs: {
    type: Array,
    default: () => ['首页', '分类', '宝贝']
  },
  // 当前激活的Tab索引
  activeIndex: {
    type: Number,
    default: 0
  },
  // 二级Tab列表
  secondaryTabs: {
    type: Array,
    default: () => ['综合', '销量', '助力值']
  },
  // 二级Tab激活索引
  secondaryActiveIndex: {
    type: Number,
    default: 0
  },
  // 排序类型
  sortType: {
    type: String,
    default: 'up'
  },
  // 是否关注
  isAttention: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'tab-change',
  'secondary-tab-change',
  'attention-click'
])

// 计算属性
const showSecondaryTabs = computed(() => {
  return props.activeIndex === 2 // 宝贝页面显示二级Tab
})

const attentionText = computed(() => {
  return props.isAttention ? '取消关注' : '关注'
})

// 事件处理
const handleTabClick = (index) => {
  emit('tab-change', index)
}

const handleSecondaryTabClick = (index) => {
  emit('secondary-tab-change', index)
}

const handleAttentionClick = () => {
  emit('attention-click')
}
</script>

<style lang="scss" scoped>
.shop-tab-navigation {
  position: sticky;
  z-index: 15;
  top: 0;
  background: linear-gradient(135deg, #ffffff 0%, #fafafa 100%);
  box-shadow:
    0 2rpx 12rpx rgba(0, 0, 0, 0.04),
    0 1rpx 4rpx rgba(0, 0, 0, 0.02);
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.04);
  backdrop-filter: blur(20rpx);
}

.tab-container {
  height: 96rpx;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: relative;
  padding: 0 20rpx;

  .tab-item {
    font-size: 30rpx;
    color: #666666;
    position: relative;
    padding: 16rpx 20rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 20rpx;
    font-weight: 500;
    cursor: pointer;

    &.tab-active {
      color: #22A3FF;
      font-weight: 600;
      background: rgba(34, 163, 255, 0.08);
      transform: translateY(-2rpx);

      .tab-indicator {
        background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
        transform: scaleX(1);
        box-shadow: 0 2rpx 8rpx rgba(34, 163, 255, 0.3);
      }
    }

    &:active {
      transform: scale(0.96);
    }

    .tab-indicator {
      position: absolute;
      bottom: 8rpx;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      width: 32rpx;
      height: 4rpx;
      background: transparent;
      border-radius: 2rpx;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    }
  }

  .attention-button {
    padding: 0 24rpx;
    height: 56rpx;
    background: linear-gradient(135deg, #22A3FF 0%, #4FC3F7 100%);
    border-radius: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 22rpx;
    font-weight: 600;
    position: relative;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
      0 4rpx 12rpx rgba(34, 163, 255, 0.25),
      inset 0 1rpx 0 rgba(255, 255, 255, 0.2);
    border: 1rpx solid rgba(34, 163, 255, 0.3);
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
      transition: left 0.5s ease;
    }

    &:active {
      transform: scale(0.94);
      background: linear-gradient(135deg, #1976D2 0%, #42A5F5 100%);
      box-shadow:
        0 2rpx 6rpx rgba(34, 163, 255, 0.4),
        inset 0 1rpx 2rpx rgba(0, 0, 0, 0.1);

      &::before {
        left: 100%;
      }
    }
  }
}

.secondary-tab-container {
  height: 88rpx;
  background: rgba(248, 248, 248, 0.6);
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(10rpx);
  padding: 0 20rpx;

  .secondary-tab-item {
    font-size: 28rpx;
    color: #888888;
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 16rpx;
    font-weight: 500;
    cursor: pointer;

    &.secondary-tab-active {
      color: #22A3FF;
      font-weight: 600;
      background: rgba(34, 163, 255, 0.08);
      transform: translateY(-1rpx);
    }

    &:active {
      transform: scale(0.96);
    }

    .sort-arrows {
      margin-left: 6rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 2rpx;

      .arrow {
        font-size: 16rpx;
        color: #cccccc;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        line-height: 1;
        font-weight: 700;

        &.arrow-active {
          color: #22A3FF;
          transform: scale(1.2);
          text-shadow: 0 1rpx 2rpx rgba(34, 163, 255, 0.3);
        }
      }
    }
  }
}
</style>
