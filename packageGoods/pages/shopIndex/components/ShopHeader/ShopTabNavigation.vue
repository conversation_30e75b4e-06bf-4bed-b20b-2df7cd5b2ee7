<template>
  <view class="shop-tab-navigation">
    <!-- 主要Tab导航 -->
    <view class="tab-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ 'tab-active': activeIndex === index }"
        @click="handleTabClick(index)">
        {{ tab }}
        <view class="tab-indicator"></view>
      </view>

      <!-- 关注按钮 -->
      <view class="attention-button" @click="handleAttentionClick">
        {{ attentionText }}
      </view>
    </view>

    <!-- 二级Tab导航 (宝贝页面) -->
    <view v-if="showSecondaryTabs" class="secondary-tab-container">
      <view
        v-for="(tab, index) in secondaryTabs"
        :key="index"
        class="secondary-tab-item"
        :class="{ 'secondary-tab-active': secondaryActiveIndex === index }"
        @click="handleSecondaryTabClick(index)">
        {{ tab }}

        <!-- 排序箭头 (助力值) -->
        <view v-if="index === 2" class="sort-arrows">
          <view
            class="arrow arrow-up"
            :class="{ 'arrow-active': sortType === 'up' && secondaryActiveIndex === 2 }">
            ▲
          </view>
          <view
            class="arrow arrow-down"
            :class="{ 'arrow-active': sortType === 'down' && secondaryActiveIndex === 2 }">
            ▼
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  // 主要Tab列表
  tabs: {
    type: Array,
    default: () => ['首页', '分类', '宝贝']
  },
  // 当前激活的Tab索引
  activeIndex: {
    type: Number,
    default: 0
  },
  // 二级Tab列表
  secondaryTabs: {
    type: Array,
    default: () => ['综合', '销量', '助力值']
  },
  // 二级Tab激活索引
  secondaryActiveIndex: {
    type: Number,
    default: 0
  },
  // 排序类型
  sortType: {
    type: String,
    default: 'up'
  },
  // 是否关注
  isAttention: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits([
  'tab-change',
  'secondary-tab-change',
  'attention-click'
])

// 计算属性
const showSecondaryTabs = computed(() => {
  return props.activeIndex === 2 // 宝贝页面显示二级Tab
})

const attentionText = computed(() => {
  return props.isAttention ? '取消关注' : '关注'
})

// 事件处理
const handleTabClick = (index) => {
  emit('tab-change', index)
}

const handleSecondaryTabClick = (index) => {
  emit('secondary-tab-change', index)
}

const handleAttentionClick = () => {
  emit('attention-click')
}
</script>

<style lang="scss" scoped>
.shop-tab-navigation {
  position: sticky;
  z-index: 15;
  top: 0;
  background: #ffffff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid #f0f0f0;
}

.tab-container {
  height: 96rpx;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: space-around;
  position: relative;
  padding: 0 20rpx;

  .tab-item {
    font-size: 30rpx;
    color: #666666;
    position: relative;
    padding: 16rpx 20rpx;
    transition: color 0.2s ease;
    font-weight: 500;
    cursor: pointer;

    &.tab-active {
      color: #22A3FF;
      font-weight: 600;

      .tab-indicator {
        background: #22A3FF;
        transform: scaleX(1);
      }
    }

    .tab-indicator {
      position: absolute;
      bottom: 8rpx;
      left: 50%;
      transform: translateX(-50%) scaleX(0);
      width: 32rpx;
      height: 3rpx;
      background: transparent;
      border-radius: 2rpx;
      transition: transform 0.2s ease;
    }
  }

  .attention-button {
    padding: 0 24rpx;
    height: 56rpx;
    background: #22A3FF;
    border-radius: 28rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #ffffff;
    font-size: 22rpx;
    font-weight: 600;
    transition: background-color 0.2s ease;

    &:active {
      background: #1976D2;
    }
  }
}

.secondary-tab-container {
  height: 88rpx;
  background: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: space-around;
  border-top: 1rpx solid #e8e8e8;
  padding: 0 20rpx;

  .secondary-tab-item {
    font-size: 28rpx;
    color: #888888;
    display: flex;
    align-items: center;
    padding: 16rpx 20rpx;
    transition: color 0.2s ease;
    font-weight: 500;
    cursor: pointer;

    &.secondary-tab-active {
      color: #22A3FF;
      font-weight: 600;
    }

    .sort-arrows {
      margin-left: 6rpx;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 2rpx;

      .arrow {
        font-size: 16rpx;
        color: #cccccc;
        transition: color 0.2s ease;
        line-height: 1;
        font-weight: 700;

        &.arrow-active {
          color: #22A3FF;
        }
      }
    }
  }
}
</style>
