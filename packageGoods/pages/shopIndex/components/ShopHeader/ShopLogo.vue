<template>
  <view 
    class="shop-logo" 
    :class="{ 'clickable': isEnterprise }"
    :data-enterprise="isEnterprise ? '点击查看店铺证件' : ''"
    @click="handleClick">
    <image :src="logoFullUrl" mode="aspectFill"></image>
    
    <!-- 企业认证标识 -->
    <view v-if="isEnterprise" class="enterprise-badge">
      <text>企业</text>
    </view>
  </view>
</template>

<script setup>
import { computed } from 'vue'

// Props定义
const props = defineProps({
  logoUrl: {
    type: String,
    default: ''
  },
  isEnterprise: {
    type: Boolean,
    default: false
  }
})

// Events定义
const emit = defineEmits(['logo-click'])

// 计算属性
const logoFullUrl = computed(() => {
  if (!props.logoUrl) return ''
  return uni.env.IMAGE_URL + props.logoUrl
})

// 事件处理
const handleClick = () => {
  if (props.isEnterprise) {
    emit('logo-click')
  }
}
</script>

<style lang="scss" scoped>
.shop-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  flex-shrink: 0;
  border: 3rpx solid #f0f0f0;

  &.clickable {
    transition: transform 0.2s;

    &:active {
      transform: scale(0.95);
    }

    // 企业店铺可点击查看资质提示
    &::after {
      content: attr(data-enterprise);
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.5);
      color: #fff;
      font-size: 22rpx;
      text-align: center;
      padding: 6rpx 0;
      transform: translateY(100%);
      transition: transform 0.3s;
    }

    &:active::after {
      transform: translateY(0);
    }
  }

  image {
    width: 100%;
    height: 100%;
  }

  .enterprise-badge {
    position: absolute;
    top: -6rpx;
    right: -6rpx;
    background: linear-gradient(45deg, #FF6B35, #FF8A50);
    border-radius: 12rpx;
    padding: 4rpx 8rpx;
    box-shadow: 0 4rpx 8rpx rgba(255, 107, 53, 0.3);

    text {
      font-size: 18rpx;
      color: #ffffff;
      font-weight: 600;
    }
  }
}
</style>
