<template>
	<view class="recharge">
		<view class="amount-selection-card">
			<view class="card-title">
				<text>助力金额</text>
				<text class="subtitle">选择或输入您想要助力的金额</text>
			</view>

			<view class="amount-options">
				<view
					v-for="(item, index) in priceList"
					:key="index"
					:class="['amount-option', selectedIndex === index ? 'selected' : '']"
					@click="changeSelectedIndex(index)"
				>
					<text v-if="item !== '其他'">¥ {{item}}</text>
					<text v-else>{{item}}</text>
				</view>
			</view>
		</view>

		<view style="margin-bottom: 24rpx;">
			<activityBanner></activityBanner>
		</view>

		<view class="amount-confirm-card">
			<view class="amount-input-row">
				<text class="label">助力金额</text>
				<input
					type="number"
					v-model="amount"
					placeholder="请输入助力金额"
					class="amount-input"
					ref="amountInput"
				/>
			</view>

			<view class="divider"></view>

			<view class="amount-summary">
				<text class="summary-text">助力金额</text>
				<text class="summary-amount">¥ {{amount}}</text>
			</view>
		</view>

		<view class="confirm-button-container">
			<view class="confirm-button" hover-class="button-hover" @click="toPay">
				<text>确认助力</text>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref,
		nextTick,
		watch
	} from 'vue';
	import {
		redTo
	} from '@/hooks';
	import {
		parseObjToPath
	} from '@/utils';

	const priceList = [
		50, 100, 300, 500, 1500, '其他'
	]
	const amount = ref(50)

	// 跟踪选中的索引
	const selectedIndex = ref(0) // 默认选中第一个选项

	// 输入框引用
	const amountInput = ref(null)

	function changeSelectedIndex(index) {
		// 更新选中索引
		selectedIndex.value = index

		if (priceList[index] === '其他') {
			// 选择“其他”选项时，清空金额并聚焦输入框
			amount.value = ''
			// 使用nextTick确保在DOM更新后聚焦
			nextTick(() => {
				// 聚焦输入框
				if (amountInput.value) {
					amountInput.value.focus()
				}
			})
		} else {
			// 选择其他选项时，直接设置金额
			amount.value = priceList[index]
		}
	}

	// 监听金额输入框的变化
	watch(amount, (newVal) => {
		// 如果用户手动输入了金额，并且该金额不在预设金额列表中
		// 则自动选中“其他”选项
		if (newVal && !priceList.includes(Number(newVal))) {
			// 找到“其他”选项的索引
			const otherIndex = priceList.findIndex(item => item === '其他')
			if (otherIndex !== -1) {
				selectedIndex.value = otherIndex
			}
		}
	})

	function toPay() {
		// 验证金额是否有效
		if (!amount.value || isNaN(Number(amount.value)) || Number(amount.value) <= 0) {
			uni.showToast({
				title: '请输入有效的助力金额',
				icon: "none"
			})
			return
		}

		let info = {
			price: Number(amount.value),
			payClassic: 3
		};
		// 使用redTo函数跳转到收银台页面
		redTo(`/pages/cashier/cashier${parseObjToPath(info)}`)

		// 注释掉原来的直接支付方式
		// payResult(info, true, () => {
		// 	uni.showToast({
		// 		title: '助力成功~',
		// 		icon: "none"
		// 	})
		// })
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.recharge {
		padding: 30rpx;
		padding-bottom: 140rpx;
	}

	.amount-selection-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.card-title {
		margin-bottom: 24rpx;
		display: flex;
		align-items: center;

		text {
			font-size: 32rpx;
			color: #333333;
			font-weight: 500;
		}

		.subtitle {
			font-size: 24rpx;
			color: #999999;
			margin-left: 16rpx;
			font-weight: normal;
		}
	}

	.amount-options {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 20rpx;
	}

	.amount-option {
		height: 80rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background-color: #F8F8F8;
		border-radius: 12rpx;
		font-size: 30rpx;
		color: #333333;
		transition: all 0.2s ease;
		border: 1rpx solid #EEEEEE;
	}

	.amount-option.selected {
		background: linear-gradient(135deg, #22A3FF, #1A69D1);
		color: #FFFFFF;
		transform: scale(1.02);
		box-shadow: 0 4rpx 8rpx rgba(34, 163, 255, 0.2);
		border: none;
	}

	.amount-confirm-card {
		background-color: #FFFFFF;
		border-radius: 16rpx;
		padding: 30rpx;
		margin-bottom: 24rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	}

	.amount-input-row {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 24rpx;
	}

	.label {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
	}

	.amount-input {
		text-align: right;
		font-size: 30rpx;
		color: #333333;
		width: 60%;
	}

	.divider {
		height: 1rpx;
		background: linear-gradient(to right, rgba(0,0,0,0.05), rgba(0,0,0,0.1), rgba(0,0,0,0.05));
		margin: 24rpx 0;
	}

	.amount-summary {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.summary-text {
		font-size: 28rpx;
		color: #666666;
	}

	.summary-amount {
		font-size: 36rpx;
		color: #22A3FF;
		font-weight: 600;
	}

	.confirm-button-container {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		z-index: 10;
	}

	.confirm-button {
		height: 90rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #22A3FF, #1A69D1);
		border-radius: 45rpx;
		box-shadow: 0 6rpx 12rpx rgba(34, 163, 255, 0.2);
		transition: all 0.2s ease;

		text {
			font-size: 32rpx;
			color: #FFFFFF;
			font-weight: 500;
		}
	}

	.button-hover {
		transform: scale(0.98);
		opacity: 0.9;
	}
</style>