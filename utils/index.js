import {
	ref
} from "vue";
/*
获取底部安全距离
参数bottomMore:再增加多少间距。
*/
export function getSafeBottom(bottomMore = '20') {
	let safeBottom = 0;
	const {
		safeArea,
		screenHeight,
		safeAreaInsets
	} = uni.getSystemInfoSync();
	// #ifdef MP-WEIXIN
	safeBottom = `calc(${screenHeight - safeArea.bottom}px + ${bottomMore}rpx)`
	// #endif
	// #ifndef MP-WEIXIN
	safeBottom = `calc(${safeAreaInsets.bottom}px + ${bottomMore}rpx)`
	// #endif
	return safeBottom
}

//将对象解析成路径携带参数
export function parseObjToPath(obj) {
	let str = '?'
	for (let key in obj) {
		if (!(obj[key] !== 0 && !obj[key])) {
			str += `${str == '?' ? '' : '&' }${key}=${obj[key]}`
		}
	}
	return str
}
//解析形似 "{"0":"files/20191205/(1)_1575521260134.jpg","1":"files/20191205/(2)_1575521260732.jpg"}"格式的图片 固定返回数组;
export function parseImgurl(str) {
	// 如果传入值为 null 或 undefined，直接返回空数组
	if (str === null || str === undefined) {
		return [];
	}
	
	if (typeof(str) == 'string') {
		try {
			let res = JSON.parse(str)
			if (Array.isArray(res)) {
				return res
			}
			if (typeof(res) == 'string') {
				return [res]
			}
			if (Object.prototype.toString.call(res) === '[object Object]') {
				let sz = []
				for (let key in res) {
					sz.push(res[key])
				}
				return sz
			}
		} catch (e) {
			console.error('parseImgurl问题:' + e)
			//TODO handle the exception
			return []; // 解析失败时返回空数组
		}
	} else if (Array.isArray(str)) {
		return str;
	}
	
	// 其他情况返回空数组，确保始终返回数组类型
	return [];
}
//获取胶囊图信息
export function getJnInfo() {
	// #ifdef MP-WEIXIN
	return wx.getMenuButtonBoundingClientRect()
	// #endif
	return {
		width: 0,
		height: 0,

	}
}


// 检测数据类型的功能函数
const checkedType = (target) => Object.prototype.toString.call(target).replace(/\[object (\w+)\]/, "$1").toLowerCase();
// 实现深拷贝（Object/Array）
export const clone = (target, hash = new WeakMap) => {
	let result;
	let type = checkedType(target);
	if (type === 'object') result = {};
	else if (type === 'array') result = [];
	else return target;
	if (hash.get(target)) return target;
	let copyObj = new target.constructor();
	hash.set(target, copyObj)
	for (let key in target) {
		if (checkedType(target[key]) === 'object' || checkedType(target[key]) === 'array') {
			result[key] = clone(target[key], hash);
		} else {
			result[key] = target[key];
		}
	}
	return result;
}
//去掉所有空格
export function trim(str) {
	let reg = /[\t\r\f\n\s]*/g;
	if (typeof str === 'string') {
		str = str.replace(reg, '');
	}
	return str
}
//将数字解析成 89,878,789 类似的数字
export function formatNum(num) {
	let str = num + '';
	let newStr = "";
	let count = 0;
	// 当数字是整数
	if (str.indexOf(".") == -1) {
		for (let i = str.length - 1; i >= 0; i--) {
			if (count % 3 == 0 && count != 0) {
				newStr = str.charAt(i) + "," + newStr;
			} else {
				newStr = str.charAt(i) + newStr;
			}
			count++;
		}
		str = newStr; //自动补小数点后两位
		return str;
	}
	// 当数字带有小数
	else {
		for (let i = str.indexOf(".") - 1; i >= 0; i--) {
			if (count % 3 == 0 && count != 0) {
				newStr = str.charAt(i) + "," + newStr;
			} else {
				newStr = str.charAt(i) + newStr; //逐个字符相接起来
			}
			count++;
		}
		str = newStr + (str + "00").substr((str + "00").indexOf("."), 3);
		return str;
	}
}
//将秒转化为天，时，分，秒的形式
export function getAllTime(t) {
	//如果小余1  则返回0
	let r = {
		d: 0,
		h: 0,
		m: 0,
		s: 0,
	}
	r.d = Math.floor(t / (3600 * 24));
	r.h = Math.floor((t - r.d * 3600 * 24) / 3600);
	r.m = Math.floor((t - r.d * 3600 * 24 - r.h * 3600) / 60)
	r.s = t - r.d * 3600 * 24 - r.h * 3600 - r.m * 60
	return r
}
//返回当天年月日
export function todayDate() {
	const date = new Date();
	const y = date.getFullYear(); //获取完整的年份(4位)
	const m = date.getMonth(); //获取当前月份(0-11,0代表1月)
	const d = date.getDate(); //获取当前日(1-31)
	const time = `${y}-${m}-${d}`
	return time
}