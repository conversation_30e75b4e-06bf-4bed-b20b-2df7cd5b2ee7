<template>
	<view class="search">
		<view class="search-iptWrap">
			<input class="search-iptWrap-input" type="text" v-model="searchText" placeholder="搜索您想资助的产品"
				placeholder-style="color:#999999">
			<view class="search-iptWrap-button" @click="toSearch()">
				搜索
			</view>
		</view>
		<view class="search-commonWrap" v-if="!isSearch">
			<view class="search-commonWrap-top">
				<view class="search-commonWrap-top-title">
					历史搜索
				</view>
				<image @click="oldDelete" class="search-commonWrap-top-img" src="@/static/search/delete.png" mode="">
				</image>
			</view>
			<view class="search-commonWrap-tab">
				<view v-for="(item,index) in oldKeywordList" :key="index" @click="wordSearch(item)">
					{{item}}
				</view>

			</view>
		</view>
		<view v-if="(isSearch && !list?.length)  || (!isSearch && !oldKeywordList?.length)">
			<empty></empty>
		</view>
		<view class="search-cnt">
			<view v-for="(item,index) in list" :key="index">
				<productDetail :info='item' width="338rpx" height="460rpx" :showGoCart='false' sizeType='normal'>
				</productDetail>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		computed,
		nextTick,
		ref
	} from 'vue';
	import {
		listGet
	} from '@/hooks'
	import productDetail from '@/components/productDetail/productDetail.vue';
	const searchText = ref('')
	const oldKeywordList = ref([])
	const isSearch = ref(false)

	async function refresh() {
		let {
			data
		} = await uni.http.get(uni.api.getUser);
		oldKeywordList.value = data.result;
	}

	//清除历史搜索
	function oldDelete() {
		uni.showModal({
			content: '确定清除历史搜索记录？',
			success: async res => {
				if (res.confirm) {
					uni.showLoading({
						mask: true,
					});
					let {
						data
					} = await uni.http.post(uni.api.del, {
						searchKey: '',
					});
					oldKeywordList.value = [];
					uni.hideLoading();
				} else if (res.cancel) {
					console.log('用户点击取消');
				}
			},
		});
	}

	refresh()

	async function wordSearch(item) {
		searchText.value = item
		await nextTick()
		toSearch()
	}

	function toSearch() {
		isSearch.value = true;
		listRefresh()
	}

	const {
		mode,
		list,
		total,
		refresh: listRefresh
	} = listGet({
		apiUrl: uni.api.searchGoodList,
		method: 'post',
		isPullDownRefresh: false,
		isWatchOptions: false,
		isReqTypeReq: false,
		isReachBottom: true,
		options: computed(() => ({
			search: searchText.value,
			isPlatform: 0,
			pattern: 0
		}))
	})
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.search {
		padding: 30rpx;

		&-commonWrap {
			&-top {
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-title {
					color: #333333;
					font-size: 28rpx;
				}

				&-img {
					width: 30rpx;
					height: 30rpx;
				}
			}

			&-tab {
				padding: 30rpx 0;
				display: flex;
				flex-wrap: wrap;

				>view {
					margin-right: 24rpx;
					margin-bottom: 24rpx;
					padding: 12rpx 21rpx;
					font-size: 26rpx;
					color: #666666;
					background: #e7e7e7;
					border-radius: 100rpx;
				}
			}
		}

		&-cnt {
			display: grid;
			/* 三列，均分容器宽度 */
			grid-template-columns: repeat(2, 1fr);
			gap: 24rpx;

			>view {
				background-color: white;
			}
		}

		&-iptWrap {
			height: 88rpx;
			background: #fafafa;
			border: 2rpx solid #999999;
			border-radius: 100rpx;
			padding-left: 50rpx;
			padding-right: 20rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 54rpx;

			&-input {
				font-size: 28rpx;
			}

			&-button {
				width: 138rpx;
				height: 60rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				color: #FAFAFA;
				font-size: 26rpx;
				display: flex;
				align-items: center;
				justify-content: center;

			}
		}
	}
</style>