<template>
	<view class="phoneLogin">
		<view class="phoneLogin-container">
			<view class="phoneLogin-line">
				<view>
					手机号
				</view>
				<view>
					<input v-model="submitInfo.phone" type="number" placeholder="请输入手机号"
						placeholder-class="phoneLogin-line-placeClass" />
				</view>
			</view>
			<view class="phoneLogin-line">
				<view>
					图形验证码
				</view>
				<view>
					<input v-model="submitInfo.captchaContent" type="text" placeholder="图形验证码"
						placeholder-class="phoneLogin-line-placeClass" style="margin-right: 20rpx;" />
					<image v-if="qrInfo.base64Img" @click="getQrCode" :src="qrInfo.base64Img" mode="heightFix"
						style="height:50rpx;"></image>
				</view>
			</view>


			<view class="phoneLogin-line">
				<view>
					验证码
				</view>
				<view>
					<input v-model="submitInfo.captcha" maxlength="6" type="number" placeholder="请输入验证码"
						placeholder-class="phoneLogin-line-placeClass" style="margin-right: 20rpx;" />
					<view style="color: #368BFF;font-size: 28rpx;" v-if="countDown.timeCount == 60" @click="getCode">
						获取验证码
					</view>
					<view v-else class="phoneLogin-line-placeClass">
						{{ countDown.timeCount }}s后可发
					</view>
				</view>
			</view>
		</view>

		<view class="phoneLogin-rules" @click="changeReadStatus">
			<view>
				<view v-if="isRead">

				</view>
			</view>
			<view>
				我已阅读并同意 <text @click.stop="userAgree">《用户使用协议》</text>及<text @click.stop="ysAgree">《隐私政策》</text>
			</view>
		</view>




		<view class="phoneLogin-btn">
			<view @click="toLogin">
				登录
			</view>
		</view>
	</view>
</template>
<script setup>
	let isRead = ref(false);
	import {
		getSafeBottom
	} from '@/utils';
	import {
		ref
	} from "vue";
	import {
		countDownStore,
	} from '@/store';
	import {
		loginSuccessCallBack
	} from '@/hooks';
	getQrCode()
	const countDown = countDownStore();
	let phoneReg = /^1[3456789]\d{9}$/; //手机号
	let submitInfo = ref({
		phone: '',
		captcha: "",
		promotionCode: "",
		captchaContent: ""
	})
	//图形验证码信息
	let qrInfo = ref({

	})

	//阅读协议已读状态
	function changeReadStatus() {
		isRead.value = !isRead.value
	}
	//跳转用户协议
	function userAgree() {
		uni.navigateTo({
			url: '/packageSearch/pages/agreement/agreement?path=userServiceAgreement'
		})
	}
	//跳转用户协议
	function ysAgree() {
		uni.navigateTo({
			url: '/packageSearch/pages/agreement/agreement?path=privacyAgreement'
		})
	}

	//获取验证码
	async function getCode() {
		if (!phoneReg.test(submitInfo.value.phone)) {
			uni.showToast({
				title: '手机号格式不正确',
				icon: 'none'
			})
			return;
		}
		if (!submitInfo.value.captchaContent) {
			uni.showToast({
				title: '请输入图形验证码',
				icon: 'none'
			})
			return;
		}
		uni.showLoading({
			mask: true
		})
		await uni.http.post(uni.api.appVerificationCode, {
			phone: submitInfo.value.phone,
			captchaKey: qrInfo.value.captchaKey,
			captchaContent: submitInfo.value.captchaContent
		})
		countDown.createTimer();
		uni.hideLoading()
	}
	//获取图形验证码
	async function getQrCode() {
		uni.showLoading({
			mask: true
		})
		let {
			data
		} = await uni.http.post(uni.api.getCaptcha)
		qrInfo.value = data.result
		uni.hideLoading()
	}
	//去登录
	async function toLogin() {
		if (!phoneReg.test(submitInfo.value.phone)) {
			uni.showToast({
				title: '手机号格式不正确',
				icon: 'none'
			})
			return;
		}
		if (!submitInfo.value.captchaContent) {
			uni.showToast({
				title: '请输入图形验证码',
				icon: 'none'
			})
			return;
		}
		if (!isRead.value) {
			uni.showToast({
				title: '请阅读并同意相关协议',
				icon: 'none'
			})
			return;
		}

		uni.showLoading({
			mask: true
		})
		// loginByPhone
		try {
			let {
				data
			} = await uni.http.get(uni.api.refreshToken, {
				params: submitInfo.value
			})
			console.log(data)
			loginSuccessCallBack({
				result: {
					['X-AUTH-TOKEN']: data.result
				}
			})
			uni.hideLoading()
		} catch (e) {
			uni.hideLoading()
			//TODO handle the exception
		}
	}
</script>
<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.phoneLogin {
		width: 750rpx;
		padding: 32rpx;

		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}



		&-rules {
			display: flex;
			align-items: center;
			margin-top: 30rpx;
			justify-content: flex-end;

			>view:nth-child(1) {
				width: 35rpx;
				height: 35rpx;
				border: 1rpx solid #B3B3B3;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				margin-right: 10rpx;

				>view {
					width: 30rpx;
					height: 30rpx;
					border-radius: 50%;
					background-color: #FF7171;
				}
			}

			>view:nth-child(2) {
				color: #666666ff;
				font-size: 25rpx;
				font-face: PingFangSC;
				font-weight: 400;

				>text {
					color: #0055feff;
				}
			}
		}


		&-container {
			width: 100%;
			background: #FFFFFF;
			border-radius: 24rpx;
			padding: 0 32rpx;
		}

		&-line {
			width: 100%;
			height: 120rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			border-bottom: 2rpx solid #EEEEEE;
			font-size: 30rpx;
			color: #000000;

			>view:nth-child(2) {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				input {
					text-align: right;
				}
			}

			&-placeClass {
				font-size: 28rpx;
				color: #A2A2A2;
			}
		}

		&-line:last-child {
			border-bottom: none;
		}
	}
</style>