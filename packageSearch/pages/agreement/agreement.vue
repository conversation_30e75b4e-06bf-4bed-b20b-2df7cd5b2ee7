<!-- 58行客服电话未修改 -->
<template>
	<view class="attentionContent">
		<view class="modular" v-for="(item, index) in modularsData" :key="index">
			<view class="title">{{ item.title }}</view>
			<view class="cnt">
				<text class="seat-size"></text>
				<text>{{ item.cnt }}</text>
			</view>
		</view>
	</view>
</template>
<script>
export default {
	data() {
		return {
			//用户服务协议
			userServiceAgreement: [
				{
					title: `第一条 定义 `,
					cnt: `1.1 本平台：是指由${uni.env.APP_COMPANY}电子渠道运营中心所有并负责运营的“${
						uni.env.APP_NAME
					}”平台。1.2 所有权以及相关的权利：在本协议中是指：第一，所有权；第二，知识产权，包括但不限于著作权、专利权、商标权、商业秘密等；第三，除上述权利之外的对物、信息及其载体和表现形式的使用、利用、传播、复制、发行、编辑、修改、处分等权利。1.3 用户：是指接受本平台服务的自然人、法人或其他组织。1.4 个人信息：在本协议中，是指以电子或者其他方式记录的能够单独或者与其他信息结合识别用户身份或者反映用户活动情况的各种信息。1.5 网络服务：是指本平台基于互联网方式向用户提供的业务宣传、业务查询、业务办理、交费充值、商品交易、用户关怀、营销活动等服务。1.6 平台内经营者：是指除${
						uni.env.APP_COMPANY
					}以外的、通过本网站销售商品或者提供服务的第三方经营者。`
				},
				{
					title: `第二条 平台服务内容`,
					cnt: `2.1 本平台向用户提供的服务包括：(1)用户注册、登录、安全退出、找回密码、修改密码、基本资料修改、历史订单查询等服务；(2)业务查询、业务办理、充值缴费等服务；(3)商品交易服务；(4)对本平台用户进行消息推送，通过短信消息、微信公众号等媒体推送的消息的通知服务；(5)本平台为用户提供的其他服务。2.2 本平台有权根据情况变化调整平台服务的具体内容，本平台服务内容进行调整的，${
						uni.env.APP_COMPANY
					}无需另行通知用户，调整后的内容一经发布即自动生效。如用户不同意上述调整，应当立即停止使用服务；如用户继续使用服务，视为用户同意调整后的内容并同意遵守。2.3 本平台仅提供服务，与服务有关的通信设备（包括但不限于电脑终端、手机终端、调制解调器及其他与接入互联网有关的装置）及通信服务所需的费用（包括但不限于为接入互联网而支付的电话费及宽带费）由用户自行承担。2.4 用户使用本平台提供的具体服务时，应当满足相应的前提条件、具备相应的服务使用权限。用户不满足使用服务的前提条件、不具备服务使用权限的，无法使用相应服务。2.5 用户使用本平台提供的服务的，应当同时遵守用户与${
						uni.env.APP_COMPANY
					}及/或关联公司另行签订的相应服务协议`
				},
				{
					title: `第三条 用户账号管理`,
					cnt: `3.1 用户确认，用户注册、登录以及实际使用本平台提供的服务时，应当是具备完全民事权利能力和完全民事行为能力的自然人、法人或其他组织。如用户不具备前述主体资格，则用户及用户的监护人应当依照法律规定承担因此而导致的一切后果，${
						uni.env.APP_COMPANY
					}有权注销或永久冻结用户的账户。3.2 用户可以使用手机号码、宽带账号等作为本平台账号、登录本平台。用户也可以按照本平台要求的程序进行注册，获得在本平台的账号，登录本平台；按照本注册程序获得本平台账号的用户，可以在登录后通过“我的”进行注册信息的变更及注销。用户也可以按照本平台要求的程序、使用用户拥有的其他第三方平台账号登录本平台。3.3 用户使用具体服务时，本平台可能会要求用户完成进一步激活或验证流程，用户应当按照相应页面的提示提供身份信息及资料，并应保证提供的身份信息及资料真实、及时、完整和准确。3.4 用户在本平台的账号、密码、短信随机验证码等是用户在本平台的唯一身份权证。为享受本平台提供的网络服务，用户应当按照对应的服务要求键入正确的用户账号及密码或短信验证码或其他身份信息。3.5 用户应妥善保管用户账号、密码和手机短信验证码，避免使用过于简单的密码。用户在服务使用过程中可以根据本平台规定更改密码。用户账号和密码不得出借、转让、继承或者赠与。因非本平台原因导致用户的账号、密码遗失、遗忘或被他人窃取的，本平台不承担责任。如果用户发现自己的个人信息泄密，尤其是本平台账户及密码发生泄露，请立即联络${
						uni.env.APP_COMPANY
					}。3.6 用户通过账号、密码和/或手机短信验证码登录后使用本平台的行为，均视为用户本人行为，用户应当承担由此导致的相关后果和责任。3.7 用户登录后，可以设置用户在本平台的昵称。用户账号、昵称应当符合法律法规规定，不得违反法律法规规定、侵犯或涉嫌侵犯他人合法权益，否则，${
						uni.env.APP_COMPANY
					}有权暂停或终止提供本平台的所有服务，并冻结用户账号。`
				},
				{
					title: `第四条 商品交易服务规则`,
					cnt: `4.1 用户可以使用本平台提供的商品交易服务，可以通过本平台订购${
						uni.env.APP_COMPANY
					}或平台内经营者销售的商品和服务（统称“商品”）。4.2 对于平台内经营者销售的商品，本平台依法履行平台责任，同时用户应当遵守与平台内经营者之间的约定，${
						uni.env.APP_COMPANY
					}不参与平台内经营者与用户之间的交易或服务过程，不对平台内经营者的销售行为（包括但不限于发布商品信息、收取费用、商品配送、售后服务等）的合法性、真实性、有效性作任何明示和默示的担保，亦不对由此产生的后果承担法律责任。4.3 用户应当按照本协议约定订购商品。用户订购商品时，应当仔细确认所购商品的名称、价格、数量、型号、联系地址、电话、收货人等信息。收货人与用户本人不一致的，收货人/用户的行为和意思表示视为用户/收货人的行为和意思表示，用户/收货人应对收货人/用户的行为及意思表示的法律后果承担连带责任。4.4 ${
						uni.env.APP_COMPANY
					}或平台内经营者作为商品销售方时应当按照本协议约定向用户销售商品。商品销售方将会按照与用户的约定将用户购买的商品送到其所指定的送货地址。所有在本平台上列出的送货时间为参考时间，参考时间的计算是根据库存状况、正常的处理过程和送货时间、送货地点的基础上估计得出的。用户应当清楚准确地填写真实姓名、送货地址及联系方式。因如下情况造成订单延迟或无法配送等，商品销售方不承担迟延配送的责任：(1)用户提供错误信息和不详细的地址；(2)货物送达无人签收，由此造成的重复配送所产生的费用及相关的后果；(3)不可抗力，例如：自然灾害、交通戒严、突发战争等；(4)法定及特殊节假日期间，物流商无法正常配送的。`
				},
				{
					title: `第五条 用户的权利和义务`,
					cnt: `5.1 用户使用本协议项下服务，应当遵守《中华人民共和国网络安全法》《中华人民共和国电信条例》《中华人民共和国计算机信息网络国际联网暂行规定》和其他有关法律、法规或相关规定、以及本协议的约定，不得存在任何违法违规行为，不得侵犯${
						uni.env.APP_COMPANY
					}以及任何第三方的合法权益。5.2 在遵守本协议的前提下，用户有权使用本平台提供的服务。用户同意通过短消息通知及公众号等端口短信消息接收通信费、使用量阈值提醒信息及用户关怀、营销活动等最新的优惠产品和服务的相关信息。如用户不愿意接受相关信息，可以通过客服电话${ 
						''}或按照短信消息中退订提示进行退订。5.3 本平台所提供的服务需要获得用户授权终端设备的部分信息方可正常使用；本平台按照相关法律法规，以最小化权限原则、区分网络服务场景进行授权获取的提示。用户有权不同意，但可能会影响本平台或相应网络服务的正常使用。5.4 用户承诺使用网络服务应当符合国家法律法规的规定，且行为符合下列要求：(1)不干扰本平台的正常运转，不进行任何破坏或试图破坏网络安全的行为，不进行任何改变或试图改变本平台系统配置或破坏系统安全的行为，不得侵入本平台及国家计算机信息系统；(2)不对本平台展示或提供的、由${
						uni.env.APP_COMPANY
					}所有的任何数据或信息作商业性利用，包括但不限于在未经${uni.env.APP_COMPANY}事先书面同意的情况下，以复制、传播等任何方式使用本平台展示或提供的、由${
						uni.env.APP_COMPANY
					}所有的资料、信息、数据等；(3)不模仿、修改、翻译、改编、出借、出售、转许可、传播或转让本平台提供的服务，也不得逆向工程、反汇编、反编译、分解拆卸或试图以其他方式发现本平台提供的服务的源代码；(4)不发送、上传和储存带有病毒的、蠕虫的、木马和其他有害内容的计算机代码、文件、脚本和程序；(5)遵守所有使用网络服务的网络协议、规定、程序和惯例；(6)不从事其他违反法律法规规定或双方约定的行为。5.5 用户不得通过本平台制作、复制、发布、传播含有下列内容的信息：（1）反对宪法所确定的基本原则的；（2）危害国家安全，泄露国家秘密，颠覆国家政权，破坏国家统一的；（3）损害国家荣誉和利益的；（4）煽动民族仇恨、民族歧视，破坏民族团结的；（5）破坏国家宗教政策，宣扬邪教和封建迷信的；（6）散布谣言，扰乱社会秩序，破坏社会稳定的；（7）散布淫秽、色情、赌博、暴力、凶杀、恐怖或者教唆犯罪的；（8）侮辱或者诽谤他人，侵害他人合法权益的；（9）含有法律、行政法规禁止的其他内容的。5.6 若用户存在违反法律规定或本协议约定的任一情形，本平台有权按相关规定暂停或终止提供服务，保存有关记录，并向政府主管部门报告，由此引起的一切后果和责任由用户负责。同时，${
						uni.env.APP_COMPANY
					}有权终止本协议，并不承担任何责任。5.7 用户违反本协议使用网络服务导致${uni.env.APP_COMPANY}或第三人损失的，应当承担相应法律责任、赔偿${
						uni.env.APP_COMPANY
					}或第三人的损失。`
				},
				{
					title: `第六条 本平台的权利和义务`,
					cnt: `6.1 任何本平台所发布的信息及/或其载体，及/或其表现形式，及/或服务，除以下情况之外，其所有权及相关的权利均独占性地属于${
						uni.env.APP_COMPANY
					}及/或关联公司所有：(1)依据相关法律规定该信息及/或载体，及/或服务，不享有所有权以及与此相关的权利的；(2)依据${
						uni.env.APP_COMPANY
					}和其他主体之间的协议、合同、章程，由${uni.env.APP_COMPANY}和其他主体共享，或其他主体单独享有或${uni.env.APP_COMPANY}转让的；(3)依据${
						uni.env.APP_COMPANY
					}具有法律效力的正式声明而放弃的。6.2 非经${
						uni.env.APP_COMPANY
					}及/或授权其使用的第三方权利人书面同意，用户不得擅自使用本平台内容，且无权擅自复制、修改这些内容，或创造与内容有关的派生产品。任何对本平台享有的基于该信息及/或其载体，及/或其表现形式，及/或服务的权利的侵犯，均属于对${
						uni.env.APP_COMPANY
					}权利的侵犯。`
				},
				{
					title: `第七条 用户个人信息保护`,
					cnt: `7.1 用户应保证自己在登录使用本平台时用户身份信息的真实性、准确性及完整性，如果相关信息发生变化，用户应及时通知本平台予以变更。用户应当就用户身份信息的不真实、不准确、不完整承担责任。本平台不能也不会对个人信息的不真实、不准确，或个人信息未及时更新而引起的任何损失或损害承担责任。7.2 用户未能按照本平台的要求和内容提供本平台要求的必要信息的，本平台有权中止本平台所提供的对应服务，由此所导致的任何已经或可能发生的损失，由用户自行承担，本平台并保留对该用户进行追索的权利。本平台有权对用户的信息及相关操作进行查阅，发现信息或相关操作中存在任何问题或怀疑，均有权向用户发出询问及要求改正的通知或者直接作出删除账户等处理。7.3 ${
						uni.env.APP_COMPANY
					}非常重视用户个人信息的保护，${
						uni.env.APP_COMPANY
					}通过本平台向用户提供服务时，将按照相关法律法规的规定收集、存储、使用、共享、转移、公开披露、保护和管理用户信息和隐私。本网站的隐私政策具体见《用户隐私政策》，用户同意仔细阅读并充分理解和接受该隐私政策，并同意该隐私政策作为本协议的重要组成部分。${
						uni.env.APP_COMPANY
					}将在停止运营本平台时，停止继续收集个人信息活动，并通过通知或公告的形式告知用户。`
				},
				{
					title: `第八条 违约责任`,
					cnt: `8.1 双方应共同遵守本协议约定，如一方违约给对方造成损失的，违约方应赔偿守约方的损失。8.2 除双方另有约定外，用户违反本协议中的义务、承诺、保证等的任意内容，${
						uni.env.APP_COMPANY
					}均有权就其违约情节，尤其是对${
						uni.env.APP_COMPANY
					}或相关第三方造成的损失，随时采取以下措施中的一种或多种：8（1）要求用户立即更换、修改相应内容；8（2）限制、中止用户使用相应服务；8（3）终止用户使用全部服务，终止本协议；8（4）依法追究用户的法律责任。88.3 如用户因违反有关法律法规或者本协议，使${
						uni.env.APP_COMPANY
					}遭受任何损失，受到其他用户、任何第三方的索赔或任何行政管理部门的处罚，用户应对${
						uni.env.APP_COMPANY
					}或相关第三方的实际损失进行全额赔偿（包括合理的律师费用等必要费用）。`
				},
				{
					title: `第九条 责任限制`,
					cnt: `9.1 本平台不就通信系统或互联网的中断或无法运作、技术故障、计算机错误或终端病毒、信息损坏或丢失或其它在本平台合理控制范围之外的原因而产生的其他任何性质的破坏而向用户或任何第三方承担赔偿责任。9.2 本平台需要定期或不定期地对相关的信息网络系统、设备进行检修、维护或升级，如因此类情况而造成网络服务在合理时间内的中断，本平台无需为此承担任何责任，但本平台将尽可能事先进行通知。9.3 ${
						uni.env.APP_COMPANY
					}将致力于提供快捷、准确的平台服务；但因网络环境、信息交互时延等客观因素，${
						uni.env.APP_COMPANY
					}不保证用户在使用本平台时在操作上不会中断或没有错误，不保证能及时纠正本网站所有缺陷，不保证本平台能满足用户的所有要求，亦不保证用户能通过本平台订购所有的业务和产品，对网络服务的及时性、安全性、准确性也都不作担保。9.4 用户通过本平台进行交易、获取有偿服务而发生的所有税费及有关费用，均由用户承担。${
						uni.env.APP_COMPANY
					}提醒用户应该通过自己的谨慎判断确定相关服务及陈列商品及相关信息的准确新、真实性、合法性和有效性。`
				},
				{
					title: `第十条 网络服务的停止`,
					cnt: `10.1 在以下情形下，本平台有权停止对用户提供服务，且无需通知用户或取得用户同意:(1)如用户是通过手机号码及服务密码/或短信验证码登陆的，当该用户所对应的手机号码停机或销号后；(2)用户违反本协议规定使用网络服务；(3)用户提供的身份信息等资料不真实；(4)用户账户被删除。10.2 用户不同意本协议的内容及${
						uni.env.APP_COMPANY
					}对本协议的后续修改，应当停止使用本平台的服务。用户如对本平台的服务不满意，可以停止使用本平台的服务，或通知本平台停止对该用户的服务。10.3、用户服务停止后，本平台没有义务向该用户或第三方传送任何未处理的信息或未完成的服务，亦无需对该用户或第三方负责。但本协议的终止并不意味着终止前所发生的未完成用户指令的撤销，也不能消除因终止前的交易所带来的法律后果。`
				},
				{
					title: `第十一条 其他网络应用或服务提供`,
					cnt: `11.1 从本平台链接至${
						uni.env.APP_COMPANY
					}以外的应用或服务：某些情况下，本平台会提供跳转至其它应用或服务。此应用或服务将会引导用户到第三方发行或经营的应用或服务界面，该第三方可能并非${
						uni.env.APP_COMPANY
					}的合作机构或与${uni.env.APP_COMPANY}有任何联系。${uni.env.APP_COMPANY}将该应用或服务列入本平台内，仅为方便用户的使用需求快捷满足。除非${
						uni.env.APP_COMPANY
					}已经明确声明与该第三方有合作关系，否则，提供应用或服务至此第三方界面，并不视为${
						uni.env.APP_COMPANY
					}同意、推荐、认可、保证或推介任何第三方或在第三方网站上所提供的任何服务、产品，亦不可视为${
						uni.env.APP_COMPANY
					}与该第三方及其网站有任何形式的合作。11.2 从本站链接至${uni.env.APP_COMPANY}其它应用或服务：本平台亦包含链接至${
						uni.env.APP_COMPANY
					}其它应用或服务，以方便用户的使用。${
						uni.env.APP_COMPANY
					}其他应用或服务各自制定使用条款，条款可能互有差异，用户应先仔细查阅适用的使用条款，然后方可使用相关的应用或服务。`
				},
				{
					title: `第十二条 通知`,
					cnt: `12.1 本平台对于用户的所有通知均可通过用户预留的任一联系方式（包括但不限于电子邮件、手机号码）或者本平台公告的方式送达。该等通知于发送之日视为已送达用户。`
				},
				{
					title: `第十三条 协议范围及协议变更`,
					cnt: `13.1 本协议包括《用户服务协议》正文以及所有本平台已经发布的或将来可能发布的各类规则、通知、公告等（统称“规则”）。所有规则为本协议不可分割的组成部分，与本协议正文具有同等法律效力。13.2 本协议仅适用于${
						uni.env.APP_COMPANY
					}通过本平台提供的服务，不适用于通过其他平台（包括但不限于网站、APP等）提供的服务。本平台有权根据法律法规变化和运营需要，不时地制定、修改本协议条款和/或各类规则。本协议及/或各类规则如有任何变更，将以本平台公示方式进行公告，而不再单独通知用户。用户如果不同意相关变更，应立即停止使用本平台提供的网络服务。如果用户继续享用本平台服务，则视为用户对修改后的协议和规则的同意和遵守。`
				},
				{
					title: `第十四条 法律适用与争议解决`,
					cnt: `14.1 本协议之效力、解释、变更、履行与争议解决均适用中华人民共和国（为本协议之目的，不含香港、澳门、台湾地区）法律法规。14.2 因双方就本协议的签订、履行或解释发生争议，双方应持平等、友好、争取合理解决问题的态度协商解决；如协商未成，任何一方均应向${
						uni.env.APP_COMPANY
					}所在地法院提起诉讼。`
				}
			],
			//隐私保护协议
			privacyAgreement: [
				{
					title: `一、 ${uni.env.APP_NAME}如何收集您的个人信息`,
					cnt: `您的个人信息来源于您向${uni.env.APP_NAME}提供的信息及${uni.env.APP_NAME}通过您使用平台服务所收集到的信息：您向${uni.env.APP_NAME}提供的信息a)您根据${
						uni.env.APP_NAME
					}要求提供的个人注册信息（商家应法律法规要求需公示的企业名称及相关工商注册信息除外）；b)您使用平台特定产品或服务时（如购买产品功能或会员服务等），为满足向您提供产品和服务之目的，除注册时提供的信息外，您还需要进一步向我们提供您的真实姓名、身份证号码、银行卡、头像和简介等信息，如果您不使用特定产品和服务，则无需提供相关信息。${
						uni.env.APP_NAME
					}通过您使用平台服务所收集到的信息您在使用${
						uni.env.APP_NAME
					}时，为满足向您提供服务之目的，更个性化的为您提供相关服务，除您在注册或开通服务时提供的个人信息外，我们还可能会记录您在使用我们的产品和/或服务时提供、形成或留存的信息，包括但不限于设备信息、日志信息、位置信息、唯一应用程序编号、网络和连接信息、Cookie数据等。敏感信息：个人敏感信息包括您的种族、宗教、个人健康和医疗信息等一旦泄露、非法提供或滥用可能危害人身和财产安全，极易导致个人名誉、身心健康受到损害或歧视性待遇等的特殊个人信息。请您在使用本产品和/或服务时，由您主动提供、上传或发布的内容和信息，可能会泄露您的敏感个人信息，请您谨慎对待。`
				},
				{
					title: `二、 ${uni.env.APP_NAME}如何使用、转移、披露您的个人信息`,
					cnt: `1.信息使用a) ${uni.env.APP_NAME}不会向任何无关第三方提供、出售、出租、分享或交易您的个人信息，除非事先得到您的许可，或该第三方和${
						uni.env.APP_NAME
					}单独或共同为您提供服务，且在该服务结束后，其将被禁止访问包括其以前能够访问的所有这些资料。b) ${
						uni.env.APP_NAME
					}亦不允许任何第三方以任何手段收集、编辑、出售或者无偿传播您的个人信息。任何${
						uni.env.APP_NAME
					}平台用户如从事上述活动，一经发现，产品有权立即终止与该用户的服务协议。c)为服务用户的目的，${
						uni.env.APP_NAME
					}可能通过使用您的个人信息，向您提供您感兴趣的信息，包括但不限于向您发出产品和服务信息，或者与产品合作伙伴共享信息以便他们向您发送有关其产品和服务的信息（后者需要您的事先同意）。2.信息转移及披露在如下情况下，${
						uni.env.APP_NAME
					}将依据您的个人意愿或法律的规定全部或部分的披露您的个人信息：a)经您事先同意，向第三方披露；b)为提供您所要求的产品和服务，而必须和第三方分享您的个人信息；c)基于国家、公共利益及安全，依照法律及合法程序，${
						uni.env.APP_NAME
					}将向有权的司法机关、行政机关或有关个人披露您的个人信息，或依照法院裁决、判决或 政府机关的强制要求执行相关行为。请您知悉，上述国家包括但不限于中华人民共和国或其他产品开展业务的国家及相关法律法规。d)为维护第三人的合法利益，或调查、预防或处理违法、违规、侵权、安全或技术方面的问题或学术研究等合理之目的等情形下而使用、共享、披露您的个人信息。e)如您是适格的知识产权投诉人并已提起投诉，应被投诉人要求，向被投诉人披露，以便双方处理可能的权利纠纷；f)在${
						uni.env.APP_NAME
					}平台上创建的某一交易中，如交易任何一方履行或部分履行了交易义务并提出信息披露请求的，${
						uni.env.APP_NAME
					}有权决定向该用户提供其交易对方的联络方式等必要信息，以促成交易的完成或纠纷的解决。g)其它产品根据法律、法规或者网站协议认为合适的披露。h)数据转移若${
						uni.env.APP_NAME
					}开发者团队发生重组、合并、分立、清算或资产出售时，您的信息将作为交易标的转移到新的主体，在本协议变更前，您的个人信息仍然受到本个人信息保护协议的保护。3.信息存储和交换${
						uni.env.APP_NAME
					}收集的有关您的信息和资料将保存在${
						uni.env.APP_NAME
					}及（或）其关联公司的服务器上，这些信息和资料可能传送至您所在国家、地区或产品收集信息和资料所在地的境外并在境外被访问、存储和展示。*   Cookie的使用a)在您未拒绝接受cookies的情况下，${
						uni.env.APP_NAME
					}会在您的手机上设定或取用cookies，以便您能登录或使用依赖于cookies的${uni.env.APP_NAME}平台服务或功能。${
						uni.env.APP_NAME
					}使用cookies可为您提供更加周到的个性化服务，包括推广服务。b)您有权选择接受或拒绝接受cookies。您可以通过修改浏览器设置的方式拒绝接受cookies。但如果您选择拒绝接受cookies，则您可能无法登录或使用依赖于cookies的产品网络服务或功能。c)通过${
						uni.env.APP_NAME
					}所设cookies所取得的有关信息，将适用本协议。`
				},
				{
					title: `三、您如何管理或删除您的个人信息`,
					cnt: `您可以在我们系统中查阅或删除您的上传的声音、等信息；您也可以通过修改个人设置、删除相关信息等方式撤回部分授权；请知悉，当您撤回同意或授权后，可能将导致${
						uni.env.APP_NAME
					}无法继续为您提供服务。您可以选择通过修改浏览器设置的方式拒绝cookie信息，但您可能无法登录或使用依赖于cookie的${uni.env.APP_NAME}服务或功能。`
				},
				{
					title: `四、关于未成年人的个人信息`,
					cnt: `${
						uni.env.APP_NAME
					}非常重视对未成年人信息的保护。若您是18周岁以下的未成年人，请您及您的监护人仔细阅读本个人信息保护协议，并在征得您的监护人同意的前提下使用本产品和/或服务或向本产品提供信息。`
				},
				{
					title: `五、您的个人信息存储`,
					cnt: `请您知悉，${uni.env.APP_NAME}在全球运营平台及服务时就收集到的信息将统一默认存储在中华人民共和国境内的服务器内。`
				},
				{
					title: `六、${uni.env.APP_NAME}的个人信息保障措施`,
					cnt: `我们将采取符合业界标准、合理可行的安全防护措施保护您提供的个人信息安全，防止您的个人信息遭到未经授权访问、公开披露、使用、修改、损坏或丢失。我们会以高度的勤勉义务对待您的信息，未经您同意，我们不会向任何第三方共享您的信息。也请您谨慎向第三方提供您的个人信息。如您发现自己存储在产品内的个人信息泄密，请您立即联络我方，以便我方采取相应措施`
				},
				{
					title: `七、 个人信息保护协议的修订`,
					cnt: `${uni.env.APP_NAME}可能对本协议进行不定期修改，因此请经常查看。协议修改后，${
						uni.env.APP_NAME
					}将在相关页面或以其他合适的方式公布修改的内容。如果您不同意本协议的修改，请立即停止使用${
						uni.env.APP_NAME
					}或取消已经获得的服务；如果您选择在本协议修改后继续访问或使用${uni.env.APP_NAME}平台或服务的，则视为您已接受本协议的修改。您与${
						uni.env.APP_NAME
					}签署的本协议所列明的条款，并不能完全涵盖您与${uni.env.APP_NAME}所有的关于个人信息的权利和义务。因此，${
						uni.env.APP_NAME
					}公布其他声明、规则等均视为本协议之补充条款，为本协议不可分割的组成部分，与本协议具有同等法律效力。`
				},
				{
					title: `八、如何联系我们`,
					cnt: `如您对本协议有任何疑问、建议或意见的，或您发现您的个人信息可能被泄露的，您可通过app内意见反馈联系我们 。本协议的生效、履行、解释及争议的解决，您因使用${
						uni.env.APP_NAME
					}平台或服务而产生或与本协议相关的一切争议、权利主张或其他事项，均适用中华人民共和国法律（港澳台地区除外 ）。您与${
						uni.env.APP_NAME
					}平台发生的一切争议，应友好协商，如协商不成的，可选择按下述任一途径解决：（一） 提交仲裁委员会，按照申请仲裁时现行有效的仲裁规则进行仲裁；仲裁裁决为终局裁决，对各方均有约束力；（二） 提交协议签订地人民法院诉讼管辖；请您再次确认您已全部阅读并充分理解上述条款。【审慎提醒】 如您使用${
						uni.env.APP_NAME
					}平台，将视为您已同意前述服务使用协议及个人信息保护协议，该等条款将立即生效，并构成您和${
						uni.env.APP_NAME
					}平台之间有约束力的法律文件。请您再次确认您已全部阅读并充分理解上述协议。`
				}
			],
			modularsData: ''
		};
	},
	
	onLoad(options) {
		let title = '服务条款';
		if (options.path) {
			this.modularsData = this[options.path];
		}
		if (options.path == 'privacyAgreement') {
			title = '隐私政策';
		}
		uni.setNavigationBarTitle({
			title
		});
	}
};
</script>

<style lang="scss">
.attentionContent {
	width: 100%;
	padding: 40rpx;
	margin-bottom: 30rpx;

	.modular {
		font-size: 28rpx;
		margin-bottom: 50rpx;

		.title {
			font-weight: 700;
			font-size: 32rpx;
			margin-bottom: 30rpx;
		}

		.cnt {
			line-height: 40rpx;

			.seat-size {
				width: 50rpx;
				height: 40rpx;
				display: inline-block;
			}
		}
	}
}
</style>
