<template>
	<view class="login">
		<image src="@/static/login/bg.png" class="login-bg"></image>
		<view class="login-container">
			<image :src="imgUrl + settingStore.setting?.frontLogo" mode="widthFix" class="login-container-logo"></image>
			<view class="login-container-text">
				欢迎来到{{env.APP_NAME}}
			</view>
			<view class="login-container-bottom">
				<view v-if="!isRead" class="login-container-btn" @click="modalAtten">
					<!-- <image src="@/static/img/login/wx.png"></image> -->
					手机号快捷登录
				</view>
				<button v-else open-type="getPhoneNumber" @getphonenumber="wxLogin" class="login-container-btn">
					<!-- <image src="@/static/img/login/wx.png"></image> -->
					手机号快捷登录
				</button>
				<view class="login-container-btn"
					style="background-image: linear-gradient(-69deg, #9a9a9a 0%,#9a9a9a 97%)" @click="toUpPage">
					取消登录
				</view>
				<view class="login-container-rules" @click="changeReadStatus">
					<view>
						<view v-if="isRead">

						</view>
					</view>
					<view>
						我已阅读并同意 <text @click.stop="userAgree">《用户使用协议》</text>及<text @click.stop="ysAgree">《隐私政策》</text>
					</view>
				</view>
			</view>
			<!-- 暂时隐藏其他登录方式模块
			<view class="login-otherLogin">
				<view class="login-otherLogin-desc">
					<view>

					</view>
					<view>
						其他登录方式
					</view>
					<view>

					</view>
				</view>
				<view class="login-otherLogin-btn">
					<view @click="navTo('/packageSearch/pages/phoneLogin/phoneLogin')">
						<image src="@/static/login/phone.png"></image>
						<view>
							手机验证
						</view>
					</view>
				</view>
			</view>
			-->

		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from "vue";
	import {
		userStore
	} from '@/store/index.js'
	import {
		getUserInfos,
		loginSuccessCallBack,
		navTo,
		toUpPage,
	} from '@/hooks/index.js'
	import {
		getSafeBottom
	} from '@/utils/index.js'

	const users = userStore()
	import {
		frontSetting
	} from '@/store/index.js'
	const settingStore = frontSetting()
	const imgUrl = uni.env.IMAGE_URL
	//项目配置
	const env = ref(uni.env);
	let isRead = ref(false);
	let code = ref('');
	getCode();
	//阅读协议已读状态
	function changeReadStatus() {
		isRead.value = !isRead.value
	}
	//手机号快捷登录
	async function wxLogin(e) {
		if (!e.detail) {
			uni.showToast({
				title: '授权失败',
				icon: 'none'
			})
			return;
		}
		uni.showLoading({
			mask: true
		})

		try {
			let {
				data
			} = await uni.http.post(uni.api.loginByCode, {
				...e.detail,
				code: code.value
			})
			uni.hideLoading();
			loginSuccessCallBack(data)

		} catch (e) {
			getCode();
			//TODO handle the exception
		}
	}
	//未阅读提示
	function modalAtten() {
		uni.showToast({
			title: '请先阅读并同意相关协议~',
			icon: "none"
		})
	}
	//获取code
	function getCode() {
		return new Promise((resolve, reject) => {
			uni.login({
				success(e) {
					code.value = e.code
					resolve(e.code);
				},
				fail(e) {
					code.value = ''
					reject('login fali:' + e)
					console.log('login fali:' + e)
				}
			})
		})
	}
	//跳转用户协议
	function userAgree() {
		uni.navigateTo({
			url: '/packageSearch/pages/agreement/agreement?path=userServiceAgreement'
		})
	}
	//跳转用户协议
	function ysAgree() {
		uni.navigateTo({
			url: '/packageSearch/pages/agreement/agreement?path=privacyAgreement'
		})
	}
</script>

<style lang="scss">
	.login {
		width: 750rpx;
		padding-top: 376rpx;
		position: relative;

		&-bg {
			width: 750rpx;
			height: 610rpx;
			position: absolute;
			left: 0;
			top: 0;
		}

		&-otherLogin {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;
			font-size: 28rpx;
			position: fixed;
			left: 3vw;
			bottom: v-bind(getSafeBottom(40));
			width: 94vw;
			color: #666666;

			&-desc {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 24rpx;

				>view:nth-child(1),
				>view:nth-child(3) {
					flex: 1;
					height: 2rpx;
					background-color: #F9F9F9;
				}

				>view:nth-child(2) {
					margin: 0 10rpx;
				}
			}

			&-btn {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: center;

				>view {
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					text-align: center;

					>image:nth-child(1) {
						border-radius: 50%;
						width: 80rpx;
						height: 80rpx;
						padding: 10rpx;
						margin-bottom: 16rpx;
						background-color: #F2A33C;
					}
				}
			}
		}

		&-container {
			width: 100%;
			display: flex;
			flex-direction: column;
			align-items: center;



			&-bottom {

				display: flex;
				flex-direction: column;
				align-items: center;
				margin-top: 120rpx;
			}

			&-logo {
				width: 200rpx;
				height: 200rpx;
				margin-bottom: 30rpx;
			}

			&-text {
				font-family: PingFangSC-Regular;
				font-weight: 400;
				font-size: 32rpx;
				color: #333333;
				letter-spacing: 6rpx;
			}

			&-cancel {
				margin-bottom: 20rpx;
				font-size: 26rpx;
				color: #666666;
				text-align: center;
			}

			&-btn {
				width: 606rpx;
				height: 100rpx;
				background-image: linear-gradient(-69deg, #FF0000 0%, #FF7B7B 97%);
				border-radius: 58rpx;
				font-family: PingFangSC-Medium;
				font-weight: 500;
				font-size: 32rpx;
				color: #FFFFFF;
				letter-spacing: 2rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-bottom: 20rpx;
				border: none;

				>image {
					width: 44rpx;
					height: 44rpx;
					margin-right: 10rpx;
				}
			}

			&-rules {
				display: flex;
				align-items: center;

				>view:nth-child(1) {
					width: 35rpx;
					height: 35rpx;
					border: 1rpx solid #B3B3B3;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					margin-right: 10rpx;

					>view {
						width: 30rpx;
						height: 30rpx;
						border-radius: 50%;
						background-color: #FF7171;
					}
				}

				>view:nth-child(2) {
					color: #666666ff;
					font-size: 25rpx;
					font-face: PingFangSC;
					font-weight: 400;

					>text {
						color: #0055feff;
					}
				}
			}
		}
	}
</style>