<template>
	<view class="commissionDetails">
		<!-- <view class="commissionDetails-tab">
			<view v-for="(item,index) in tabData" :key="index" @click="changeTabIndex(index)"
				:class="{selected:index === tabIndex}">
				{{item}}
			</view>
		</view> -->
		<uv-sticky bgColor="#f8f8f8">
			<view style="padding-bottom: 16rpx; padding-top: 8rpx; box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);">
				<uv-tabs :current='tabIndex' :scrollable='false' :list="tabData" lineWidth="45rpx" lineHeight='6rpx'
					lineColor="#1A69D1" :activeStyle="{
							color: '#1A69D1',
							fontSize:'28rpx',
							fontWeight: 'bold'
				    	}" :inactiveStyle="{
							color: '#999999',
							fontSize:'28rpx'
						}" itemStyle="height:74rpx;" @click="changeTabIndex"></uv-tabs>
			</view>
		</uv-sticky>
		<view class="commissionDetails-line" v-for="(item,index) in list" :key="index">
			<view class="commissionDetails-line-left">
				<view class="payType-container">
					<text>{{item.payType}}</text>
					<text v-if="tabIndex === 0"
						class="status-tag"
						:class="{
							'pending-status': item.tradeStatus == 2,
							'completed-status': item.tradeStatus == 5
						}">
						[{{item.tradeStatus == 2 ? '待结算' : '交易完成'}}]
					</text>
				</view>
				<view>
					{{item.createTime}}
				</view>
			</view>
			<view class="commissionDetails-line-right" :class="{
				'pending-amount': tabIndex === 1 || (tabIndex === 0 && item.tradeStatus == 2),
				'completed-amount': tabIndex === 2 || (tabIndex === 0 && item.tradeStatus == 5)
			}">
				+{{item.amount}}
			</view>
		</view>
		<empty v-if="!list?.length"></empty>
	</view>
</template>

<script setup>
	import {
		computed,
		ref
	} from "vue";
	import {
		listGet,
	} from "@/hooks";
	const tabData = [{
		name: '全部'
	}, {
		name: '待结算'
	}, {
		name: '交易完成'
	}];
	let tabIndex = ref(0)
	//改变状态索引
	function changeTabIndex(e) {
		const index = e.index
		tabIndex.value = index
	}
	const patternList = [-1, 1, 5]
	//请求参数
	let reqOptions = computed(() => {
		return {
			pattern: patternList[tabIndex.value]
		}
	})
	const {
		list
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.findMemberRechargeRecordProtomerList,
		pageSize: 20,
		pdIsLogin: true,
		callback: (data) => {
			// 调试：查看返回的数据结构
			if (data && data.length > 0) {
				console.log('佣金明细数据示例:', JSON.stringify(data[0]))
			}
		}
	})
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.commissionDetails {
		padding: 30rpx;
		padding-top: 0;
		padding-bottom: 50rpx;

		.selected {
			color: #333333 !important;
			border-bottom-color: #22A3FF !important;
		}

		&-tab {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 65rpx;
			color: #999999;
			font-size: 28rpx;
			margin-bottom: 30rpx;

			>view {
				border-bottom-color: transparent;
				border-bottom-width: 2rpx;
				border-bottom-style: solid;
				padding-bottom: 6rpx;
			}
		}

		&-line {
			padding: 20rpx 30rpx;
			background-color: white;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			&-left {
				.payType-container {
					color: #333333;
					font-size: 30rpx;
					margin-bottom: 10rpx;
					display: flex;
					align-items: center;
					flex-wrap: wrap;
					font-weight: 500;

					.status-tag {
						font-size: 24rpx;
						margin-left: 12rpx;
						font-weight: normal;

						&.pending-status {
							color: #B7950B;
						}

						&.completed-status {
							color: #f54301;
						}
					}
				}

				>view:nth-child(2) {
					color: #999999;
					font-size: 26rpx;
					margin-top: 4rpx;
				}
			}

			&-right {
				text-align: right;
				font-size: 36rpx;
				font-weight: 500;
				color: #333333;
				min-width: 160rpx;

				&.pending-amount {
					color: #B7950B; /* 深黄褐色文字，与订单未确认相同 */
				}

				&.completed-amount {
					color: #f54301; /* 红色文字，与订单已确认相同 */
				}
			}
		}
	}
</style>