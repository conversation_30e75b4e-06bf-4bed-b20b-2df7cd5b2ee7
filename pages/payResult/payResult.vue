<template>
	<view class="payResult">
		<image class="payResult-logo" src="@/static/img/payResult/success.png" mode="widthFix"></image>
		<view class="payResult-atten">
			支付成功
		</view>
		<view class="payResult-opera">
			<view @click="toIndex">
				返回首页
			</view>
			<view v-if="'residuePayTimes' in giftBagResult && giftBagResult.residuePayTimes >= 1" @click="goGiftBagBuy">
				继续支付
			</view>
			<view @click="toDetail" v-if="detailBtnText">
				{{detailBtnText}}
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		computed,
		ref
	} from "vue";
	import {
		redTo
	} from "@/hooks";
	import {
		parseObjToPath
	} from "@/utils";
	//  1成功  2失败
	let type = ref('1');
	//payClassic 0商品 1礼包 2礼包团 3余额充值 4待支付订单 5兑换券支付
	let payClassic = ref('0')
	//当前购买的礼包信息
	let giftBagResult = ref({})
	const detailBtnText = computed(() => {
		let btnText = ''
		switch (payClassic.value * 1) {
			case 0:
				btnText = '查看订单'
				break;
			case 1:
				btnText = '查看礼包'
				break;
			case 2:
				btnText = '查看团队'
				break;
			case 3:
				btnText = '查看明细'
				break;
			case 4:
				btnText = '查看订单'
				break;
			case 5:
				btnText = '查看券'
				break;
			case 6:
				btnText = '查看批发卡'
				break;
			default:
				break;
		}
		return btnText
	})
	onLoad(async (options) => {
		type.value = options.type
		payClassic.value = options.payClassic
		//如果是礼包,则请求判断是否需要再支付一次
		if (options.payClassic == 1) {
			uni.showLoading({
				mask: true
			})
			//礼包id查询礼包记录id
			let {
				data: dataRes
			} = await uni.http.post(uni.api.getMarketingGiftBagRecordByGiftId, {
				id: options.id
			})
			if (dataRes.result?.id) {
				let {
					data
				} = await uni.http.post(uni.api.findMarketingGiftBagRecordInfo, {
					id: dataRes.result?.id
				})
				giftBagResult.value = data.result || {}
			} else {
				giftBagResult.value = {}
			}
			uni.hideLoading()
		}
	})

	function toIndex() {
		uni.switchTab({
			url: '/pages/index/index'
		})
	}

	function toDetail() {
		try {
			switch (payClassic.value * 1) {
				case 0:
					redTo('/packageOrder/pages/myPackage/myPackage')
					break;
				case 1:
					redTo('/pages/myGiftBag/myGiftBag')
					break;
				case 2:
					redTo('/pages/myGiftBagTeam/myGiftBagTeam')
					break;
				case 3:
					redTo('/pages/avaBalanceRecord/avaBalanceRecord')
					break;
				case 4:
					redTo('/packageOrder/pages/myPackage/myPackage')
					break;
				case 5:
					redTo('/pages/exchangeCoupon/exchangeCoupon')
					break;
				case 6:
					redTo('/pages/wholesaleCard/wholesaleCard')
					break;
				default:
					// 默认跳转到首页
					uni.switchTab({
						url: '/pages/index/index'
					})
					break;
			}
		} catch (error) {
			console.error('跳转详情页面失败:', error);
			// 如果跳转失败，返回首页
			uni.switchTab({
				url: '/pages/index/index'
			})
		}
	}
	// 继续礼包支付
	function goGiftBagBuy() {
		try {
			let obj = {
				id: giftBagResult.value.id, //礼包id
				tPhone: '',
				marketingGiftBagRecordId: giftBagResult.value?.mid || '',
				payClassic: 1
			};
			redTo(`/pages/cashier/cashier${parseObjToPath(obj)}`)
		} catch (error) {
			console.error('跳转收银台页面失败:', error);
			// 如果跳转失败，显示提示
			uni.showToast({
				title: '跳转失败，请稍后再试',
				icon: 'none',
				duration: 2000
			});
		}
	}
</script>

<style lang="scss">
	.payResult {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding-top: 120rpx;

		&-logo {
			width: 120rpx;
			margin-bottom: 45rpx;
		}

		&-atten {
			font-size: 36rpx;
			color: #000000;
			letter-spacing: 0;
			line-height: 50rpx;
			text-align: center;
			margin-bottom: 100rpx;
			font-weight: bold;
		}

		&-opera {
			display: flex;
			align-items: center;
			flex-direction: column;


			>view {
				width: 500rpx;
				height: 100rpx;
				border-radius: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
			}

			>view {
				margin-bottom: 20rpx;
			}

			>view:last-child {
				margin-bottom: 0;
			}

			>view:nth-child(even) {

				background-color: transparent;
				color: #FF1E1E;
				border: 1rpx solid #FF1C1C;
			}


			>view:nth-child(odd) {
				color: white;
				background-image: linear-gradient(-69deg, #FF0000 0%, #FF7B7B 97%);
			}
		}
	}
</style>