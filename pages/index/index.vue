<template>
	<view class="index">
		<view class="index-top">
			<view class="index-top-cnt">
				<view class="index-top-desc">
					目前共<text>{{info.totalStoreCount}}</text>人需要助力
				</view>
			</view>
			<image class="index-top-bg" src="@/static/index/top_bg.png" mode=""></image>
		</view>
		<view class="index-container">
			<view class="index-container-data">
				<view class="dotView">
					<view class="dot">

					</view>
					<view class="dotTitle">
						平台数据
					</view>
				</view>

				<view class="index-container-data-dt">
					<view class="index-container-data-dt-lt">
						<view>
							<view>
								{{info.totalPerformance}}
							</view>
							<view>
								助力总值
							</view>
						</view>
						<view>
							<view>
								{{info.totalMemberCount}}
							</view>
							<view>
								助力人数
							</view>
						</view>
					</view>

					<image @click="navTo('/packageRecharge/pages/recharge/recharge')" class="index-container-data-dt-rt"
						src="@/static/index/btn.png" mode=""></image>


				</view>
			</view>
			<view style="margin: 20rpx 0;">
				<activityBanner></activityBanner>
			</view>

			<view class="index-container-comWrap" v-if="info.storeType15List?.length">
				<view class="index-container-comWrap-top">
					<image class="index-container-comWrap-top-bg" src="@/static/index/d_1.png" mode=""></image>
					<view class="index-container-comWrap-top-ct">
						<view class="index-container-comWrap-top-ct-left">
							品牌馆
							<image src="@/static/index/star.png" mode=""></image>
						</view>
						<view class="index-container-comWrap-top-ct-right"
							@click="navTo('/pages/helpPanel/helpPanelNew?type=1')">
							查看更多
							<image src="@/static/index/arrow-right-blue.png" mode=""></image>
						</view>
					</view>
				</view>
				<view class="index-container-comWrap-main">
					<scroll-view scroll-x="true" class="index-container-comWrap-main-info">
						<view class="index-container-comWrap-main-info-ct">
							<view v-for="(item,index) in info.storeType15List" :key="index">
								<view class="index-container-comWrap-main-info-product" @click="navToGoodDetail(item)">
									<image class="index-container-comWrap-main-info-product-img"
										:src="imgUrl +  parseImgurl(item.mainPicture)[0]" mode=""></image>
									<view class="index-container-comWrap-main-info-product-name">
										{{item.goodName}}
									</view>
									<view class="index-container-comWrap-main-info-product-lv">
										<image src="@/static/index/i_1.png" mode=""></image>
										<view>
											{{item.smallPrice}}
										</view>
									</view>
									<view class='index-container-comWrap-main-info-product-desc'>
										已兑换数量 {{item.salesVolume}}
									</view>
									<view class="index-container-comWrap-main-info-product-desc">
										创业积分 {{item.salesPerformance}}
									</view>
								</view>
							</view>
						</view>
					</scroll-view>
				</view>
			</view>

			<view class="index-container-comWrap" v-if="info.storeType16List?.length">
				<view class="index-container-comWrap-top">
					<image class="index-container-comWrap-top-bg" src="@/static/index/d_1.png" mode=""></image>
					<view class="index-container-comWrap-top-ct">
						<view class="index-container-comWrap-top-ct-left">
							生活馆
							<image src="@/static/index/star.png" mode=""></image>
						</view>
						<view class="index-container-comWrap-top-ct-right"
							@click="navTo('/pages/helpPanel/helpPanelNew?type=2')">
							查看更多
							<image src="@/static/index/arrow-right-blue.png" mode=""></image>
						</view>
					</view>
				</view>
				<view class="index-container-comWrap-main">
					<scroll-view scroll-x="true" class="index-container-comWrap-main-info">
						<view class="index-container-comWrap-main-info-ct">
							<view v-for="(item,index) in info.storeType16List" :key="index">
								<view class="index-container-comWrap-main-info-product" @click="navToGoodDetail(item)">
									<image class="index-container-comWrap-main-info-product-img"
										:src="imgUrl +  parseImgurl(item.mainPicture)[0]" mode=""></image>
									<view class="index-container-comWrap-main-info-product-name">
										{{item.goodName}}
									</view>
									<view class="index-container-comWrap-main-info-product-lv">
										<image src="@/static/index/i_1.png" mode=""></image>
										<view>
											{{item.smallPrice}}
										</view>
									</view>
									<view class='index-container-comWrap-main-info-product-desc'>
										已兑换数量 {{item.salesVolume}}
									</view>
									<view class="index-container-comWrap-main-info-product-desc">
										创业积分 {{item.salesPerformance}}
									</view>
								</view>
							</view>
						</view>
					</scroll-view>


				</view>
			</view>

			<view class="index-container-comWrap" v-if="info.storeType17List?.length">
				<view class="index-container-comWrap-top">
					<image class="index-container-comWrap-top-bg" src="@/static/index/d_1.png" mode=""></image>
					<view class="index-container-comWrap-top-ct">
						<view class="index-container-comWrap-top-ct-left">
							创业馆
							<image src="@/static/index/star.png" mode=""></image>
						</view>
						<view class="index-container-comWrap-top-ct-right"
							@click="navTo('/pages/helpPanel/helpPanelNew?type=3')">
							查看更多
							<image src="@/static/index/arrow-right-blue.png" mode=""></image>
						</view>
					</view>
				</view>
				<view class="index-container-comWrap-main">
					<scroll-view scroll-x="true" class="index-container-comWrap-main-info">
						<view class="index-container-comWrap-main-info-ct">
							<view v-for="(item,index) in info.storeType17List" :key="index">
								<view class="index-container-comWrap-main-info-product" @click="navToGoodDetail(item)">
									<image class="index-container-comWrap-main-info-product-img"
										:src="imgUrl +  parseImgurl(item.mainPicture)[0]" mode=""></image>
									<view class="index-container-comWrap-main-info-product-name">
										{{item.goodName}}
									</view>
									<view class="index-container-comWrap-main-info-product-lv">
										<image src="@/static/index/i_1.png" mode=""></image>
										<view>
											{{item.smallPrice}}
										</view>
									</view>
									<view class='index-container-comWrap-main-info-product-desc'>
										已兑换数量 {{item.salesVolume}}
									</view>
									<view class="index-container-comWrap-main-info-product-desc">
										创业积分 {{item.salesPerformance}}
									</view>
								</view>
							</view>

						</view>
					</scroll-view>


				</view>
			</view>
		</view>
		<cartAction></cartAction>
	</view>
</template>

<script setup lang="ts">
	import { ref } from 'vue';
	import productDetail from '@/components/productDetail/productDetail.vue';
	import { navTo, navToGoodDetail } from '@/hooks';
	import { parseImgurl } from '@/utils'
	import {
		cartCountHooks
	} from '@/hooks/getCartCount.js'
	import { onShow, onPullDownRefresh } from '@dcloudio/uni-app';
	const info = ref({})
	const imgUrl = uni.env.IMAGE_URL
	const refresh = async () => {
		const { data: { result } } = await uni.http.get(uni.api.indexNew)
		info.value = result
		const {
			cartCountRefresh
		} = cartCountHooks()
		cartCountRefresh()
	}
	onShow(() => {
		refresh()
	})
	onPullDownRefresh(() => {
		refresh()
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 500);
	});
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.index {
		.dotView {
			display: flex;
			align-items: center;
		}

		.dot {
			width: 8rpx;
			height: 27rpx;
			background: #22a3ff;
			border-radius: 100px;
			margin-right: 15rpx;
		}

		.dotTitle {
			color: #333333;
			font-size: 30rpx;
		}

		&-container {
			padding: 0 30rpx;
			position: relative;
			z-index: 10;
			top: -140rpx;

			&-comWrap {
				margin-top: 30rpx;

				&-main {
					width: 100%;
					padding: 20rpx;
					border-radius: 32rpx;
					border-top-left-radius: 0;
					background-color: #4CB4FE;
					display: flex;
					align-items: center;
					justify-content: center;

					&-info {
						white-space: nowrap;
						width: 600rpx;
						background-color: white;
						border-radius: 32rpx;
						padding: 20rpx;

						&-ct {
							display: flex;
							align-items: center;

							>view {
								display: flex;
								align-items: center;
								margin-right: 8rpx;
							}

						}




						&-product {
							width: 200rpx;
							color: #999999;

							&-img {
								width: 200rpx;
								height: 200rpx;
								border-radius: 16rpx;
								margin-bottom: 10rpx;
							}

							&-name {
								font-size: 24rpx;
								margin-bottom: 10rpx;
								/* 设置容器的宽度 */
								white-space: nowrap;
								/* 防止文本换行 */
								overflow: hidden;
								/* 隐藏超出部分 */
								text-overflow: ellipsis;
								/* 显示省略号 */
							}

							&-lv {
								display: flex;
								align-items: center;
								font-size: 28rpx;
								color: #E6A600;
								margin-bottom: 10rpx;

								>image {
									width: 20rpx;
									height: 20rpx;
									margin-right: 10rpx;
								}
							}

							&-desc {
								margin-bottom: 10rpx;
								font-size: 24rpx;
							}
						}
					}
				}

				&-top {
					height: 60rpx;
					display: flex;
					align-items: center;
					padding: 0 10rpx 0 40rpx;
					position: relative;

					&-bg {
						position: absolute;
						z-index: 1;
						width: 100%;
						height: 60rpx;
						left: 0;
						top: 0;
					}

					&-ct {
						position: relative;
						z-index: 2;
						width: 100%;
						height: 100%;
						display: flex;
						align-items: center;
						justify-content: space-between;

						&-left {
							color: #FDFDFD;
							font-size: 30rpx;
							display: flex;
							padding-top: 24rpx;

							>image {
								width: 22rpx;
								height: 22rpx;
								margin-left: 10rpx;
							}
						}

						&-right {
							display: flex;
							align-items: center;
							color: #4CB4FE;
							font-size: 28rpx;

							>image {
								width: 20rpx;
								height: 20rpx;
								margin-left: 10rpx;
							}
						}
					}
				}
			}


			&-data {
				height: 213rpx;
				background: linear-gradient(180deg, #e7f5ff, #eef8fe 23%, #fdfdfd 99%);
				border-radius: 16rpx;
				padding: 30rpx;
				position: relative;


				&-dt {
					width: 100%;
					display: flex;
					align-items: center;
					margin-top: 30rpx;

					&-lt {
						// flex: 1;
						width: 400rpx;
						display: flex;
						align-items: center;
						justify-content: space-around;
						color: #666666;

						>view {
							flex: 1;
							display: flex;
							flex-direction: column;
							align-items: center;
							justify-content: center;

							>view:nth-child(1) {
								font-size: 36rpx;
								font-weight: bold;
								margin-bottom: 6rpx;
							}

							>view:nth-child(2) {
								font-size: 24rpx;
							}
						}
					}

					&-rt {
						position: absolute;
						width: 240rpx;
						height: 110rpx;
						z-index: 15;
						top: 50rpx;
						right: 10rpx;
						// transform: translateY(-30rpx);

					}
				}



			}
		}



		&-top {
			padding: 0 30rpx;
			height: 558rpx;
			position: relative;
			z-index: 1;
			padding-top: 340rpx;

			&-bg {
				position: absolute;
				z-index: 4;
				width: 750rpx;
				height: 558rpx;
				left: 0;
				top: 0;
			}

			&-cnt {
				position: relative;
				z-index: 6;
			}




			&-desc {
				padding: 0 24rpx;
				height: 40rpx;
				width: fit-content;
				display: flex;
				align-items: center;
				font-size: 24rpx;
				color: #FFFFFF;
				background: #95d3ff;
				border-radius: 100rpx;

				text {
					color: #28A6FF;
					margin: 0 8rpx;
				}
			}


		}
	}
</style>