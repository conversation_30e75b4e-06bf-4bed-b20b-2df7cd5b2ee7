<template>
	<view class="revenueDetail">
		<view class="revenueDetail-top">
			<view v-for="(item,index) in tabList" :key="index" :class="{sel:selectTab === index}"
				@click="changeSelectTab(index)">
				<template v-if="index != 2">
					{{item.text}}
				</template>
				<template v-else>
					{{selectTimeRange.startDate ? `${selectTimeRange.startDate} ${selectTimeRange.endDate}` : item.text}}
				</template>
			</view>
		</view>
		<view class="revenueDetail-container">
			<template v-if="list.length > 0">
				<detailInfo v-for="(item,index) in list" :info="item" :key="index"></detailInfo>
			</template>
			<empty v-else></empty>

		</view>
		<mxDatepicker :show="showPicker" format="yyyy-mm-dd" type="rangetime" @confirm="datePickerConfirm"
			@cancel="showPicker = false" />
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		watch
	} from 'vue';
	import {
		listGet,
		navTo
	} from '@/hooks';
	import detailInfo from './components/detailInfo/detailInfo.vue';
	const showPicker = ref(false)
	const tabList = [{
		text: '近7天',
		value: '7'
	}, {
		text: '近30天',
		value: '30'
	}, {
		text: '自定义',
		value: ''
	}]
	const selectTab = ref(0)
	const selectTimeRange = ref({
		dateRange: tabList[selectTab.value].value,
	})
	const paramsOptions = computed(() => ({
		...selectTimeRange.value
	}))

	const changeSelectTab = (i) => {
		if (i == 2) {
			showPicker.value = true
		} else {
			selectTab.value = i
			selectTimeRange.value = {
				dateRange: tabList[selectTab.value].value,
			}
		}
	}

	const {
		mode,
		list,
		refresh: listRefresh
	} = listGet({
		apiUrl: uni.api.distributionOrders,
		options: paramsOptions,
		pdIsLogin: true,
		isReqTypeReq: false
	})
	const datePickerConfirm = (e) => {
		showPicker.value = false
		selectTimeRange.value = {
			startDate: e.value[0],
			endDate: e.value[1],
			dateRange: ""
		}
		selectTab.value = 2
	}


	watch(paramsOptions, () => {
		listRefresh()
	}, {
		immediate: true,
		deep: true
	})
</script>

<style lang="scss">
	page {
		background-color: #F2F2F2;
	}

	.revenueDetail {
		.sel {
			background-color: #22a3ff !important;
			color: white !important;
			box-shadow: 0 4rpx 8rpx rgba(34, 163, 255, 0.2);
		}

		&-container {
			padding: 30rpx;
		}

		&-top {
			width: 750rpx;
			height: 100rpx;
			background-color: white;
			position: sticky;
			top: 0;
			left: 0;
			z-index: 5;
			display: flex;
			align-items: center;
			padding-left: 40rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			>view {
				height: 60rpx;
				line-height: 60rpx;
				background-color: #f6f6f8;
				border-radius: 30rpx;
				padding: 0 24rpx;
				font-size: 26rpx;
				color: #333333;
				margin-right: 20rpx;
				transition: all 0.3s ease;
			}
		}
	}
</style>