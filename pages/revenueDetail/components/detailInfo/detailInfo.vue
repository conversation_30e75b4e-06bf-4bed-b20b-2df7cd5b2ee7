<template>
	<view class="detailInfo">
		<view class="detailInfo-line detailInfo-line-header">
			<text class="user-nickname">{{ info.memberNickName }}</text>
			<text class="action-text">下单了</text>
		</view>
		<view class="detailInfo-line">
			<text class="detailInfo-line-label">商品名称：</text>{{ info.goodName }}
		</view>
		<view class="detailInfo-line">
			<text class="detailInfo-line-label">助力值：</text>{{ info.actuallyReceivedAmount }}
		</view>
		<view class="detailInfo-line" style="border-bottom: none">
			<text class="detailInfo-line-label">下单时间：</text>{{ info.createTime }}
		</view>
		<view class="detailInfo-line" style="border-bottom: none"> </view>
		<view class="detailInfo-line lastLine" v-if="info.commissionStatus == '1'">
			<image src="@/static/revenueDetail/i.png" style="width: 26rpx; height: 26rpx; margin-right: 22rpx"></image>
			订单已确认：+{{ info.confirmedCommission }}助力
		</view>
		<view class="detailInfo-line lastLine unconfirmed-line" v-if="info.commissionStatus == '0'">
			<image src="@/static/revenueDetail/i.png" style="
          width: 26rpx;
          height: 26rpx;
          margin-right: 22rpx;
          filter: sepia(100%);
        "></image>
			订单未确认：+{{ info.unconfirmedCommission }}助力
		</view>
	</view>
</template>

<script lang="ts" setup>
	const props = defineProps({
		info: {
			type: Object,
			default: () => ({}),
		},
	});
</script>

<style lang="scss">
	.detailInfo {
		background-color: white;
		border-radius: 16rpx;
		overflow: hidden;
		padding: 0 30rpx;
		position: relative;
		margin-bottom: 30rpx;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

		.lastLine {
			border-bottom: none;
			color: #f54301;
			background: linear-gradient(97deg, #fffbf8 0%, #fff3f2 100%);
			position: absolute;
			left: 0;
			bottom: 0;
			width: 100%;
			padding: 0 30rpx;
		}

		.unconfirmed-line {
			color: #B7950B !important; /* 深黄褐色文字 */
			background: linear-gradient(97deg, #FFFEF0 0%, #FFFBE5 100%) !important; /* 更淡的黄色渐变背景 */
		}

		&-line {
			min-height: 90rpx;
			display: flex;
			align-items: center;
			border-bottom: 1rpx solid #e5e5e5;
			font-size: 28rpx;
			color: #333333;
			padding: 16rpx 0;

			&-label {
				color: #666666;
				min-width: 140rpx;
				font-weight: 500;
			}
		}
		
		&-line-header {
			padding-top: 24rpx;
			
			.user-nickname {
				font-weight: bold;
				margin-right: 14rpx;
				color: #22a3ff;
			}
			
			.action-text {
				color: #333333;
			}
		}
	}
</style>
