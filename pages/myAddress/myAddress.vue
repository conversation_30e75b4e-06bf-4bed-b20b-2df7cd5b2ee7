<template>
	<view class="myAddress">
		<view class="address-item" hover-class="item-hover" v-for="(item,index) in list" :key="index" @click="selectAddresss(item)">
			<view class="address-content">
				<view class="address-info">
					<view class="address-detail">{{ item.areaExplan }}{{ item.areaAddress }}</view>
					<view class="address-contact">
						<text class="contact-name">{{ item.linkman }}</text>
						<text class="contact-phone">{{ item.phone }}</text>
						<view class="default-tag" v-if="item.isDefault == 1">默认</view>
					</view>
				</view>

				<view class="address-actions">
					<view class="action-btn edit" @click.stop="navTo(`/pages/opAddress/opAddress${parseObjToPath(item)}`)">
						<image src="@/static/myAddress/edit.png" mode="aspectFit"></image>
						<text>编辑</text>
					</view>
					<view class="action-divider"></view>
					<view class="action-btn delete" @click.stop="showDeleteModal(item)">
						<image src="@/static/myAddress/delete.png" mode="aspectFit"></image>
						<text>删除</text>
					</view>
				</view>
			</view>
		</view>
		<empty v-if='!list?.length'></empty>

		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='温馨提示' content="您确定要删除该地址吗" :duration="2000"
				:before-close="false" @confirm="deleteAdd"></uni-popup-dialog>
		</uni-popup>
		<view class="add-address-btn" hover-class="btn-hover" @click="navTo('/pages/opAddress/opAddress')">
			<text>新增地址</text>
		</view>
	</view>
</template>

<script setup>
	import {
		onShow,
		onLoad
	} from "@dcloudio/uni-app"
	import {
		navTo,
		toUpPage
	} from '@/hooks';
	import {
		parseObjToPath
	} from '@/utils'
	import {
		ref
	} from "vue";
	import {
		userStore
	} from '@/store';
	const users = userStore()
	let list = ref([]),
		popup = ref(),
		deleteItem = {}, //要删除的地址信息
		options = ref({});
	onShow(() => {
		refresh()
	})
	onLoad((o) => {
		options.value = o
	})
	async function refresh() {
		if (users.token) {
			let {
				data
			} = await uni.http.get(uni.api.getAddress);
			list.value = data.result.records
		}
	}

	function showDeleteModal(item) {
		deleteItem = item
		popup.value.open()
	}
	//删除地址
	async function deleteAdd() {
		uni.showLoading({
			mask: true
		})
		await uni.http.get(uni.api.delAddress, {
			params: {
				...deleteItem,
			}
		});
		refresh()
		uni.showToast({
			title: '操作成功~',
			icon: 'none'
		})
	}

	//选择地址
	function selectAddresss(item) {
		if (options.value.type == 'select') {
			toUpPage()
			//触发全局自定义事件,选择地址,在对应的页面监听
			uni.$emit('selectAddresss', item)
		} else {
			navTo(`/pages/opAddress/opAddress${parseObjToPath(item)}`)
		}
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.myAddress {
		padding: 30rpx;
		padding-bottom: 140rpx;
	}

	.address-item {
		margin-bottom: 24rpx;
		border-radius: 16rpx;
		background-color: #ffffff;
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		overflow: hidden;
		transition: transform 0.2s ease;
	}

	.item-hover {
		transform: scale(0.98);
		opacity: 0.9;
	}

	.address-content {
		padding: 30rpx;
	}

	.address-info {
		margin-bottom: 24rpx;
	}

	.address-detail {
		font-size: 30rpx;
		color: #333333;
		font-weight: 500;
		line-height: 1.5;
		margin-bottom: 16rpx;
	}

	.address-contact {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
	}

	.contact-name {
		font-size: 28rpx;
		color: #666666;
		margin-right: 16rpx;
	}

	.contact-phone {
		font-size: 28rpx;
		color: #666666;
	}

	.default-tag {
		margin-left: 16rpx;
		padding: 4rpx 12rpx;
		background-color: #E6F7FF;
		border: 1rpx solid #22A3FF;
		border-radius: 8rpx;
		font-size: 24rpx;
		color: #22A3FF;
	}

	.address-actions {
		display: flex;
		align-items: center;
		padding-top: 24rpx;
		border-top: 1rpx solid #F5F5F5;
	}

	.action-btn {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 12rpx 0;

		image {
			width: 32rpx;
			height: 32rpx;
			margin-right: 8rpx;
		}

		text {
			font-size: 28rpx;
			color: #666666;
		}
	}

	.action-btn.edit {
		color: #22A3FF;
	}

	.action-btn.delete {
		color: #FF4D4F;
	}

	.action-divider {
		width: 1rpx;
		height: 40rpx;
		background-color: #F0F0F0;
	}

	.add-address-btn {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		width: calc(100% - 60rpx);
		background-color: #22A3FF;
		box-shadow: 0 4rpx 10rpx rgba(34, 163, 255, 0.2);
		z-index: 10;

		display: flex;
		align-items: center;
		justify-content: center;
		height: 90rpx;
		border-radius: 45rpx;
		transition: opacity 0.2s ease;

		text {
			font-size: 32rpx;
			color: #ffffff;
			font-weight: 500;
		}
	}

	.btn-hover {
		opacity: 0.8;
	}
</style>