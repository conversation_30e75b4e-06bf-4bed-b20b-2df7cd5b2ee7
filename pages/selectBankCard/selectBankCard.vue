<template>
	<view class="selectBankCard">
		<view class="selectBankCard-view" v-for="(item,index) in list" :key="index" @click="changeSelectedIndex(index)">
			<!-- <image class="selectBankCard-view-icon" src="@/static/index/btn1.png"></image> -->
			<view class="selectBankCard-view-right">
				<view>
					{{item.cardholder}} {{item.bankName}}
				</view>
				<view>
					{{item.bankCard}}
				</view>
			</view>
			<view class="selectBankCard-view-noSel" :class="{selected:index === selectIndex}">

			</view>
		</view>
		<view class="selectBankCard-btn">
			<view @click="sure" v-if="list?.length">
				确定
			</view>
			<view @click="redTo('/packageUser/pages/myBankCard/myBankCard')" v-else>
				添加银行卡
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		navTo,
		redTo,
		listGet,
		toUpPage
	} from '@/hooks';
	const {
		mode,
		list,
		refresh
	} = listGet({
		apiUrl: uni.api.returnMemberBankCard,
		reqType: 1
	})
	const selectIndex = ref(0)

	function changeSelectedIndex(index) {
		selectIndex.value = index
	}

	function sure() {
		const itm = list.value[selectIndex.value]
		if (!itm) {
			uni.showToast({
				title: '请选择银行卡',
				icon: 'none'
			})
			return;
		}
		toUpPage()
		//触发全局自定义事件,选择银行卡,在对应的页面监听
		uni.$emit('selectBankCard', itm)
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.selectBankCard {
		padding: 30rpx;

		.selected {
			border: 2rpx solid #45BB66 !important;
			background-color: #45BB66 !important;
		}

		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 5;
			width: 750rpx;
			padding: 30rpx;
			background-color: white;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				text-align: center;
				color: white;
				font-size: 30rpx;
				background-color: #22A3FF;
				border-radius: 16rpx;
			}
		}

		&-view {
			padding: 30rpx;
			border-radius: 16rpx;
			background-color: white;
			display: flex;
			position: relative;
			margin-bottom: 30rpx;

			&-noSel {
				position: absolute;
				width: 30rpx;
				height: 30rpx;
				border: 2rpx solid #000000;
				border-radius: 50%;
				right: 50rpx;
				top: 64rpx;
				z-index: 10;
			}

			&-sel {
				position: absolute;
				width: 30rpx;
				height: 30rpx;
				background-color: #45BB66;
				border-radius: 50%;
				right: 50rpx;
				top: 64rpx;
				z-index: 10;
			}

			&-icon {
				width: 30rpx;
				height: 30rpx;
				margin-right: 14rpx;
			}

			&-right {
				>view:nth-child(1) {
					color: #333333;
					font-size: 30rpx;
					line-height: 30rpx;
					margin-bottom: 30rpx;
				}

				>view:nth-child(2) {
					font-size: 28rpx;
					color: #666666;
				}
			}
		}
	}
</style>