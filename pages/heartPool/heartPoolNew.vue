<template>
  <view class="page">
    <view class="search-container">
      <view class="search-box" @click="navTo('/packageSearch/pages/search/search')">
        <uni-icons :size="16" color="#AAAAAA" type="search" class="search-icon"/>
        <view class="search-placeholder">搜索您想资助的产品</view>
      </view>
    </view>
    <cartAction></cartAction>
    <view class="tab-container">
      <view
        v-for="(tab, index) in tabs"
        :key="index"
        class="tab-item"
        :class="{ active: currentTab === index }"
        @click="switchTab(index)"
      >
        {{ tab }}
        <view v-if="currentTab === index" class="tab-line"/>
      </view>
    </view>
    <scroll-view
      class="shop-list"
      scroll-y
      @scrolltolower="loadMore"
    >
      <view
        v-for="(shop, index) in formatShopList"
        :key="index"
        class="shop-card"
        @click="onCardClick(shop)"
      >
        <view class="shop-header">
          <image class="shop-logo" :src="shop.logo" mode="aspectFill"/>
          <view class="shop-title">
            <text class="shop-name">{{ shop.name }}</text>
            <view class="shop-info-right">
              <view class="shop-score">{{ shop.score }}分</view>
              <view class="shop-level">{{ shop.levelName || 'Lv.' + shop.level }}</view>
            </view>
          </view>
        </view>
        <view class="shop-info">
          <view class="location-type">
            <uni-icons :size="14" color="#666666" type="location" />
            <text class="city">{{ shop.city }}</text>
            <uni-icons :size="14" color="#666666" type="shop" />
            <text class="type">{{ shop.type }}</text>
          </view>
        </view>
        <view v-if="shop.tags && shop.tags.length" class="shop-tags">
          <text
            v-for="(tag, tagIndex) in shop.tags.slice(0, 2)"
            :key="tagIndex"
            class="tag"
          >{{ tag }}</text>
        </view>
        <view v-if="shop.description" class="shop-description">
          {{ shop.description }}
        </view>
        <view class="shop-contact" @click.stop="onContactClick($event, shop)">
          <uni-icons :size="16" color="#4788F2" type="phone" />
          <text class="contact-text">联系</text>
        </view>
      </view>
      <view v-if="mode === 'loading'" class="loading">
        <uni-icons :size="20" color="#4788F2" type="spinner-cycle" />
        <text class="loading-text">加载中...</text>
      </view>
      <view v-if="mode === 'noMore' && formatShopList.length > 0" class="no-more">
        <text class="no-more-text">没有更多数据了</text>
      </view>
      <view v-if="formatShopList.length === 0 && mode !== 'loading'" class="empty">
        <text class="empty-text">暂无店铺数据</text>
      </view>
    </scroll-view>
  </view>
</template>
    <script setup>
import { ref, computed, onMounted } from 'vue';
import { listGet, navToStoreIndex, navTo } from '@/hooks';
import { onShow } from '@dcloudio/uni-app';
import { cartCountHooks } from '@/hooks/getCartCount.js';
import cartAction from '@/components/cartAction/cartAction.vue';

const imgUrl = uni.env.IMAGE_URL;

// 搜索相关
// 点击搜索框跳转到搜索页面

// 标签页相关
const currentTab = ref(0);
const tabs = ['品牌馆', '生活馆', '创业馆'];
const storeTypeList = [15, 16, 17]; // 对应品牌馆、生活馆、创业馆的storeType值

const switchTab = (index) => {
  if (currentTab.value === index) return;
  currentTab.value = index;
  // 切换标签时重新加载数据
  searchOptions.value.storeType = storeTypeList[index];
  refresh();
};

// 店铺列表相关

// 搜索参数
const searchOptions = ref({
  storeType: storeTypeList[currentTab.value],
  pattern: 0
});

// 使用listGet获取店铺列表数据
const {
  list,
  mode,
  refresh
} = listGet({
  apiUrl: uni.api.findStoreManageList,
  options: searchOptions,
  isReqTypeReq: false, // 不自动在onLoad或onShow时请求
  isWatchOptions: false // 不监听options变化自动请求
});

// 将API返回的数据映射到UI需要的格式
const formatShopList = computed(() => {
  if (!list.value || !list.value.length) return [];

  return list.value.map(shop => {
    // 保留原始数据结构，只添加UI显示需要的字段
    return {
      ...shop, // 保留原始数据的所有字段
      // 为UI显示增加的字段
      logo: imgUrl + shop.logoAddr,
      name: shop.storeName,
      level: shop.storeLevel || 0,
      levelName: shop.storeLevelName || '',
      score: shop.totalPerformance || 0,
      tags: [shop.mainTypeName, shop.storeTypeName].filter(Boolean),
      description: shop.introduce || '',
    };
  });
});

// 加载更多数据
const loadMore = () => {
  if (mode.value === 'loading' || mode.value === 'noMore') return;
  refresh(2); // 调用refresh的第二个模式，即加载更多
};

// 点击店铺卡片
const onCardClick = (shop) => {
  // 直接将整个shop对象传递给navToStoreIndex函数，与原页面保持一致
  navToStoreIndex(shop);
};

// 点击联系按钮
const onContactClick = (event, shop) => {
  event.stopPropagation(); // 阻止事件冒泡，避免触发卡片点击

  if (shop.phone) {
    uni.makePhoneCall({
      phoneNumber: shop.phone,
      fail: () => {
        uni.showToast({
          title: '拨打电话失败',
          icon: 'none'
        });
      }
    });
  } else {
    uni.showToast({
      title: '暂无联系电话',
      icon: 'none'
    });
  }
};

onMounted(() => {
  // 页面加载完成后初始化数据
  refresh();
});

onShow(() => {
  // 页面显示时刷新购物车数量
  const { cartCountRefresh } = cartCountHooks();
  cartCountRefresh();
});
</script>
    <style>
page {
  height: 100%;
  background-color: #F5F5F5;
}

.page {
  height: 100%;
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
}

/* 搜索区域样式增强 */
.search-container {
  padding: 16rpx 30rpx 20rpx;
  background: linear-gradient(to bottom, #FFFFFF, #F8F8F8);
  flex-shrink: 0;
  position: relative;
  z-index: 10;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.search-box {
  height: 72rpx;
  background-color: #FFFFFF;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  padding: 0 20rpx;
  position: relative;
  box-sizing: border-box;
  box-shadow: inset 0 1rpx 6rpx rgba(0, 0, 0, 0.05);
  border: 1rpx solid #EEEEEE;
}

.search-placeholder {
  flex: 1;
  height: 72rpx;
  font-size: 28rpx;
  color: #999999;
  margin-left: 10rpx;
  line-height: 72rpx;
}

.search-icon {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 标签页样式增强 */
.tab-container {
  height: 88rpx;
  display: flex;
  background-color: #FFFFFF;
  flex-shrink: 0;
  position: relative;
  z-index: 9;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.tab-container::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(to right, transparent, #E0E0E0, transparent);
}

.tab-item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  transition: all 0.3s;
}

.tab-item.active {
  color: #4788F2;
  font-weight: 600;
  background: linear-gradient(180deg, rgba(71, 136, 242, 0.05) 0%, rgba(71, 136, 242, 0) 100%);
}

.tab-line {
  position: absolute;
  bottom: 0;
  width: 48rpx;
  height: 4rpx;
  background: linear-gradient(to right, #4788F2, #6AA1FF);
  border-radius: 2rpx;
  transition: all 0.3s;
}

/* 店铺列表样式增强 */
.shop-list {
  flex: 1;
  overflow: auto;
  padding: 24rpx 30rpx;
  box-sizing: border-box;
  background-color: #F5F5F5;
  position: relative;
}

.shop-card {
  background-color: #FFFFFF;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
  position: relative;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
}

.shop-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.12);
}

.shop-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(to bottom, #4788F2, #6AA1FF);
  border-radius: 3rpx 0 0 3rpx;
}

.shop-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
}

.shop-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  border: 2rpx solid #FFFFFF;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
  object-fit: cover;
}

.shop-title {
  flex: 1;
  margin-left: 24rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.shop-name {
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 300rpx;
  background: linear-gradient(to right, #333333, #666666);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
}

.shop-info-right {
  display: flex;
  align-items: center;
}

.shop-score {
  font-size: 24rpx;
  color: #FF6B00;
  margin-right: 12rpx;
  font-weight: 500;
}

.shop-level {
  padding: 4rpx 14rpx;
  background: #4788F2;
  border-radius: 8rpx;
  color: #FFFFFF;
  font-size: 22rpx;
}

.shop-info {
  margin-bottom: 16rpx;
  position: relative;
}

.location-type {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #666666;
}

.city,
.type {
  margin: 0 12rpx 0 4rpx;
}

.shop-tags {
  display: flex;
  margin-bottom: 16rpx;
  flex-wrap: wrap;
}

.tag {
  padding: 6rpx 14rpx;
  background: #EBF3FF;
  color: #4788F2;
  font-size: 22rpx;
  border-radius: 8rpx;
  margin-right: 12rpx;
  margin-bottom: 8rpx;
}

.shop-description {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.6;
  margin-bottom: 24rpx;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  overflow: hidden;
  position: relative;
  padding-left: 10rpx;
  border-left: 2rpx solid #EEEEEE;
}

.shop-contact {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
}

.shop-contact::before {
  content: '';
  position: absolute;
  top: -12rpx;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(to right, transparent, #EEEEEE, transparent);
}

.contact-text {
  color: #4788F2;
  font-size: 26rpx;
  margin-left: 6rpx;
  font-weight: 500;
}

.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-text {
  color: #999999;
  font-size: 26rpx;
  margin-left: 12rpx;
}

.no-more, .empty {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.no-more-text, .empty-text {
  color: #999999;
  font-size: 26rpx;
}

.empty {
  padding: 100rpx 0;
}

/* 适配不同机型 */
@media screen and (max-width: 320px) {
  .shop-name {
    max-width: 240rpx;
  }

  .shop-logo {
    width: 100rpx;
    height: 100rpx;
    border-radius: 50%;
  }

  .shop-level {
    padding: 2rpx 10rpx;
    font-size: 20rpx;
  }
}

@media screen and (min-width: 768px) {
  .shop-name {
    max-width: 360rpx;
  }

  .shop-card {
    padding: 32rpx;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10rpx); }
  to { opacity: 1; transform: translateY(0); }
}

.shop-card {
  animation: fadeIn 0.3s ease-out;
  animation-fill-mode: both;
}

.shop-card:nth-child(2) {
  animation-delay: 0.1s;
}

.shop-card:nth-child(3) {
  animation-delay: 0.2s;
}

.shop-card:nth-child(n+4) {
  animation-delay: 0.3s;
}
</style>
