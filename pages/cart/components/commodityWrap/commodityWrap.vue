<template>
	<view class="commodityWrap" @click="toDetail">
		<view>
			<image v-if="props.infos.mainPicture" :src="imgUrl + parseImgurl(props.infos.mainPicture)?.[0]" mode="">
			</image>
			<image v-else-if="props.infos.goodMainPicture" :src="imgUrl + parseImgurl(props.infos.goodMainPicture)?.[0]"
				mode="">
			</image>
		</view>
		<view>
			<view class="title">
				<view class="truncate line-clamp-1">
					{{props.infos.goodName}}
				</view>
			</view>
			<view class="desc truncate line-clamp-1" v-if="props.infos.specification || props.infos.goodSpecification">
				规格：{{props.infos.specification || props.infos.goodSpecification}}
			</view>
			<view class="priLab" v-if='showPriLab'>
				<template v-if="('opinion' in props.infos) && props.infos.opinion != 1">
					<view>该地区暂不支持配送</view>
				</template>
				<template v-else>
					<view v-if="props.showPrice">
						<image src="@/static/index/i_1.png" mode=""></image>
						{{props.priceKeySefc}}{{props.infos[props.priceKey]}}
					</view>
					<view v-if="props.infos.label">
						{{props.infos.label}}
					</view>
				</template>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		parseImgurl
	} from '@/utils'
	import {
		navTo,
		navToGoodDetail
	} from '@/hooks';
	import {
		computed, toRefs
	} from 'vue'
	const props = defineProps({
		infos: {
			type: Object,
			default () {
				return {}
			}
		},
		//是否要点击去详情
		isToDetail: {
			type: Boolean,
			default: true
		},
		//价格键名
		priceKey: {
			type: String,
			default: 'price'
		},
		//价格前面的前缀
		priceKeySefc: {
			type: String,
			default: '¥'
		},
		//是否显示价格
		showPrice: {
			type: Boolean,
			default: true
		},
		//右侧物品信息容器高度
		rightWrapHeight:{
			type:String,
			default:'180rpx'
		}
	})
	const {
		rightWrapHeight
	} = toRefs(props)
	const imgUrl = uni.env.IMAGE_URL;
	const showPriLab = computed(() => {
		return ('opinion' in props.infos) && props.infos.opinion != 1 || (props.showPrice || props.infos.label)
	})

	function toDetail() {
		if (props.isToDetail) {
			navToGoodDetail(props.infos)
		}

	}
</script>

<style lang="scss">
	.commodityWrap {
		width: 100%;
		display: flex;
		// align-items: center;
		justify-content: space-between;

		>view:nth-child(1) {
			margin-right: 20rpx;

			>image {
				width: 180rpx;
				height: 180rpx;
				border-radius: 20rpx;
			}
		}

		>view:nth-child(2) {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;
			height: v-bind(rightWrapHeight);
			overflow: hidden;

			.title {
				width: 100%;
				height: 42rpx;
				line-height: 42rpx;
				font-size: 30rpx;
				color: 0;
				text-align: justify;
			}

			.desc {

				font-size: 24rpx;
				color: #999999;
				text-align: justify;
				line-height: 40rpx;
				height: 40rpx;
				width: fit-content;
			}

			.priLab {
				display: flex;
				align-items: center;

				>view:nth-child(1) {
					// font-weight: bold;
					font-size: 28rpx;
					color: #E6A600;
					line-height: 44rpx;
					margin-right: 16rpx;
					display: flex;
					align-items: center;

					>image {
						width: 30rpx;
						height: 30rpx;
						margin-right: 4rpx;
					}
				}

				>view:nth-child(2) {
					padding: 3rpx 8rpx;
					background: #00BB80;
					border-radius: 4rpx;
					font-size: 18rpx;
					color: #FFFFFF;
					line-height: 18rpx;
				}
			}
		}

	}
</style>