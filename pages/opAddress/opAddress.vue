<template>
	<view class="opAddress">
		<view class="form-item">
			<view class="form-label">
				<text class="required-mark">*</text>
				<text>收货人</text>
			</view>
			<view class="form-input-wrap">
				<input class="form-input" type="text" v-model="datas.linkman" placeholder="请输入收货人姓名" placeholder-class="placeholder-style" />
			</view>
		</view>

		<view class="form-item">
			<view class="form-label">
				<text class="required-mark">*</text>
				<text>联系电话</text>
			</view>
			<view class="form-input-wrap">
				<input class="form-input" type="number" v-model="datas.phone" placeholder="请输入收货人联系电话" placeholder-class="placeholder-style" />
			</view>
		</view>

		<view class="form-item address-select" hover-class="item-hover" @click="showAddressModal">
			<view class="form-label">
				<text class="required-mark">*</text>
				<text>选择地址</text>
			</view>
			<view class="form-select">
				<text class="select-text">{{datas.areaExplan ? datas.areaExplan : '请选择省市区'}}</text>
				<image class="select-arrow" src="@/static/right-arrow-gray.png" mode="aspectFit"></image>
			</view>
		</view>

		<view class="form-item">
			<view class="form-label">
				<text class="required-mark">*</text>
				<text>详细地址</text>
			</view>
			<view class="form-input-wrap">
				<input class="form-input" type="text" v-model="datas.areaAddress" placeholder="请输入详细地址" placeholder-class="placeholder-style" />
			</view>
		</view>

		<view class="form-item">
			<view class="form-label">
				<text>是否默认</text>
			</view>
			<view class="form-switch">
				<switch :checked='datas.isDefault === "1"' @change="switchChange" color="#22A3FF" />
			</view>
		</view>

		<view class="save-button" hover-class="button-hover" @click="submit">
			<text>保存信息</text>
		</view>

		<linkAddress ref="linkAddresses" height="550rpx" @confirmCallback="confirmCallback"></linkAddress>
	</view>
</template>

<script setup>
	import {
		onLoad
	} from "@dcloudio/uni-app"
	// import {
		// getSafeBottom
	// } from '@/utils/index.js'
	import {
		ref
	} from "vue";
	import {
		toUpPage
	} from "@/hooks";
	let datas = ref({
		isDefault: 1
	});
	let linkAddresses = ref();
	let title = ref('新增收货地址')
	onLoad((options) => {
		if (options.id) {
			uni.setNavigationBarTitle({
				title: '编辑收货地址'
			})
			title.value = '编辑收货地址'
			datas.value = options

		}
	})
	const switchChange = (e) => {
		datas.value.isDefault = e.detail.value ? '1' : '0'
	}
	// 设置是否默认 - 当前使用switchChange代替此功能
	// function changeDefault() {
	// 	if (!datas.value.isDefault * 1) {
	// 		datas.value.isDefault = 1
	// 	} else {
	// 		datas.value.isDefault = 0
	// 	}
	// }
	// 选择地址回调回调
	function confirmCallback(data) {
		if (!data.userAddress.province) return;
		if (
			data.userAddress.province &&
			(data.userAddress.province == '北京' || data.userAddress.province == '天津' || data.userAddress.province == '上海' ||
				data.userAddress.province == '重庆')
		) {
			datas.value.areaExplan = `${data.userAddress.province}市${data.userAddress.city}区${data.userAddress.district}`

		} else {
			datas.value.areaExplan =
				`${data.userAddress.province}${data.userAddress.province ? '省' : ''}${data.userAddress.city}${data.userAddress.city ? '市' : ''}${data.userAddress.district}`
		}
		let areaExplanIds = '';
		areaExplanIds += data.submission.province;
		if (data.submission.city) {
			areaExplanIds += ',' + data.submission.city;
			if (data.submission.county) {
				areaExplanIds += ',' + data.submission.county;
			}
		}
		datas.value.areaExplanIds = areaExplanIds
		datas.value.sysAreaId = `${data.submission.province}`
	}
	//显示地址弹窗
	function showAddressModal() {
		linkAddresses.value.show()
	}
	//提交地址
	async function submit() {
		let data = datas.value;
		if (!data.linkman) {
			uni.showToast({
				icon: "none",
				title: '请填写收货人姓名'
			})
			return;
		}
		if (data.linkman.length < 2) {
			uni.showToast({
				icon: "none",
				title: '请填写正确的收货人姓名'
			})
			return;
		}
		if (!/^1[3456789]\d{9}$/.test(data.phone)) {
			uni.showToast({
				icon: "none",
				title: '请输入正确的手机号码'
			})
			return;
		}
		if (!data.areaExplan) {
			uni.showToast({
				icon: "none",
				title: '请设置收货区域'
			})
			return;
		}
		if (!data.areaAddress) {
			uni.showToast({
				icon: "none",
				title: '请填写详细地址'
			})
			return;
		}
		uni.showLoading({
			mask: true
		})
		await getLonAndLat();
		await uni.http.get(uni.api.addAddress, {
			params: datas.value
		});
		uni.showToast({
			icon: "none",
			title: '操作成功~'
		})
		setTimeout(() => {
			toUpPage()
		}, 800)
	}
	//获取经纬度
	function getLonAndLat() {
		let data = datas.value;
		return new Promise(async (resolve) => {
			let areaExplan = data.areaExplan;
			if (!areaExplan) {
				uni.showToast({
					icon: "none",
					title: '请设置收货区域'
				})
				return;
			}
			let info = {
				address: areaExplan + data.areaAddress,
				pageSize: 1,
				pageIndex: 1
			};
			let {
				data: res
			} = await uni.http.get(uni.api.getAddsress, {
				params: info
			});
			data.latitude = res.result?.[0]?.location?.lat || ''; //维度
			data.longitude = res.result?.[0]?.location?.lng || ''; //经度
			resolve('success');
		});
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.opAddress {
		padding: 0;
	}

	.form-item {
		display: flex;
		align-items: center;
		padding: 28rpx 30rpx;
		background-color: #FFFFFF;
		border-bottom: 1rpx solid #F0F0F0;
	}

	.form-label {
		width: 140rpx;
		display: flex;
		align-items: center;
		font-size: 28rpx;
		color: #333333;
		font-weight: 500;
		justify-content: flex-end;
	}

	.required-mark {
		color: #FF4D4F;
		margin-right: 8rpx;
	}

	.form-input-wrap {
		flex: 1;
		margin-left: 20rpx;
	}

	.form-input {
		width: 100%;
		height: 60rpx;
		font-size: 28rpx;
		color: #333333;
	}

	.placeholder-style {
		color: #999999;
		font-size: 28rpx;
	}

	.form-select {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 60rpx;
		margin-left: 20rpx;
	}

	.select-text {
		font-size: 28rpx;
		color: #333333;
	}

	.select-arrow {
		width: 32rpx;
		height: 32rpx;
		transition: transform 0.2s;
	}

	.address-select {
		position: relative;

		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background-color: transparent;
			transition: background-color 0.2s;
		}
	}

	.item-hover {
		background-color: #F9F9F9;

		.select-arrow {
			transform: translateX(4rpx);
		}
	}

	.form-switch {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		margin-left: 20rpx;
	}

	.save-button {
		position: fixed;
		bottom: 30rpx;
		left: 30rpx;
		right: 30rpx;
		width: calc(100% - 60rpx);
		background-color: #22A3FF;
		box-shadow: 0 4rpx 10rpx rgba(34, 163, 255, 0.2);
		z-index: 10;

		display: flex;
		align-items: center;
		justify-content: center;
		height: 90rpx;
		border-radius: 45rpx;
		transition: opacity 0.2s ease;

		text {
			font-size: 32rpx;
			color: #ffffff;
			font-weight: 500;
		}
	}

	.button-hover {
		opacity: 0.8;
	}
</style>