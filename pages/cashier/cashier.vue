<template>
	<view class="cashier">
		<view class="cashier-top">
			<view>
				<text>¥</text>
				<text>{{info.allTotalPrice}}</text>
			</view>
			<view>
				支付金额
			</view>
		</view>

		<view class="cashier-bottom" style="margin-bottom: 30rpx;">
			<template v-if="isShowType(2)">
				<view class="cashier-bottom-payLine">
					<view>
						<image src="@/static/img/cashier/yue.png" mode=""></image>
						<view>
							账户余额
						</view>
					</view>
					<view>
						<input type="digit" class="cashier-bottom-payLine-ipt"
							:placeholder="'可用余额¥' + (info.balance || 0)" v-model="balance" @input="balanceChange"
							@focus="balanceFocus" />
						<image class="cashier-bottom-payLine-srw" src="@/static/img/cashier/sr.png" v-if="!balance">
						</image>
						<image class="cashier-bottom-payLine-srw" src="@/static/img/cashier/srSel.png" v-else></image>
					</view>
				</view>
				<view style="width: 95%;height: 1rpx;background: #EEEEEE;margin: 0 auto;">

				</view>
			</template>
			<view class="cashier-bottom-payLine" v-if="isShowType(3)">
				<view>
					<image src="@/static/img/cashier/flj.png" mode=""></image>
					<view>
						福利金
					</view>
				</view>

				<view>
					<input type="digit" class="cashier-bottom-payLine-ipt" @input="welfareChange"
						:placeholder="'可用福利金¥' +  + (info.welfarePayments || 0)" v-model="welfare" />
					<image class="cashier-bottom-payLine-srw" src="@/static/img/cashier/sr.png" v-if="!welfare"></image>
					<image class="cashier-bottom-payLine-srw" src="@/static/img/cashier/srSel.png" v-else></image>
					<view class="cashier-bottom-payLine-desc">
						抵扣金额：<text style="color: #FF3333;">-¥0.00</text>
					</view>
				</view>


			</view>
			<!-- <view class="cashier-bottom-payLine" v-if="mustToInputPassword">
				<view>
					<image src="@/static/img/cashier/jymm.png" mode=""></image>
					<view>
						交易密码
					</view>
				</view>
				<input v-model="code" type="password" class="cashier-bottom-payLine-ipt" style="padding-right: 0;"
					safe-password-length="6" maxlength="6" placeholder="请输入六位交易密码" />
			</view> -->
		</view>

		<view class="cashier-bottom">
			<view class="cashier-bottom-payLine" @click="changePayType('0')" v-if="isShowType(0)">
				<view>
					<image src="@/static/img/cashier/wxPay.png" mode=""></image>
					<view>
						微信支付
					</view>
				</view>
				<image v-if='payType == "0"' src="@/static/img/cashier/sel.png" mode=""></image>
				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
			</view>
			<!-- <view style="width: 95%;height: 1rpx;background: #EEEEEE;margin: 0 auto;">

			</view> -->
			<!-- <view class="cashier-bottom-payLine" @click="changePayType('1')">
				<view>
					<image src="@/static/img/cashier/aliPay.png" mode=""></image>
					<view>
						支付宝支付
					</view>
				</view>
				<image v-if='payType == "1"' src="@/static/img/cashier/sel.png" mode=""></image>
				<image v-else src="@/static/img/cashier/noSel.png" mode=""></image>
			</view> -->
		</view>


		<view class="cashier-placeholder btnOpePlaceholder">

		</view>
		<view class="cashier-btnOpe btnOpe">
			<view class="bottomFixBtn" @click="toInputPassword">
				{{ payText }}¥{{btnPrice}}
			</view>
		</view>
		<!-- 提示(交易密码) -->
		<uni-popup ref="passAtten" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='设置交易密码' content="您还未设置过交易密码,是否马上去设置？" :duration="2000"
				:before-close="false" @confirm="toSet" @close="toClose"></uni-popup-dialog>
		</uni-popup>
		<!-- 输入交易密码弹窗 -->
		<uni-popup ref="passwordInput" type="center">
			<view class="passwordInput">
				<view class="passwordInput-title">
					验证交易密码
				</view>
				<view>
					<myp-one :maxlength='6' :isPwd="true" type="box" size="80" :autoFocus="false" marginRight="10"
						@finish='passwordFinish'></myp-one>
				</view>
			</view>
		</uni-popup>

	</view>
</template>

<script setup>
	import {
		computed,
		ref
	} from "vue";
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		getSafeBottom
	} from '@/utils/index.js'
	import {
		authPhone,
		navTo,
		redTo,
		goPayDeal,
		toUpPage
	} from "@/hooks";
	import {
		frontSetting,
		userStore
	} from "@/store";
	let payType = ref('0'), //wx  zfb
		balance = ref(''), //余额
		welfare = ref(''), //福利金
		options = ref({}),
		info = ref({}),
		code = ref() //交易密码
	const passAtten = ref();
	const passwordInput = ref();
	const integralValue = frontSetting().setting.integralValue
	//结合payClassic使用   开始-----
	//请求页面的接口 
	const payApiList = [
		uni.api.submitOrder, //默认 商品
		uni.api.submitMarketingGiftBag, //礼包
		uni.api.toCashierDeskMarketingStoreGiftbag, //礼包团
		uni.api.balanceToCashierDesk, //余额充值
		uni.api.unpaidOrderSubmit, //待支付订单
		uni.api.submitCertificate, //兑换券支付
		uni.api.toCashierDeskMarketingStoreGiftCard //批发卡充值
	]
	//最终支付的接口
	const toPayedApiList = [
		uni.api.payOrderCarLog, //默认 商品
		uni.api.payMarketingGiftBagRecord, //礼包
		uni.api.payMarketingStoreGiftbag, //礼包团
		uni.api.blancePay, //余额充值
		uni.api.payOrderCarLog, //待支付订单
		uni.api.payCertificate, //兑换券支付
		uni.api.payMarketingStoreGiftCard //批发卡充值
	]
	//获取日志的键名
	const getPayOrderCarLogIdKeyList = [
		'payOrderCarLogId', //默认 商品
		'payGiftBagLogId', //礼包
		'payLogId', //礼包团
		'payBalanceLogId', //余额充值
		'payOrderCarLogId', //待支付订单
		'payCertificateLogId', //兑换券支付
		'payLogId', //批发卡支付
	]
	//结合payClassic使用   结束-----
	//支付方式(按钮文字显示)
	const payText = computed(() => {
		if (isShowType(0) && payType.value == '0') {
			return '微信'
		}
		if (isShowType(1) && payType.value == '1') {
			return '支付宝'
		}
		return '支付'
	})
	//获取系统配置:是否开启强制输入密码
	const mustToInputPassword = computed(() => {
		return frontSetting().setting.transactionPasswordState == 1;
	})
	//用户是否设置过交易密码
	const userSettedPassword = computed(() => {
		return userStore().userInfo.transactionPassword == 1;
	})
	//按钮中的价格(最终支付价格)
	const btnPrice = computed(() => {
		let btnPrice = info.value.allTotalPrice - (balance.value || 0) - ((welfare.value || 0) * integralValue)
		if (btnPrice < 0 || btnPrice == 'NaN') btnPrice = 0;
		return btnPrice && btnPrice.toFixed(2) || 0;
	})
	//日志id
	const payOrderCarLogId = computed(() => {
		if (!info.value.memberListId) return ''
		return info.value[getPayOrderCarLogIdKeyList[options.value.payClassic || 0]]
	})
	onLoad((op) => {
		if (op.orderJson) op.orderJson = decodeURIComponent(op.orderJson); //商品订单解析
		options.value = op
		refresh()
	})
	//输入交易密码完成
	async function passwordFinish(code) {
		uni.showLoading({
			mask: true
		})
		//如果设置了 校验是否正确
		let {
			data
		} = await uni.http.get(uni.api.srcTransactionPasswordValid, {
			params: {
				srcTransactionPassword: code
			}
		})
		passwordInput.value.close()
		toPay()
	}
	//余额聚焦时
	function balanceFocus() {
		if (!balance.value) {
			let val = info.value.balance
			//如果大于订单总价格 等于订单总价格
			if (val >= info.value.allTotalPrice) {
				val = info.value.allTotalPrice
			}
			balance.value = val;
			welfare.value = ''
		}
	}
	//余额的值变化
	function balanceChange(e) {
		const val = e.target.value;
		setTimeout(() => {
			//如果大于账户余额 等于账户余额
			if (val >= info.value.balance) {
				balance.value = info.value.balance
			}
			//如果大于订单总价格 等于订单总价格
			if (val >= info.value.allTotalPrice) {
				balance.value = info.value.allTotalPrice
			}
			//如果小于0则为0
			if (val < 0) {
				balance.value = 0
			}
			welfare.value = ''
		}, 10)
	}
	//福利金的值变化
	function welfareChange(e) {
		//福利金值
		const val = e.target.value;
		//福利金转化人民币
		const rmb = (val / integralValue).toFixed(2);
		//扣除余额后的人民币
		const lestRMB = info.value.allTotalPrice - balance.value
		setTimeout(() => {
			//如果大于扣除余额后剩余的人民币 则等于扣除余额后剩余的人民币
			if (rmb >= lestRMB) {
				welfare.value = (lestRMB * integralValue).toFixed(2)
			}
			//如果大于用户拥有的福利金 则等于用户拥有的福利金
			if (val >= info.value.welfare) {
				welfare.value = info.value.welfare
			}
			//如果小于0则为0
			if (val < 0) {
				welfare.value = 0
			}
		}, 10)
	}

	//改变支付渠道
	function changePayType(type = '') {
		payType.value = type
	}
	//去设置交易密码
	function toSet() {
		//跳转到设置密码
		// redTo()
		authPhone(1)
	}
	//取消去设置交易密码
	function toClose() {

	}

	//支付前交易密码相关
	async function toInputPassword() {
		//如果系统需要强制需要输入密码并且没有走人民币的支付（有微信或者支付宝或其他）并且不是兑换券兑换的订单
		if (mustToInputPassword.value && btnPrice.value * 1 <= 0 && options.value?.buyType != 3) {
			if (!userSettedPassword.value) {
				//如果用户没有设置交易密码  弹出提示
				passAtten.value.open()
			} else {
				//设置过交易密码  弹出交易密码弹窗
				passwordInput.value.open()
			}
		} else {
			uni.showLoading({
				mask: true
			})
			toPay()
		}
	}


	//支付
	async function toPay() {
		if (payText.value == '支付' && btnPrice.value * 1 > 0) {
			//如果微信和支付宝都不可支付，判断btnPrice是否为0  不为0则不可支付
			uni.showToast({
				title: '该订单只支持余额或福利金支付~',
				icon: 'none'
			})
			return;
		}
		let apiUrl = uni.api.payOrderCarLog;
		if (options.value.payClassic) {
			apiUrl = toPayedApiList[options.value.payClassic]
		}
		let reqInfo = {
			payOrderCarLogId: payOrderCarLogId.value,
			payModel: payType.value, //，0：微信支付；1：支付宝支付；
			memberListId: info.value.memberListId,
			welfarePayments: welfare.value || '',
			balance: balance.value || '',
			price: btnPrice.value,
			marketingStoreGiftCardMemberListId: options.value.marketingStoreGiftCardMemberListId || ''
		};
		let {
			data
		} = await uni.http.post(apiUrl, reqInfo);
		goPayDeal({
			data,
			id: options.value.id || ''
		}, options.value.payClassic)
	}
	//判断显示隐藏
	function isShowType(type) {
		return info.value.payModel && info.value.payModel.indexOf(type) != -1 ? true : false;
	}
	async function refresh() {
		uni.showLoading({
			mask: true
		})
		let apiUrl = uni.api.submitOrder //默认商品支付
		if (options.value.payClassic) {
			apiUrl = payApiList[options.value.payClassic]
		}
		try {
			let {
				data
			} = await uni.http.post(apiUrl, {
				...options.value
			})
			//对于各种折扣的解析 索引0:微信折扣 索引1:支付宝折扣 索引2余额折扣 索引3积分折扣 索引4sla折扣
			// if (data.result.discount) {
			// 	data.result.discount = Object.values(data.result.discount).map((item, index) => item)
			// }
			info.value = data.result;
			uni.hideLoading()
		} catch (e) {
			uni.hideToast()
			uni.showModal({
				showCancel: false,
				content: e.data?.message || '',
				confirmText: '知道了',
				success() {
					toUpPage()
				}
			})
			//TODO handle the exception
		}
	}
</script>

<style lang="scss">
	.cashier {
		width: 750rpx;
		padding: 0 32rpx;

		&-placeholder {
			padding-bottom: v-bind(getSafeBottom());
		}

		&-btnOpe {
			padding-bottom: v-bind(getSafeBottom());
		}

		.passwordInput {
			width: 650rpx;
			padding: 32rpx;
			border-radius: 32rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			background-color: white;
			overflow: hidden;

			&-title {
				font-weight: 500;
				font-size: 32rpx;
				color: #333333;
				margin-bottom: 32rpx;
			}
		}



		&-top {
			width: 100%;
			height: 330rpx;
			display: flex;
			flex-direction: column;
			align-items: center;
			justify-content: center;
			font-size: 24rpx;
			color: #555555;

			>view:nth-child(1) {
				font-weight: bold;
				color: #FF3333;
				margin-bottom: 8rpx;

				>text:nth-child(2) {
					font-size: 80rpx;
					line-height: 80rpx;
				}

				>text:nth-child(1) {
					font-size: 44rpx;
					line-height: 44rpx;
				}
			}
		}

		&-bottom {
			width: 100%;
			background: #FFFFFF;
			border-radius: 17.17rpx;
			// padding:  24rpx;
			// margin-bottom: 40rpx;

			&-payLine {
				padding: 0 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 24rpx;
				color: #222222;
				// line-height: 44rpx;
				width: 100%;
				height: 125rpx;

				&-srw {
					width: 32rpx;
					height: 32rpx;
					margin-left: 10rpx;
					position: absolute;
					z-index: 11;
				}

				&-desc {

					position: absolute;
					z-index: 11;
					color: #222222;
					font-size: 24rpx;
					right: 50rpx;
					bottom: -30rpx;

					// #FF3333

				}

				&-ipt {
					flex: 1;
					// width: 280rpx;
					height: 44rpx;
					font-size: 32rpx;
					color: #222222;
					text-align: right;
					line-height: 44rpx;
					padding-right: 50rpx;
				}

				>view:nth-child(1) {
					display: flex;
					align-items: center;

					>image {
						width: 46rpx;
						height: 46rpx;
						margin-right: 20rpx;
					}
				}

				>image:nth-child(2) {
					width: 30rpx;
					height: 30rpx;
				}

				>view:nth-child(2) {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					position: relative;
				}
			}
		}
	}
</style>