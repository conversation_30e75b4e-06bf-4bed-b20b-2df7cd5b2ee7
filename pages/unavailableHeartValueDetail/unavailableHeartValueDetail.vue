<template>
	<view class="unavailableHeartValueDetail">
		<view class="unavailableHeartValueDetail-line">
			<view class="unavailableHeartValueDetail-line-left">
				<view>
					待获得助力
				</view>
				<view>
					2024-01-11 09:35:38
				</view>
			</view>
			<view class="unavailableHeartValueDetail-line-right">
				500
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">

</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.unavailableHeartValueDetail {
		padding: 30rpx;

		&-line {
			padding: 16rpx 30rpx;
			background-color: white;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;

			&-left {
				>view:nth-child(1) {
					color: #333333;
					font-size: 30rpx;
					margin-bottom: 10rpx;
				}

				>view:nth-child(2) {
					color: #999999;
					font-size: 24rpx;
				}
			}

			&-right {
				color: #333333;
				text-align: right;
				font-size: 30rpx;
			}
		}
	}
</style>