<template>
    <view class="page">
    <!-- 顶部轮播图 -->
    <view class="banner-container">
      <swiper class="banner" :indicator-dots="true" :autoplay="true" :interval="3000" :duration="1000">
        <swiper-item v-for="(item, index) in advList" :key="index">
          <view class="banner-image-container">
            <image @click.stop="adNavTo(item)" :src="item.pictureAddr" mode="aspectFill" class="banner-image"/>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <!-- 过渡区域 -->
    <view class="transition-area">
      <view class="transition-content">
        <uni-icons type="star-filled" size="18" color="#FF4D4F"/>
        <text class="transition-text">品牌精选</text>
      </view>
    </view>
    <!-- 热门商品推荐 -->
    <view class="hot-section" v-if="hotList.length">
    <view class="section-header">
    <view class="section-title-container">
      <uni-icons type="fire-filled" size="18" color="#FF4D4F"/>
      <text class="section-title">热门推荐</text>
    </view>
    <view class="more-btn" @click="showMoreHotProducts">
      <text class="more-text">更多</text>
      <uni-icons type="right" size="14" color="#666666"/>
    </view>
    </view>
    <scroll-view scroll-x="true" class="hot-scroll" @scroll="onHotScroll" :scroll-left="hotScrollLeft">
    <view class="hot-products">
      <view v-for="(item, index) in hotList" :key="index"
        class="product-card"
        :class="{ 'product-card-active': currentVisibleIndex === index }"
        @click="navToGoodDetail(item)">
        <view class="product-card-inner">
          <image :src="imgUrl + parseImgurl(item.mainPicture)[0]" mode="aspectFill" class="product-image"/>
          <text class="product-name">{{ item.goodName }}</text>
          <text class="product-price">¥{{ item.smallPrice }}</text>
        </view>
      </view>
    </view>
    </scroll-view>
    <view class="scroll-indicator" v-if="hotList.length > 3">
      <view v-for="(_, index) in hotList" :key="index"
        class="indicator-dot"
        :class="{ 'indicator-dot-active': currentVisibleIndex === index }"
        @click="scrollToItem(index)">
      </view>
    </view>
    </view>
    <!-- 店铺列表 -->
    <view class="shop-list">
    <view v-for="(shop, index) in list" :key="index" class="shop-card" @click="navToStoreIndex(shop)">
      <view class="shop-info">
        <image :src="imgUrl + shop.logoAddr" mode="aspectFill" class="shop-logo"/>
        <view class="shop-detail">
          <!-- 店铺名称和等级 -->
          <view class="shop-header-row">
            <view class="shop-name-container">
              <text class="shop-name">{{ shop.storeName }}<text class="shop-level" v-if="shop.storeLevel">Lv.{{ shop.storeLevel }}</text></text>
            </view>
          </view>

          <!-- 类型和业绩 -->
          <view class="shop-info-row">
            <text class="shop-type">{{ shop.storeTypeName || '企业' }}</text>
            <text class="shop-revenue">累计业绩{{ shop.totalPerformance }}</text>
          </view>

          <!-- 城市和标签 -->
          <view class="shop-subinfo">
            <view class="shop-city-container">
              <uni-icons type="location" size="14" color="#666666"/>
              <text class="shop-city">{{ shop.city || '未知' }}</text>
            </view>
            <view class="shop-tags">
              <text v-for="(tag, tagIndex) in [shop.mainTypeName, shop.storeTypeName].filter(Boolean)"
                :key="tagIndex"
                class="tag">{{ tag }}</text>
            </view>
          </view>
        </view>

        <!-- 电话按钮 -->
        <view class="contact-btn" @click.stop="makePhoneCall(shop.takeOutPhone)">
          <uni-icons type="phone-filled" size="20" color="#2B85E4"/>
        </view>
      </view>

      <!-- 商品展示区 -->
      <scroll-view scroll-x="true" class="shop-products" v-if="shop.storeGoods && shop.storeGoods.length">
        <view class="products-container">
          <view v-for="(product, productIndex) in shop.storeGoods"
            :key="productIndex"
            class="shop-product" @click.stop="navToGoodDetail(product)">
            <image :src="imgUrl + parseImgurl(product.mainPicture)[0]" mode="aspectFill" class="shop-product-image"/>
            <text class="shop-product-name">{{ product.goodName }}</text>
            <text class="shop-product-price">¥{{ product.smallPrice }}</text>
          </view>
        </view>
      </scroll-view>
    </view>
    </view>
    <empty v-if="!list.length"></empty>
    </view>
    </template>
<script setup>
import { computed, ref } from 'vue';
import { adNavTo, listGet, navToGoodDetail, navToStoreIndex, navTo } from '@/hooks';
import { parseImgurl } from '@/utils';
import { onLoad, onPullDownRefresh } from '@dcloudio/uni-app';
import empty from '@/components/empty/empty.vue';

// 基础数据
const imgUrl = uni.env.IMAGE_URL;
const advList = ref([]);
const hotList = ref([]);
const pageType = ref('3'); // 默认为创业馆
const storeType = ref('16'); // 默认为创业馆对应的storeType

// 热门商品滚动相关
const currentVisibleIndex = ref(0);
const hotScrollLeft = ref(0);
const CARD_WIDTH = 220; // 卡片宽度，单位rpx
const CARD_MARGIN = 20; // 卡片右边距，单位rpx

// 滚动到指定商品
const scrollToItem = (index) => {
  // 将rpx转换为px进行计算
  const screenWidth = uni.getSystemInfoSync().windowWidth;
  const rpxToPx = screenWidth / 750;
  const scrollPosition = index * (CARD_WIDTH + CARD_MARGIN) * rpxToPx;
  hotScrollLeft.value = scrollPosition;
  currentVisibleIndex.value = index;
};

// 监听滚动事件
const onHotScroll = (e) => {
  // 计算当前可见的卡片索引
  const screenWidth = uni.getSystemInfoSync().windowWidth;
  const rpxToPx = screenWidth / 750;
  const scrollPosition = e.detail.scrollLeft;
  const cardWidthPx = CARD_WIDTH * rpxToPx;
  const cardMarginPx = CARD_MARGIN * rpxToPx;
  const index = Math.round(scrollPosition / (cardWidthPx + cardMarginPx));

  if (index >= 0 && index < hotList.value.length) {
    currentVisibleIndex.value = index;
  }
};

// 显示更多热门商品
const showMoreHotProducts = () => {
  // 跳转到商品搜索页面，并传递当前页面类型参数
  navTo(`/packageSearch/pages/search/search?storeType=${storeType.value}`);
};

// 刷新页面数据
const refreshPage = async () => {
  // 获取广告数据
  const { data: { result } } = await uni.http.get(uni.api.findarketingAdvertisingList, {
    params: {
      pattern: 0
    }
  });
  advList.value = result.map(i => {
    i.pictureAddr = `${uni.env.IMAGE_URL}${i.pictureAddr}`;
    return i;
  });

  // 获取热门商品数据
  const searchHostStoreGoodData = await uni.http.get(uni.api.searchHostStoreGoodList, {
    params: {
      storeType: storeType.value
    }
  });
  hotList.value = searchHostStoreGoodData.data.result || [];

  // 不在这里调用refresh，避免重复请求
};

// 获取店铺列表数据
const {
  list,
  refresh
} = listGet({
  apiUrl: uni.api.findStoreManageList,
  isReqTypeReq: false,
  isWatchOptions: false, // 不监听options变化自动请求
  options: computed(() => ({
    storeType: storeType.value,
    pattern: 0
  })),
});

// 拨打电话
const makePhoneCall = (phone) => {
  if (!phone) {
    uni.showToast({
      title: '暂无联系电话',
      icon: 'none'
    });
    return;
  }

  uni.makePhoneCall({
    phoneNumber: phone,
    fail: () => {
      uni.showToast({
        title: '拨打电话失败',
        icon: 'none'
      });
    }
  });
};

// 页面加载时根据参数设置页面类型和标题
onLoad((o) => {
  if (o.type) {
    const typeList = ['品牌馆', '生活馆', '创业馆'];
    const storeTypeList = ['15', '16', '17'];
    pageType.value = o.type;
    storeType.value = storeTypeList[o.type - 1];
    uni.setNavigationBarTitle({
      title: typeList[pageType.value - 1]
    });
  } else {
    // 默认设置为品牌馆
    uni.setNavigationBarTitle({
      title: '品牌推荐'
    });
  }

  // 加载页面数据
  refreshPage();
  // 单独调用刷新店铺列表，使用Promise确保顺序执行
  Promise.resolve().then(() => {
    refresh();
  });
});

// 实现下拉刷新功能
onPullDownRefresh(() => {
  // 显示刷新提示
  uni.showLoading({
    title: '加载中...',
    mask: true
  });

  // 刷新页面数据
  Promise.all([
    refreshPage(),
    new Promise(resolve => {
      // 刷新店铺列表
      refresh();
      resolve();
    })
  ]).then(() => {
    // 刷新完成，隐藏加载提示
    uni.hideLoading();
    // 显示成功提示
    uni.showToast({
      title: '加载成功',
      icon: 'success',
      duration: 1500
    });
    // 停止下拉刷新动画
    uni.stopPullDownRefresh();
  }).catch(() => {
    // 刷新失败处理
    uni.hideLoading();
    uni.showToast({
      title: '加载失败',
      icon: 'none',
      duration: 1500
    });
    uni.stopPullDownRefresh();
  });
});
</script>
    <style>
    page {
      height: 100%;
      background-color: #F5F7FA;
    }
    .banner-container {
      width: 750rpx;
      padding: 0;
      position: relative;
      overflow: hidden;
      background-color: #FFFFFF;
    }
    .banner {
      width: 750rpx;
      height: 400rpx;
    }
    .banner-image-container {
      padding: 0 0 20rpx 0;
      box-sizing: border-box;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .banner-image {
      width: 750rpx;
      height: 400rpx;
      border-radius: 0 0 16rpx 16rpx;
    }
    .transition-area {
      margin: -20rpx 20rpx 0 20rpx;
      position: relative;
      z-index: 10;
      background: linear-gradient(to right, #FF7A7A, #FF4D4F);
      border-radius: 12rpx;
      padding: 16rpx 30rpx;
      box-shadow: 0 4rpx 16rpx rgba(255, 77, 79, 0.2);
    }
    .transition-content {
      display: flex;
      align-items: center;
    }
    .transition-text {
      font-size: 16px;
      font-weight: bold;
      color: #FFFFFF;
      margin-left: 8rpx;
    }
    .hot-section {
      margin: 20rpx;
      margin-top: 20rpx;
      padding: 30rpx;
      background-color: #FFFFFF;
      border-radius: 12px;
      box-sizing: border-box;
      box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
      position: relative;
    }
    .section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24rpx;
    }
    .section-title-container {
    display: flex;
    align-items: center;
    }
    .section-title {
      font-size: 18px;
      font-weight: bold;
      color: #333333;
      margin-left: 8rpx;
      position: relative;
    }
    .section-title::after {
      content: '';
      position: absolute;
      bottom: -6rpx;
      left: 0;
      width: 40rpx;
      height: 4rpx;
      background: linear-gradient(to right, #FF7A7A, #FF4D4F);
      border-radius: 2rpx;
    }
    .more-btn {
    display: flex;
    align-items: center;
    padding: 6rpx 12rpx;
    border-radius: 30rpx;
    transition: background-color 0.2s;
    }
    .more-btn:active {
    background-color: #F5F5F5;
    }
    .more-text {
    font-size: 14px;
    color: #666666;
    margin-right: 4rpx;
    }
    .hot-scroll {
    width: 100%;
    white-space: nowrap;
    position: relative;
    }
    .hot-products {
    padding: 10rpx 0 20rpx 0;
    white-space: nowrap;
    }
    .product-card {
    margin-right: 20rpx;
    width: 220rpx;
    flex-shrink: 0;
    display: inline-block;
    vertical-align: top;
    transition: transform 0.3s ease;
    }
    .product-card-inner {
      padding: 12rpx;
      border-radius: 12px;
      background-color: #FFFFFF;
      transition: box-shadow 0.3s ease, transform 0.3s ease;
      border: 1rpx solid #F0F0F0;
    }
    .product-card-active .product-card-inner {
      box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.1);
      transform: translateY(-6rpx) scale(1.02);
      border-color: #FFE0E0;
    }
    .product-image {
      width: 204rpx;
      height: 204rpx;
      border-radius: 10px;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
    }
    .product-name {
    font-size: 14px;
    color: #333333;
    margin-top: 12rpx;
    display: block;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    }
    .product-price {
    font-size: 16px;
    color: #FF4D4F;
    font-weight: bold;
    display: block;
    margin-top: 8rpx;
    }
    .scroll-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 16rpx;
    }
    .indicator-dot {
    width: 16rpx;
    height: 16rpx;
    border-radius: 8rpx;
    background-color: #E0E0E0;
    margin: 0 6rpx;
    transition: all 0.3s ease;
    cursor: pointer;
    }
    .indicator-dot-active {
    width: 24rpx;
    background-color: #FF4D4F;
    }
    .shop-list {
      padding: 20rpx;
      margin-top: 10rpx;
    }
    .shop-card {
      background-color: #FFFFFF;
      border-radius: 12px;
      padding: 30rpx;
      margin-bottom: 20rpx;
      box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.05);
      border: 1rpx solid #F0F0F0;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
    }
    .shop-card:active {
      transform: translateY(-2rpx);
      box-shadow: 0 8rpx 16rpx rgba(0,0,0,0.08);
    }
    .shop-info {
      display: flex;
      align-items: flex-start;
      margin-bottom: 20rpx;
      position: relative;
    }
    .shop-logo {
      width: 80rpx;
      height: 80rpx;
      border-radius: 40rpx;
      margin-right: 20rpx;
      flex-shrink: 0;
      box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
      border: 2rpx solid #FFFFFF;
    }
    .shop-detail {
      flex: 1;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      min-width: 0; /* 确保flex子项可以正确收缩 */
    }
    /* 店铺名称和等级行 */
    .shop-header-row {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      height: 40rpx; /* 固定高度 */
    }
    .shop-name-container {
      flex: 1;
      overflow: hidden;
    }
    .shop-name {
      font-size: 16px;
      font-weight: bold;
      color: #333333;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: inline-block;
    }
    /* 类型和业绩行 */
    .shop-info-row {
      display: flex;
      align-items: center;
      margin-bottom: 12rpx;
      height: 36rpx; /* 固定高度 */
    }
    .shop-revenue {
      font-size: 12px;
      color: #FF4D4F;
      background-color: #FFF1F0;
      padding: 4rpx 12rpx;
      border-radius: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 180rpx; /* 固定最大宽度 */
    }
    .shop-level {
      font-size: 12px;
      color: #2B85E4;
      background-color: #ECF5FF;
      padding: 4rpx 12rpx;
      border-radius: 4px;
      white-space: nowrap;
      margin-left: 8rpx;
      display: inline-block;
      vertical-align: middle;
    }
    .shop-type {
      font-size: 12px;
      color: #666666;
      background-color: #F5F5F5;
      padding: 4rpx 12rpx;
      border-radius: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 120rpx; /* 固定最大宽度 */
      margin-right: 16rpx; /* 添加右侧间距 */
    }
    /* 城市和标签行 */
    .shop-subinfo {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      min-height: 40rpx; /* 最小高度 */
    }
    .shop-city-container {
      display: flex;
      align-items: center;
      margin-right: 20rpx;
      flex-shrink: 0;
    }
    .shop-city {
      font-size: 14px;
      color: #666666;
      margin-left: 4rpx;
      white-space: nowrap;
    }
    .shop-tags {
      display: flex;
      flex-wrap: wrap;
      flex: 1;
      overflow: hidden;
    }
    .tag {
      font-size: 12px;
      color: #2B85E4;
      background-color: #ECF5FF;
      padding: 4rpx 12rpx;
      border-radius: 4px;
      margin-right: 12rpx;
      margin-bottom: 8rpx;
      white-space: nowrap;
    }
    .contact-btn {
      position: absolute;
      top: 0;
      right: 0;
      width: 40px;
      height: 40px;
      display: none; /* 隐藏电话按钮 */
      align-items: center;
      justify-content: center;
      z-index: 1;
    }
    .shop-products {
      width: 100%;
      overflow: hidden;
      margin-top: 16rpx;
      height: 220rpx; /* 固定高度 */
      position: relative;
    }
    .shop-products::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 1rpx;
      background: linear-gradient(to right, rgba(0,0,0,0.05), rgba(0,0,0,0), rgba(0,0,0,0.05));
    }
    .products-container {
      display: flex;
      padding: 10rpx 0;
      height: 100%;
    }
    .shop-product {
      margin-right: 20rpx;
      width: 160rpx;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      transition: transform 0.3s ease;
    }
    .shop-product:active {
      transform: translateY(-4rpx);
    }
    .shop-product-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 10px;
      box-shadow: 0 2rpx 6rpx rgba(0,0,0,0.1);
      border: 1rpx solid #F0F0F0;
    }
    .shop-product-name {
      font-size: 14px;
      color: #333333;
      margin-top: 10rpx;
      display: block;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }
    .shop-product-price {
      font-size: 14px;
      color: #FF4D4F;
      font-weight: bold;
      display: block;
      margin-top: 6rpx;
    }
    </style>
