<template>
	<view class="storeCollection">
		<view class="storeCollection-top">
			<view @click="changeIsEdit">
				{{!isEdit ? '管理' : '完成' }}
			</view>

		</view>
		<view style="padding: 30rpx;">
			<!-- @click="navToStoreIndex(item)" -->
			<view class="storeCollection-storeInfo" v-for="(item,index) in list" :key="index">
				<view class="storeCollection-storeInfo-circle" v-if="isEdit" @click.stop="recordId(item)">
					<view v-if="selectedId.indexOf(item.matId) != -1"></view>
				</view>
				<view style="flex: 1;">
					<peopleDetail :storeInfo='item'></peopleDetail>
				</view>
				<!-- <image class="storeCollection-storeInfo-img" :src="imgUrl + item.logoAddr" mode=""></image>
				<view class="storeCollection-storeInfo-right">
					<view class="storeCollection-storeInfo-right-fir">
						{{item.storeName}}
					</view>
					<view class="storeCollection-storeInfo-right-sec">
						{{item.introduce}}
					</view>
					<view class="storeCollection-storeInfo-right-thi">
						创业积分: {{item.totalPerformance}}
					</view>
				</view> -->
			</view>
			<empty v-if="!list?.length"></empty>
		</view>

		<view class="storeCollection-bottom" v-if="isEdit">
			<view class="storeCollection-bottom-left" @click="changeAllSel">
				<view class="storeCollection-storeInfo-circle">
					<view v-if="isAllSelect"></view>
				</view>
				全选
			</view>
			<view class="storeCollection-bottom-right">
				<!-- <view>
					加入购物车
				</view> -->
				<view @click="showCancelPop">
					删除
				</view>
			</view>
		</view>

		<uni-popup ref="popup" type="dialog">
			<uni-popup-dialog type='info' mode="base" title='取消收藏' :content="'确定要取消收藏这' + selectedId.length + '种店铺吗？'"
				:duration="2000" :before-close="false" @confirm="cancel"></uni-popup-dialog>
		</uni-popup>

	</view>
</template>

<script setup>
	import {
		computed,
		ref
	} from 'vue';
	import {
		locationInfo
	} from "@/store";
	import {
		listGet,
		navToStoreIndex
	} from '@/hooks'
	import peopleDetail from '@/components/peopleDetail/peopleDetail.vue';
	const imgUrl = uni.env.IMAGE_URL
	let isEdit = ref(false)
	//已勾选的id集合
	let selectedId = ref([]);
	let popup = ref()
	//请求参数
	let reqOptions = computed(() => {
		const locat = locationInfo().info
		return {
			pattern: 1, //1=默认排序 2=距离最近 3=有券优先 4=好评优先
			latitude: locat.latitude,
			longitude: locat.longitude,
		}
	})
	const {
		mode,
		list,
		total,
		refresh
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.findMemberAttentionStoreByMember,
		pdIsLogin: true
	})
	//判断是否全选
	const isAllSelect = computed(() => {
		if (list.value.length <= 0) return false
		return list.value.every(i => selectedId.value.indexOf(i.matId) != -1);
	})

	//点击勾选改变勾选状态
	function recordId(item) {
		let sy = selectedId.value.indexOf(item.matId);
		if (sy == -1) {
			selectedId.value.push(item.matId)
		} else {
			selectedId.value.splice(sy, 1)
		}
	}

	//点击全选
	function changeAllSel() {
		//是否是全选
		if (isAllSelect.value) {
			selectedId.value = []
		} else {
			selectedId.value = list.value.map(i => i.matId);
		}
	}

	//打开取消收藏提示弹窗
	function showCancelPop() {
		if (selectedId.value.length <= 0) {
			uni.showToast({
				icon: "none",
				title: "请先选择商品~"
			})
			return;
		}
		popup.value.open();
	}

	//确认取消收藏
	async function cancel(id = '') {
		uni.showLoading({
			mask: true
		})
		let ids = selectedId.value.join(',')
		if (id) {
			ids = id
		}
		await uni.http.post(uni.api.delMemberAttentionStore, {
			ids
		})
		uni.showToast({
			title: '操作成功~',
			icon: "none"
		})
		setTimeout(() => {
			refresh()
		}, 500)
	}


	//改变编辑/完成状态
	function changeIsEdit() {
		isEdit.value = !isEdit.value
	}
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.storeCollection {

		&-top {
			height: 100rpx;
			background-color: white;
			color: #333333;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			padding-left: 30rpx;
			position: sticky;
			top: 0;
			z-index: 10;
		}

		&-bottom {
			position: fixed;
			bottom: 0;
			left: 0;
			background-color: white;
			width: 750rpx;
			padding: 30rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			z-index: 5;

			&-left {
				display: flex;
				align-items: center;
				color: #999999;
				font-size: 26rpx;
			}

			&-right {
				display: flex;
				align-items: center;
				justify-content: flex-end;

				>view {
					margin-left: 20rpx;
					height: 68rpx;
					line-height: 68rpx;
					text-align: center;
					background-color: #27A5FF;
					color: white;
					border-radius: 16rpx;
					font-size: 28rpx;
					padding: 0 30rpx;
				}
			}
		}


		&-storeInfo {
			background: #ffffff;
			border-radius: 16rpx;
			// padding: 20rpx;
			display: flex;
			align-items: center;
			margin-bottom: 20rpx;

			&-img {
				width: 220rpx;
				height: 290rpx;
				margin-right: 20rpx;
				border-radius: 16rpx;
			}

			&-circle {
				width: 30rpx;
				height: 30rpx;
				border: 2rpx solid #999999;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				margin-right: 30rpx;

				>view {
					width: 14rpx;
					height: 14rpx;
					background-color: #28a7ff;
					border-radius: 50%;
				}
			}

			&-right {
				flex: 1;
				min-height: 290rpx;
				display: flex;
				flex-direction: column;
				justify-content: space-between;

				&-fir {
					color: #333333;
					font-size: 32rpx;
					margin-bottom: 10rpx;
				}

				&-sec {
					color: #999999;
					font-size: 26rpx;
					margin-bottom: 15rpx;
				}

				&-thi {
					color: #333333;
					font-size: 24rpx;
				}
			}
		}



	}
</style>