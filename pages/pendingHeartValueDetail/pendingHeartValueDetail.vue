<template>
	<view class="pendingHeartValueDetail">
		<view class="pendingHeartValueDetail-line" v-for="(item,index) in list" :key="index">
			<view class="pendingHeartValueDetail-line-left">
				<view>
					{{item.payType}}
				</view>
				<view>
					{{item.createTime}}
				</view>
			</view>
			<view class="pendingHeartValueDetail-line-right">
				<view :style="[{color:item.goAndCome == '0' ? '#FF3333' : '#00AF42'}]">
					{{item.goAndCome == '0' ? '+' : '-'}}{{item.amount}}
				</view>
				<!-- 	<view>
					{{item.balance}}
				</view> -->
			</view>
		</view>
		<empty v-if="!list?.length"></empty>
	</view>
</template>

<script setup lang="ts">
	import {
		listGet
	} from "@/hooks";
	import {
		ref
	} from "vue";
	const {
		mode,
		list
	} = listGet({
		options: ref({
			isPlatform: 1
		}),
		apiUrl: uni.api.findMemberRechargeRecordPage,
		pdIsLogin: true
	})
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.pendingHeartValueDetail {
		padding: 30rpx;

		&-line {
			padding: 16rpx 30rpx;
			background-color: white;
			border-radius: 16rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 20rpx;


			&-left,
			&-right {
				>view:nth-child(1) {
					color: #333333;
					font-size: 30rpx;
					margin-bottom: 10rpx;
				}

				>view:nth-child(2) {
					color: #999999;
					font-size: 24rpx;
				}
			}

			&-right {
				text-align: right;

				>view:nth-child(1) {
					font-weight: bold;
				}
			}
		}
	}
</style>