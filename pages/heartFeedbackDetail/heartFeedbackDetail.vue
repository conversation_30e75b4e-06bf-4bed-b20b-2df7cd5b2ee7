<template>
	<view class="container">
		<!-- 收益金额展示区 -->
		<view class="amount-section">
			<text class="amount-label">{{info.payType}}</text>
			<text class="amount-value">{{info.amount}}</text>
		</view>
		<!-- 详细信息列表 -->
		<view class="detail-list">
			<view class="list-item">
				<text class="item-label">类型</text>
				<text class="item-value">{{info.goAndCome_dictText}}</text>
			</view>

			<view class="list-item">
				<text class="item-label">变动后账户余额</text>
				<text class="item-value">{{info.currentBalance}}</text>
			</view>

			<view class="list-item">
				<text class="item-label">交易类型</text>
				<text class="item-value">{{info.payType}}</text>
			</view>

			<!-- 		<view class="list-item">
				<text class="item-label">交易明细</text>
				<text class="item-value">车主结算金发放</text>
			</view> -->

			<view class="list-item">
				<text class="item-label">余额组成</text>
				<text class="item-value">{{info.balance}}</text>
			</view>

			<view class="list-item">
				<text class="item-label">时间</text>
				<text class="item-value">{{info.createTime}}</text>
			</view>

			<view class="list-item flow-number">
				<text class="item-label">交易流水号</text>
				<view class="copy-section">
					<text class="flow-value">{{info.orderNo}}</text>
					<view class="copy-btn" @click="copy(info.orderNo)">
						<text class="copy-text">复制</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onLoad,
	} from '@dcloudio/uni-app';
	import {
		ref
	} from 'vue';
	const info = ref({})

	function copy(data) {
		uni.setClipboardData({
			data
		})
	}
	onLoad((o) => {
		if (o?.info) {
			try {
				info.value = JSON.parse(o.info)
			} catch (error) {
				console.error(error)
				//TODO handle the exception
			}

		}
	})
</script>

<style>
	page {
		background-color: #F8F8F8;
	}

	.container {
		min-height: 100%;
		padding: 0 32rpx;
	}

	.amount-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 48rpx 0;
	}

	.amount-label {
		font-size: 28rpx;
		color: #666;
		margin-bottom: 20rpx;
	}

	.amount-value {
		font-size: 96rpx;
		color: #2196F3;
		font-weight: 600;
	}

	.detail-list {
		background: #fff;
		border-radius: 16rpx;
		padding: 0 32rpx;
		margin-top: 32rpx;
	}

	.list-item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-height: 100rpx;
		border-bottom: 2rpx solid #f5f5f5;
	}

	.list-item:last-child {
		border-bottom: none;
	}

	.item-label {
		font-size: 28rpx;
		color: #666;
	}

	.item-value {
		font-size: 28rpx;
		color: #333;
	}

	.flow-number {
		align-items: flex-start;
		padding: 32rpx 0;
	}

	.copy-section {
		flex: 1;
		display: flex;
		justify-content: flex-end;
		align-items: center;
	}

	.flow-value {
		font-size: 28rpx;
		color: #333;
		word-break: break-all;
		text-align: right;
		margin-right: 20rpx;
	}

	.copy-btn {
		flex-shrink: 0;
		background: #2196F3;
		border-radius: 8rpx;
		padding: 16rpx 32rpx;
		display: flex;
		align-items: center;
	}

	.copy-text {
		font-size: 24rpx;
		line-height: 24rpx;
		color: #fff;
	}
</style>