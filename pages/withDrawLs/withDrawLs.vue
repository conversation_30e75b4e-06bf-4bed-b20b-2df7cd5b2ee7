<template>
	<view class="withDrawLs">
		<uv-sticky bgColor="#f8f8f8">
			<view style="padding-bottom: 14rpx;">
				<uv-tabs :current='tabIndex' :scrollable='false' :list="tabData" lineWidth="45rpx" lineHeight='6rpx'
					lineColor="#1A69D1" :activeStyle="{
							color: '#1A69D1',
							fontSize:'28rpx',
				    	}" :inactiveStyle="{
							color: '#999999',
							fontSize:'28rpx'
						}" itemStyle="height:70rpx;" @click="changeTabIndex"></uv-tabs>
			</view>
		</uv-sticky>

		<view class="withDrawLs-card" v-for="(item,index) in list" :key="index" @click="toggleDetail(index)">
			<!-- 卡片头部 -->
			<view class="withDrawLs-card-header">
				<view class="withDrawLs-card-header-left">
					<text class="withDrawLs-card-title">{{withdrawalTypeTextList[item.withdrawalType]}}提现</text>
					<view class="withDrawLs-card-status" :class="'status-' + item.status">
						{{item.statusName || statusTextList[item.status]}}
					</view>
				</view>
				<view class="withDrawLs-card-header-right">
					<text class="withDrawLs-card-amount">￥{{item.money}}</text>
				</view>
			</view>

			<!-- 卡片内容 -->
			<view class="withDrawLs-card-content">
				<view class="withDrawLs-card-info">
					<view class="withDrawLs-card-info-item">
						<text class="withDrawLs-card-info-label">提现时间</text>
						<text class="withDrawLs-card-info-value">{{item.timeApplication}}</text>
					</view>
					<view class="withDrawLs-card-info-item" v-if="item.serviceCharge">
						<text class="withDrawLs-card-info-label">手续费</text>
						<text class="withDrawLs-card-info-value">￥{{item.serviceCharge || '0.00'}}</text>
					</view>
					<view class="withDrawLs-card-info-item" v-if="item.amount">
						<text class="withDrawLs-card-info-label">实际到账</text>
						<text class="withDrawLs-card-info-value">￥{{item.amount || item.money}}</text>
					</view>
				</view>

				<!-- 详情部分（可展开/收起） -->
				<view class="withDrawLs-card-detail" v-if="expandedItems[index]">
					<view class="withDrawLs-card-info-item">
						<text class="withDrawLs-card-info-label">订单号</text>
						<text class="withDrawLs-card-info-value">{{item.orderNo || '-'}}</text>
					</view>
					<view class="withDrawLs-card-info-item" v-if="item.bankCard">
						<text class="withDrawLs-card-info-label">收款账号</text>
						<text class="withDrawLs-card-info-value">{{formatBankCard(item.bankCard)}}</text>
					</view>
					<view class="withDrawLs-card-info-item" v-if="item.bankName">
						<text class="withDrawLs-card-info-label">收款银行</text>
						<text class="withDrawLs-card-info-value">{{item.bankName}}</text>
					</view>
					<view class="withDrawLs-card-info-item" v-if="item.remark">
						<text class="withDrawLs-card-info-label">备注</text>
						<text class="withDrawLs-card-info-value">{{item.remark || '-'}}</text>
					</view>
				</view>

				<!-- 展开/收起按钮 -->
				<view class="withDrawLs-card-toggle">
					<text>{{expandedItems[index] ? '收起详情' : '查看详情'}}</text>
					<text class="toggle-icon">{{expandedItems[index] ? '∧' : '∨'}}</text>
				</view>
			</view>
		</view>

		<empty v-if="!list?.length"></empty>
	</view>
</template>

<script setup>
	import {
		ref,
		computed,
		reactive
	} from "vue";
	import {
		listGet,
	} from "@/hooks";

	const tabData = [{
		name: '全部'
	}, {
		name: '待审核'
	}, {
		name: '成功'
	}, {
		name: '失败'
	}];

	let tabIndex = ref(0)
	// 状态文本映射
	const statusTextList = ['待审核', '待打款', '已打款', '无效']
	const withdrawalTypeTextList = ['微信', '支付宝', '银行卡']
	// 记录展开状态
	const expandedItems = reactive({})

	// 切换详情展开/收起
	function toggleDetail(index) {
		expandedItems[index] = !expandedItems[index]
	}

	// 格式化银行卡号，保留前4位和后4位
	function formatBankCard(cardNumber) {
		if (!cardNumber) return '-'
		if (cardNumber.length <= 8) return cardNumber

		const firstFour = cardNumber.substring(0, 4)
		const lastFour = cardNumber.substring(cardNumber.length - 4)
		return `${firstFour} **** **** ${lastFour}`
	}

	// 改变状态索引
	function changeTabIndex(e) {
		const index = e.index
		tabIndex.value = index
	}

	// 请求参数
	let reqOptions = computed(() => {
		// 修改成功标签的查询逻辑，同时查询待打款(1)和已付款(2)状态
		if (tabIndex.value === 2) { // 成功标签
			return {
				pattern: '1,2' // 使用逗号分隔的字符串传递多个状态值
			}
		} else {
			const patternList = [-1, 0, 1, 3]
			return {
				pattern: patternList[tabIndex.value]
			}
		}
	})

	const {
		mode,
		list
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.findMemberWithdrawDepositPageByMemberId,
		pdIsLogin: true
	})
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.withDrawLs {
		padding: 30rpx;
		padding-top: 0;

		&-card {
			background-color: white;
			border-radius: 16rpx;
			margin-bottom: 20rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;

			&-header {
				padding: 24rpx 30rpx;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1rpx solid #f5f5f5;

				&-left {
					display: flex;
					align-items: center;
				}

				&-right {
					text-align: right;
				}
			}

			&-title {
				font-size: 32rpx;
				color: #333;
				font-weight: 500;
				margin-right: 16rpx;
			}

			&-status {
				font-size: 24rpx;
				padding: 4rpx 12rpx;
				border-radius: 20rpx;
				background-color: #f0f0f0;
				color: #666;

				&.status-0 {
					background-color: #e6f7ff;
					color: #1890ff;
				}

				&.status-1 {
					background-color: #fff7e6;
					color: #fa8c16;
				}

				&.status-2 {
					background-color: #f6ffed;
					color: #52c41a;
				}

				&.status-3 {
					background-color: #fff1f0;
					color: #f5222d;
				}
			}

			&-amount {
				font-size: 36rpx;
				color: #333;
				font-weight: 500;
			}

			&-content {
				padding: 20rpx 30rpx;
			}

			&-info {
				margin-bottom: 20rpx;

				&-item {
					display: flex;
					justify-content: space-between;
					margin-bottom: 16rpx;

					&:last-child {
						margin-bottom: 0;
					}
				}

				&-label {
					font-size: 28rpx;
					color: #666;
				}

				&-value {
					font-size: 28rpx;
					color: #333;
				}
			}

			&-detail {
				padding: 20rpx 0;
				border-top: 1rpx dashed #eee;
				margin-top: 10rpx;
			}

			&-toggle {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 16rpx 0;
				color: #1A69D1;
				font-size: 26rpx;

				.toggle-icon {
					margin-left: 8rpx;
					font-size: 24rpx;
				}
			}
		}
	}
</style>