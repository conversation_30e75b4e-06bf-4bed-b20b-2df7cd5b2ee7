<template>
	<view class="withDraw">
		<view class="withDraw-wrap">
			<view class="withDraw-wrap-top">
				<view class="withDraw-wrap-top-left">
					结算助力
				</view>
				<view class="withDraw-wrap-top-right" @click="navTo('/pages/withDrawLs/withDrawLs')">
					结算明细
					<image src="@/static/right-arrow-gray.png" mode=""></image>
				</view>
			</view>
			<input class="withDraw-wrap-ipt" type="digit" v-model="price" placeholder="请输入结算助力" />
			<view class="withDraw-wrap-desc">
				可结算助力 {{users.userInfo.balance || 0}}
			</view>
			<view class="withDraw-wrap-sj">
				<view>
					实际到账
				</view>
				<view>
					¥{{resultPrice}}
				</view>
			</view>
		</view>
		<view class="withDraw-wrap">
			<view class="withDraw-wrap-top" style="margin-bottom: 40rpx;">
				<view class="withDraw-wrap-top-left">
					结算渠道
				</view>

			</view>
			<view class="withDraw-wrap-top" style="margin: 0;" @click="navTo('/pages/selectBankCard/selectBankCard')">
				<view class="withDraw-wrap-top-left" style="color: #333333;">
					银行卡
				</view>
				<view class="withDraw-wrap-top-right" v-if="!selectedBankCardInfo">
					<view>
						请选择结算银行卡
					</view>

					<image src="@/static/right-arrow-gray.png" mode=""></image>
				</view>
				<view class="withDraw-wrap-top-right" v-else>
					<view>
						{{selectedBankCardInfo.cardholder}} {{selectedBankCardInfo.bankName}}
						{{selectedBankCardInfo.bankCard}}
					</view>

					<image src="@/static/right-arrow-gray.png" mode=""></image>
				</view>
			</view>
		</view>

		<view class="withDraw-wrap">
			<view class="withDraw-wrap-top" style="margin-bottom: 30rpx;">
				<view class="withDraw-wrap-top-left">
					温馨提醒
				</view>
			</view>
			<view class="withDraw-wrap-cnt">
				<view v-for="(item, index) in attention" :key="index">{{ item }}</view>
			</view>
		</view>

		<view class="withDraw-bottom">
			<view @click="toSure">
				立即提现
			</view>
		</view>

		<!-- 		<uni-popup ref="atten" type="dialog">
			<uni-popup-dialog mode="base" type='info' title="温馨提示" content='您还未设置过银行卡' confirmText='去认证' cancelText='取消'
				:duration="2000" :before-close="false" @close="close" @confirm="confirm"></uni-popup-dialog>
		</uni-popup> -->

		<uni-popup ref="payAtten" type="dialog">
			<uni-popup-dialog mode="base" type='info' :title="payAttenConfig.title" :content='payAttenConfig.content'
				confirmText='确认' cancelText='取消' :duration="2000" :before-close="false" @close="payAttenClose"
				@confirm="payAttenConfirm"></uni-popup-dialog>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		onLoad,
		onShow,
		onUnload
	} from "@dcloudio/uni-app"
	import {
		reactive,
		ref,
		watch
	} from "vue";
	import {
		userStore
	} from '@/store/index.js'
	import {
		getUserInfos,
		navTo,
	} from '@/hooks/index.js'
	import {
		getSafeBottom
	} from '@/utils/index.js'
	let users = userStore(),
		atten = ref(),
		payAtten = ref(),
		price = ref(0),
		individualIncomeTax = ref(0.006), //个人所得税
		amountDeducted = ref(0), //服务费
		resultPrice = ref(0), //实际到账
		attention = ref([]), //温馨提示
		minCanTx = ref(0), //最小提现额度
		selectedBankCardInfo = ref(null),
		payAttenConfig = reactive({
			title: '温馨提示',
			content: '',
		}), //提现确认后弹窗信息提示
		payType = ref('bk'); //wx  zfb  bk;
	getUserInfos();
	watch(price, async (n) => {
		if (users.token) {
			let {
				data: res
			} = await uni.http.get(uni.api.withdrawXchanger, {
				params: {
					money: price.value * 1 || 0
				}
			});
			if (n) {
				amountDeducted.value = res.result.serviceCharge;
				resultPrice.value = res.result.amount;
			} else {
				amountDeducted.value = 0
				resultPrice.value = 0
			}
		}

	}, {
		immediate: true,
		deep: true
	})
	onShow(async () => {
		if (users.token) {
			let {
				data
			} = await uni.http.get(uni.api.getWithdrawWarmPrompt);
			if (data.message.indexOf('\\n') != -1) {
				attention.value = data.message.split('\\n');
			} else {
				attention.value = [1, 2];
			}
			let {
				data: data2
			} = await uni.http.get(uni.api.getWithdrawMinimum);
			minCanTx.value = data2.message;
		}

	})
	//改变支付渠道
	function changePayType(type = '') {
		payType.value = type
	}
	//设置银行卡弹窗确认按钮
	function confirm() {
		navTo('/pages/bankCard/bankCard')
	}
	//设置银行卡弹窗取消按钮
	function close() {

	}
	//提现弹窗确认按钮
	async function payAttenConfirm() {
		uni.showLoading({
			mask: true
		})
		let apiName = 'addWithdrawDeposit';
		switch (payType.value) {
			case 'wx':
				//微信提现
				apiName = `addWithdrawDeposit`;
				break;
			case 'zfb':
				//支付宝提现
				apiName = ``;
				break;
			case 'bk':
				//银行卡提现
				apiName = `withdrawalCard`
				break;
			default:
				//银行卡提现
				apiName = `withdrawalCard`
				break;
		}
		let {
			data: res
		} = await uni.http.post(uni.api[apiName], {
			money: price.value * 1 || 0,
			memberBankCardId: selectedBankCardInfo.value.id
		});

		// 显示提示消息
		uni.showToast({
			icon: 'none',
			title: res.message
		})

		// 如果提现成功
		if (res.success || res.code === 200) {
			// 重置表单数据
			resetFormData();

			// 延迟跳转，让用户能看到成功提示
			setTimeout(() => {
				// 使用重定向跳转到提现明细页面，这样返回时不会回到提现申请页面
				uni.redirectTo({
					url: '/pages/withDrawLs/withDrawLs',
					fail: () => {
						// 如果重定向失败，使用普通导航
						navTo('/pages/withDrawLs/withDrawLs');
					}
				});
			}, 1500);
		}
	}

	// 重置表单数据
	function resetFormData() {
		price.value = 0;
		resultPrice.value = 0;
		amountDeducted.value = 0;
		selectedBankCardInfo.value = null;
	}
	//提现弹窗取消按钮
	function payAttenClose() {

	}
	//确认按钮
	function toSure() {
		if (!users.token) {
			navTo('/pages/login/login');
			return
		}
		if (users.userInfo.balance * 1 <= 0) {
			uni.showToast({
				icon: 'none',
				title: '您暂无金额可提现'
			})
			return;
		}
		if (price.value * 1 <= 0 || !price.value) {
			uni.showToast({
				icon: 'none',
				title: '请设置提现金额'
			})
			return;
		}
		if (price.value * 1 > users.userInfo.balance * 1) {
			uni.showToast({
				icon: 'none',
				title: '可提现金额不足'
			})
			return;
		}
		if (price.value * 1 < minCanTx.value * 1) {
			uni.showToast({
				icon: 'none',
				title: '提现金额不能低于最小提现额度'
			})
			return;
		}
		if (!selectedBankCardInfo.value && payType.value == 'bk') {
			// atten.value.open();
			uni.showToast({
				icon: 'none',
				title: '请选择银行卡'
			})
			return;
		}
		switch (payType.value) {
			case 'wx':
				payAttenConfig.content = `确定要提现￥${price.value * 1}到微信吗？`
				break;
			case 'zfb':
				payAttenConfig.content = `确定要提现￥${price.value * 1}到支付宝吗？`
				break;
			case 'bk':
				// ${users.userInfo.memberBankCard}的
				payAttenConfig.content = `确定要提现￥${price.value * 1}到当前的银行卡吗？`
				break;
			default:
				break;
		}
		payAtten.value.open();
	}

	function selectBankCardCallback(item) {
		selectedBankCardInfo.value = item
	}
	onLoad(() => {
		//监听银行卡选择
		uni.$on('selectBankCard', selectBankCardCallback);
	})
	onUnload(() => {
		//页面销毁 销毁监听
		uni.$off('selectBankCard', selectBankCardCallback);
	})
</script>

<style lang="scss">
	page {
		background-color: #F8F8F8;
	}

	.withDraw {
		padding: 30rpx;

		&-bottom {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 10;
			background-color: white;
			width: 750rpx;
			padding: 30rpx;

			>view {
				height: 84rpx;
				line-height: 84rpx;
				color: white;
				font-size: 30rpx;
				text-align: center;
				background: #22a3ff;
				border-radius: 16rpx;
			}
		}

		&-wrap {
			padding: 30rpx;
			border-radius: 16rpx;
			background-color: white;
			margin-bottom: 20rpx;

			&-cnt {
				color: #666666;
				font-size: 28rpx;
				line-height: 45rpx;
			}

			&-sj {
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 26rpx;
				color: #333333;
			}

			&-desc {
				color: #666666;
				font-size: 26rpx;
				padding-bottom: 20rpx;
				border-bottom: 2rpx solid #F5F5F5;
				margin-bottom: 30rpx;
			}

			&-ipt {
				height: 50rpx;
				line-height: 50rpx;
				color: #000000;
				font-size: 40rpx;
				margin-bottom: 20rpx;
			}

			&-top {
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 20rpx;

				&-left {
					font-size: 30rpx;
					color: #000000;
				}

				&-right {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: flex-end;
					color: #999999;
					font-size: 26rpx;

					>image {
						width: 20rpx;
						height: 20rpx;
						margin-left: 6rpx;
					}

					>view:first-child {
						flex: 1;
						line-height: 35rpx;
						text-align: right;
					}
				}
			}
		}
	}
</style>