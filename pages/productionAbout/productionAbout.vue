<template>
	<view class="productionAbout">
		<view class="productionAbout_sZ">
			<image class="productionAbout_sZ-bg" src="@/static/productionAbout/s_0.png" mode=""></image>
			<view class="productionAbout_sZ-progress">
				<view class="productionAbout_sZ-progress-g"
					:style="{width:((users.userInfo.totalRechargeBalance || 0) / 99).toFixed(2) * 100 + '%'}">

				</view>
			</view>
		</view>
		<image class="productionAbout_sF" :src="imgUrl + '/static/productionAbout/s_1.png'"></image>
		<image class="productionAbout_sS" src="@/static/productionAbout/s_2.png" mode=""></image>
		<view style="width: 750rpx;height: 150rpx;">

		</view>
		<view class="productionAbout-btn">
			<button @click="navTo('/packageRecharge/pages/recharge/recharge?fromDreamHelper=true')">充值 ¥99 立即成为助梦家</button>
		</view>
	</view>
</template>

<script setup>
	import {
		getUserInfos,
		navTo
	} from '@/hooks';
	import {
		userStore
	} from '@/store';
	const imgUrl = uni.env.IMAGE_URL
	const users = userStore();
	getUserInfos()
</script>

<style lang="scss">
	.productionAbout {
		&-btn {
			position: fixed;
			bottom: 0;
			left: 0;
			z-index: 10;
			width: 750rpx;
			height: 151rpx;
			background: #17376c;
			box-shadow: 0rpx -5rpx 30rpx 0rpx rgba(194, 193, 193, 0.25);
			display: flex;
			align-items: center;
			justify-content: center;

			>button {
				width: 500rpx;
				height: 80rpx;
				background: #e2bc89;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 30rpx;
				color: white;
			}
		}

		&_sF {
			width: 750rpx;
			height: 1124rpx;
		}

		&_sS {
			width: 750rpx;
			height: 1389rpx;
		}

		&_sZ {
			width: 750rpx;
			height: 390rpx;
			position: relative;

			&-bg {
				width: 100%;
				height: 100%;
			}

			&-progress {
				width: 280rpx;
				height: 16rpx;
				background: #0a386a;
				border: 2rpx solid #3884d7;
				border-radius: 10rpx;
				position: absolute;
				z-index: 2;
				left: 85rpx;
				top: 165rpx;
				padding: 0 3rpx;
				display: flex;
				align-items: center;

				&-g {
					background: #e2bc88;
					height: 10rpx;
					width: 10%;
					border-radius: 10rpx;
				}
			}
		}
	}
</style>