# 组件设计规范文档

## 🏗️ 组件架构设计

### 组件分层架构
```
┌─────────────────────────────────────────┐
│           shopIndex.vue                 │  ← 主容器 (路由页面)
│         (页面级组件)                     │
└─────────────────────────────────────────┘
                    │
    ┌───────────────┼───────────────┐
    │               │               │
┌───▼───┐      ┌────▼────┐     ┌───▼───┐
│Header │      │Category │     │Modals │  ← 功能区域组件
│Section│      │Section  │     │Section│
└───────┘      └─────────┘     └───────┘
    │               │               │
┌───▼───┐      ┌────▼────┐     ┌───▼───┐
│UI组件 │      │业务组件 │     │弹窗组件│  ← 具体功能组件
└───────┘      └─────────┘     └───────┘
    │               │               │
┌───▼───┐      ┌────▼────┐     ┌───▼───┐
│原子组件│      │复合组件 │     │交互组件│  ← 基础组件
└───────┘      └─────────┘     └───────┘
```

## 📋 组件详细设计

### 1. ShopTabNavigation.vue (Tab导航组件)
**职责**: 管理页面顶部的Tab切换导航
**复杂度**: 低
**预估行数**: 80-100行

```vue
<template>
  <view class="shop-tab-navigation">
    <view class="tab-container">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ 'tab-active': activeIndex === index }"
        @click="handleTabClick(index)">
        {{ tab.label }}
        <view class="tab-indicator" v-if="activeIndex === index"></view>
      </view>
    </view>
    <view class="attention-button" @click="handleAttention">
      {{ attentionText }}
    </view>
  </view>
</template>
```

**Props接口**:
```typescript
interface Props {
  tabs: Array<{label: string, value: string}>
  activeIndex: number
  isAttention: boolean
  attentionText?: string
}
```

**Events接口**:
```typescript
interface Events {
  'tab-change': (index: number) => void
  'attention-click': () => void
}
```

### 2. ShopInfoCard.vue (店铺信息卡片)
**职责**: 展示店铺基本信息、统计数据和宣传图片
**复杂度**: 中
**预估行数**: 120-150行

```vue
<template>
  <view class="shop-info-card">
    <!-- 店铺头部信息 -->
    <view class="shop-header">
      <ShopLogo 
        :logo-url="shopInfo.logoAddr"
        :is-enterprise="shopInfo.storeStraight === '1'"
        @click="handleLogoClick" />
      <ShopBasicInfo 
        :shop-name="shopInfo.storeName"
        :shop-level="shopInfo.storeLevel"
        :shop-intro="shopInfo.introduce" />
    </view>
    
    <!-- 店铺统计数据 -->
    <ShopStats :stats="shopStats" />
    
    <!-- 宣传图片 -->
    <ShopPropagandaImages :images="propagandaImages" />
  </view>
</template>
```

**Props接口**:
```typescript
interface ShopInfo {
  logoAddr: string
  storeName: string
  storeLevel: string
  introduce: string
  storeStraight: string
  totalPerformance: number
  goodsCount: number
  salesVolume: number
  storePropagandaImages?: string
}
```

### 3. CategoryContainer.vue (分类页面容器)
**职责**: 管理整个分类页面的布局和状态
**复杂度**: 高
**预估行数**: 150-200行

```vue
<template>
  <view class="category-container">
    <CategorySidebar 
      :categories="categories"
      :active-index="activeCategoryIndex"
      @category-select="handleCategorySelect" />
    
    <view class="category-content">
      <CategorySubTabs 
        v-if="hasSubCategories"
        :sub-categories="currentSubCategories"
        :active-index="activeSubCategoryIndex"
        @sub-category-select="handleSubCategorySelect"
        @more-categories="showMoreCategories" />
      
      <CategoryGoodsList 
        :goods-list="goodsList"
        :loading="loading"
        @goods-click="handleGoodsClick"
        @add-to-cart="handleAddToCart" />
    </view>
  </view>
</template>
```

### 4. GoodsItem.vue (商品项组件)
**职责**: 展示单个商品信息和购物车操作
**复杂度**: 中
**预估行数**: 100-120行

```vue
<template>
  <view class="goods-item" @click="handleGoodsClick">
    <image class="goods-image" :src="goodsImage" mode="aspectFill" />
    
    <view class="goods-info">
      <view class="goods-title">{{ goods.goodName }}</view>
      <view class="goods-subtitle" v-if="goods.subtitle">{{ goods.subtitle }}</view>
      
      <GoodsSalesStock 
        :sales="goods.salesVolume"
        :stock="goods.stock" />
      
      <view class="goods-bottom">
        <GoodsPrice :price="goods.price" />
        <CartQuantityControl 
          :goods="goods"
          :quantity="cartQuantity"
          @quantity-change="handleQuantityChange" />
      </view>
    </view>
  </view>
</template>
```

### 5. CartQuantityControl.vue (购物车数量控制器)
**职责**: 管理商品的购物车数量操作
**复杂度**: 高
**预估行数**: 120-150行

```vue
<template>
  <view class="cart-quantity-control">
    <!-- 缺货状态 -->
    <view v-if="isOutOfStock" class="out-of-stock">
      <text>缺货</text>
    </view>
    
    <!-- 规格选择 -->
    <view v-else-if="hasSpecification" class="spec-select" @click="handleSpecSelect">
      <text>选规格</text>
    </view>
    
    <!-- 数量控制 -->
    <view v-else class="quantity-control">
      <view class="control-btn minus" 
            :class="{ disabled: quantity <= 0 }"
            @click.stop="handleDecrease">-</view>
      <text class="quantity-text">{{ quantity }}</text>
      <view class="control-btn plus"
            :class="{ disabled: quantity >= maxStock }"
            @click.stop="handleIncrease">+</view>
    </view>
  </view>
</template>
```

## 🔧 Composables设计

### 1. useShopData.js (店铺数据管理)
**职责**: 管理店铺基础数据的获取和状态
**复杂度**: 中
**预估行数**: 80-100行

```javascript
export function useShopData(shopId) {
  const shopInfo = ref({})
  const loading = ref(false)
  const error = ref(null)
  
  const fetchShopInfo = async () => {
    try {
      loading.value = true
      const { data } = await uni.http.post(uni.api.storeManageIndex, {
        sysUserId: shopId
      })
      shopInfo.value = data.result
    } catch (err) {
      error.value = err
    } finally {
      loading.value = false
    }
  }
  
  const attentionStore = async () => {
    // 关注/取消关注逻辑
  }
  
  return {
    shopInfo: readonly(shopInfo),
    loading: readonly(loading),
    error: readonly(error),
    fetchShopInfo,
    attentionStore
  }
}
```

### 2. useCategoryNavigation.js (分类导航管理)
**职责**: 管理分类导航状态和商品加载
**复杂度**: 高
**预估行数**: 120-150行

```javascript
export function useCategoryNavigation(shopId) {
  const categories = ref([])
  const activeCategoryIndex = ref(0)
  const activeSubCategoryIndex = ref(0)
  const goodsList = ref([])
  const loading = ref(false)
  
  const currentCategory = computed(() => 
    categories.value[activeCategoryIndex.value]
  )
  
  const currentSubCategories = computed(() => 
    currentCategory.value?.chlidGoodType || []
  )
  
  const fetchCategories = async () => {
    // 获取分类数据
  }
  
  const fetchGoodsList = async () => {
    // 获取商品列表
  }
  
  const selectCategory = (index) => {
    activeCategoryIndex.value = index
    activeSubCategoryIndex.value = 0
    fetchGoodsList()
  }
  
  const selectSubCategory = (index) => {
    activeSubCategoryIndex.value = index
    fetchGoodsList()
  }
  
  return {
    categories: readonly(categories),
    activeCategoryIndex,
    activeSubCategoryIndex,
    goodsList: readonly(goodsList),
    loading: readonly(loading),
    currentCategory,
    currentSubCategories,
    fetchCategories,
    selectCategory,
    selectSubCategory
  }
}
```

### 3. useCartManagement.js (购物车管理)
**职责**: 管理购物车状态和操作
**复杂度**: 高
**预估行数**: 150-200行

```javascript
export function useCartManagement() {
  const cartItems = ref(new Map()) // goodId -> cartItem
  const cartCounts = ref(new Map()) // goodId -> quantity
  const quickCheckoutItems = ref(new Map()) // 快捷结算商品
  
  const totalCount = computed(() => 
    Array.from(cartCounts.value.values()).reduce((sum, count) => sum + count, 0)
  )
  
  const totalAmount = computed(() => 
    Array.from(quickCheckoutItems.value.values())
      .reduce((sum, item) => sum + (item.quantity * item.price), 0)
  )
  
  const addToCart = async (goods, quantity = 1, specification = '无') => {
    try {
      const { data } = await uni.http.post(uni.api.addGoodToShoppingCart, {
        goodId: goods.id,
        specification,
        isPlatform: 0,
        quantity
      })
      
      // 更新本地状态
      updateLocalCartState(goods.id, quantity, data.result)
      
      return data.result
    } catch (error) {
      throw new Error('添加购物车失败')
    }
  }
  
  const updateCartQuantity = async (goodId, newQuantity) => {
    // 更新购物车数量
  }
  
  const syncCartState = async () => {
    // 同步购物车状态
  }
  
  return {
    cartCounts: readonly(cartCounts),
    quickCheckoutItems: readonly(quickCheckoutItems),
    totalCount,
    totalAmount,
    addToCart,
    updateCartQuantity,
    syncCartState,
    getCartCount: (goodId) => cartCounts.value.get(goodId) || 0
  }
}
```

## 📁 文件结构规划

```
packageGoods/pages/shopIndex/
├── shopIndex.vue                 # 主页面文件 (约200行)
├── components/                   # 页面专用组件
│   ├── ShopHeader/
│   │   ├── ShopTabNavigation.vue
│   │   ├── ShopInfoCard.vue
│   │   ├── ShopLogo.vue
│   │   ├── ShopBasicInfo.vue
│   │   ├── ShopStats.vue
│   │   └── ShopPropagandaImages.vue
│   ├── ShopCategory/
│   │   ├── CategoryContainer.vue
│   │   ├── CategorySidebar.vue
│   │   ├── CategorySubTabs.vue
│   │   ├── CategoryGoodsList.vue
│   │   └── CategoryMoreModal.vue
│   ├── ShopGoods/
│   │   ├── GoodsItem.vue
│   │   ├── GoodsPrice.vue
│   │   ├── GoodsSalesStock.vue
│   │   ├── GoodsGrid.vue
│   │   └── NewGoodsSection.vue
│   └── ShopCart/
│       ├── CartQuantityControl.vue
│       └── CartButton.vue
├── composables/                  # 业务逻辑钩子
│   ├── useShopData.js
│   ├── useCategoryNavigation.js
│   ├── useCartManagement.js
│   └── useGoodsOperations.js
├── types/                        # 类型定义
│   └── shop.types.js
└── styles/                       # 样式文件
    ├── shop-header.scss
    ├── shop-category.scss
    ├── shop-goods.scss
    └── shop-cart.scss
```

## 🎨 样式组织规范

### 样式文件拆分
1. **组件级样式**: 每个组件使用scoped样式
2. **功能模块样式**: 按功能模块拆分样式文件
3. **公共样式**: 提取可复用的样式变量和混入

### CSS变量定义
```scss
// styles/variables.scss
:root {
  --shop-primary-color: #FF6B35;
  --shop-secondary-color: #22A3FF;
  --shop-background-color: #F5F5F5;
  --shop-card-radius: 16rpx;
  --shop-spacing-small: 16rpx;
  --shop-spacing-medium: 24rpx;
  --shop-spacing-large: 32rpx;
}
```

## 🔍 组件接口规范

### Props命名规范
- 使用kebab-case命名
- 明确的类型定义
- 提供默认值
- 添加验证规则

### Events命名规范
- 使用kebab-case命名
- 事件名称要描述性强
- 传递必要的参数
- 避免事件冒泡问题

### Slots使用规范
- 提供具名插槽
- 插槽要有明确的用途
- 提供插槽的默认内容
- 文档化插槽的使用方式

---

**注意**: 此设计规范确保了组件的一致性、可维护性和可复用性。每个组件都应该遵循单一职责原则，具有清晰的接口定义。
