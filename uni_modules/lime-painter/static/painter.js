!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).Painter={})}(this,(function(t){"use strict";var e=function(){return e=Object.assign||function(t){for(var e,i=1,n=arguments.length;i<n;i++)for(var r in e=arguments[i])Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},e.apply(this,arguments)};function i(t,e,i,n){return new(i||(i=Promise))((function(r,o){function s(t){try{h(n.next(t))}catch(t){o(t)}}function a(t){try{h(n.throw(t))}catch(t){o(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(s,a)}h((n=n.apply(t,e||[])).next())}))}function n(t,e){var i,n,r,o,s={label:0,sent:function(){if(1&r[0])throw r[1];return r[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(i)throw new TypeError("Generator is already executing.");for(;s;)try{if(i=1,n&&(r=2&o[0]?n.return:o[0]?n.throw||((r=n.return)&&r.call(n),0):n.next)&&!(r=r.call(n,o[1])).done)return r;switch(n=0,r&&(o=[2&o[0],r.value]),o[0]){case 0:case 1:r=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(r=s.trys,(r=r.length>0&&r[r.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!r||o[1]>r[0]&&o[1]<r[3])){s.label=o[1];break}if(6===o[0]&&s.label<r[1]){s.label=r[1],r=o;break}if(r&&s.label<r[2]){s.label=r[2],s.ops.push(o);break}r[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{i=r=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}var r={MP_WEIXIN:"mp-weixin",MP_QQ:"mp-qq",MP_ALIPAY:"mp-alipay",MP_BAIDU:"mp-baidu",MP_TOUTIAO:"mp-toutiao",MP_DINGDING:"mp-dingding",H5:"h5",WEB:"web",PLUS:"plus"},o=["contentSize","clientSize","borderSize","offsetSize"],s="row",a="column",h="top",d="middle",c="bottom",l="left",f="center",u="right",p="view",g="text",v="image",y="qrcode",b="block",x="inline-block",m="none",w="flex",S="absolute",z="fixed",M="transparent",k="fill",I={display:b,color:"#000000",lineHeight:"1.4em",fontSize:14,fontWeight:400,fontFamily:"sans-serif",lineCap:"butt",flexDirection:s,flexWrap:"nowrap",textAlign:"left",alignItems:"flex-start",justifyContent:"flex-start",position:"static",transformOrigin:"center center",zIndex:0},B={upx2px:function(t){return window.innerWidth/750*t},getSystemInfoSync:function(){return{screenWidth:window.innerWidth,screenHeight:window.innerHeight}},getImageInfo:function(t){var e=t.src,i=t.success,n=t.fail,r=new Image;r.onload=function(){i({width:r.naturalWidth,height:r.naturalHeight,path:r.src,src:e})},r.onerror=n,r.src=e}},W="object",P="undefined",R=typeof window==W?typeof uni==P||typeof uni!==P&&!uni.addInterceptor?r.WEB:r.H5:typeof swan==W?r.MP_BAIDU:typeof tt==W?r.MP_TOUTIAO:typeof plus===W?r.PLUS:typeof wx==W?r.MP_WEIXIN:void 0,L=R==r.MP_WEIXIN?wx:typeof uni!=P?uni.getImageInfo?{upx2px:function(t){return uni.upx2px(t)},getSystemInfoSync:function(){return uni.getSystemInfoSync()},getImageInfo:function(t){return uni.getImageInfo(t)},downloadFile:function(t){return uni.downloadFile(t)}}:Object.assign(uni,B):typeof window!=P?B:uni;if(!L.upx2px){var O=((L.getSystemInfoSync&&uni.getSystemInfoSync()).screenWidth||375)/750;L.upx2px=function(t){return O*t}}function T(t){return/^-?\d+(\.\d+)?$/.test(t)}function A(t,e,i){if("number"==typeof t)return t;if(T(t))return 1*t;if("string"==typeof t){var n=/^-?([0-9]+)?([.]{1}[0-9]+){0,1}(em|rpx|vw|vh|px|%)$/g.exec(t);if(!t||!n)return 0;var r=n[3];t=parseFloat(t);var o=0;if("rpx"===r)o=L.upx2px(t);else if("px"===r)o=1*t;else if("%"===r&&e)o=t*A(e)/100;else if("em"===r&&e)o=t*A(e||14);else if(["vw","vh"].includes(r)){var s=L.getSystemInfoSync(),a=s.screenWidth,h=s.screenHeight;o=t*("vw"==r?a:h)/100}return 1*o.toFixed(2)}return 0}function F(t){return/%$/.test(t)}var C=function(t){return!(!t||!t.startsWith("linear")&&!t.startsWith("radial"))},j=function(t,e,i,n,r,o){t.startsWith("linear")?function(t,e,i,n,r,o){for(var s=function(t,e,i,n,r){void 0===n&&(n=0);void 0===r&&(r=0);var o=t.match(/([-]?\d{1,3})deg/),s=o&&o[1]?parseFloat(o[1]):0;s>=360&&(s-=360);s<0&&(s+=360);if(0===(s=Math.round(s)))return{x0:Math.round(e/2)+n,y0:i+r,x1:Math.round(e/2)+n,y1:r};if(180===s)return{x0:Math.round(e/2)+n,y0:r,x1:Math.round(e/2)+n,y1:i+r};if(90===s)return{x0:n,y0:Math.round(i/2)+r,x1:e+n,y1:Math.round(i/2)+r};if(270===s)return{x0:e+n,y0:Math.round(i/2)+r,x1:n,y1:Math.round(i/2)+r};var a=Math.round(180*Math.asin(e/Math.sqrt(Math.pow(e,2)+Math.pow(i,2)))/Math.PI);if(s===a)return{x0:n,y0:i+r,x1:e+n,y1:r};if(s===180-a)return{x0:n,y0:r,x1:e+n,y1:i+r};if(s===180+a)return{x0:e+n,y0:r,x1:n,y1:i+r};if(s===360-a)return{x0:e+n,y0:i+r,x1:n,y1:r};var h=0,d=0,c=0,l=0;if(s<a||s>180-a&&s<180||s>180&&s<180+a||s>360-a){var f=s*Math.PI/180,u=s<a||s>360-a?i/2:-i/2,p=Math.tan(f)*u,g=s<a||s>180-a&&s<180?e/2-p:-e/2-p;h=-(c=p+(v=Math.pow(Math.sin(f),2)*g)),d=-(l=u+v/Math.tan(f))}if(s>a&&s<90||s>90&&s<90+a||s>180+a&&s<270||s>270&&s<360-a){var v;f=(90-s)*Math.PI/180,p=s>a&&s<90||s>90&&s<90+a?e/2:-e/2,u=Math.tan(f)*p,g=s>a&&s<90||s>270&&s<360-a?i/2-u:-i/2-u;h=-(c=p+(v=Math.pow(Math.sin(f),2)*g)/Math.tan(f)),d=-(l=u+v)}return h=Math.round(h+e/2)+n,d=Math.round(i/2-d)+r,c=Math.round(c+e/2)+n,l=Math.round(i/2-l)+r,{x0:h,y0:d,x1:c,y1:l}}(r,t,e,i,n),a=s.x0,h=s.y0,d=s.x1,c=s.y1,l=o.createLinearGradient(a,h,d,c),f=r.match(/linear-gradient\((.+)\)/)[1],u=E(f.substring(f.indexOf(",")+1)),p=0;p<u.colors.length;p++)l.addColorStop(u.percents[p],u.colors[p]);o.setFillStyle(l)}(e,i,n,r,t,o):t.startsWith("radial")&&function(t,e,i,n,r,o){for(var s=E(r.match(/radial-gradient\((.+)\)/)[1]),a=Math.round(t/2)+i,h=Math.round(e/2)+n,d=o.createRadialGradient(a,h,0,a,h,Math.max(t,e)/2),c=0;c<s.colors.length;c++)d.addColorStop(s.percents[c],s.colors[c]);o.setFillStyle(d)}(e,i,n,r,t,o)};function E(t){for(var e=[],i=[],n=0,r=t.substring(0,t.length-1).split("%,");n<r.length;n++){var o=r[n];e.push(o.substring(0,o.lastIndexOf(" ")).trim()),i.push(o.substring(o.lastIndexOf(" "),o.length)/100)}return{colors:e,percents:i}}function H(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function D(){return D=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var n in i)Object.prototype.hasOwnProperty.call(i,n)&&(t[n]=i[n])}return t},D.apply(this,arguments)}function Y(t,e){return Y=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Y(t,e)}function U(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function $(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=function(t,e){if(t){if("string"==typeof t)return U(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?U(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var n=0;return function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _(t){return"number"==typeof t}function X(t){return"auto"===t||null===t}function N(t){return/%$/.test(t)}var V,G=0,q=function(){function t(){H(this,"elements",[]),H(this,"afterElements",[]),H(this,"beforeElements",[]),H(this,"ids",[]),H(this,"width",0),H(this,"height",0),H(this,"top",0),H(this,"left",0),H(this,"pre",null),H(this,"offsetX",0),H(this,"offsetY",0),G++,this.id=G}var e=t.prototype;return e.fixedBind=function(t,e){void 0===e&&(e=0),this.container=e?t.parent:t.root,this.container.fixedLine=this,this.fixedAdd(t)},e.fixedAdd=function(t){this.elements.push(t);var e=t.computedStyle.zIndex;(void 0===e?0:e)>=0?this.afterElements.push(t):this.beforeElements.push(t),this.refreshLayout()},e.bind=function(t){this.container=t.parent,this.container.line=null,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),this.isInline=t.isInline(),this.container.line=this,this.outerWidth=t.parent&&t.parent.contentSize.width?t.parent.contentSize.width:1/0,this.add(t)},e.getPreLine=function(){return this.container.lines[this.container.lines.length-2]},e.canIEnter=function(t){return this.outerWidth||t.parent&&t.parent.contentSize.width,!((100*t.offsetSize.width+100*this.width)/100>this.outerWidth)||(this.closeLine(),!1)},e.closeLine=function(){delete this.container.line},e.add=function(t){this.ids.includes(t.id)||(this.ids.push(t.id),this.elements.push(t),this.refreshWidthHeight(t))},e.refreshWidthHeight=function(t){t.offsetSize.height>this.height&&(this.height=t.offsetSize.height),this.width+=t.offsetSize.width||0,(this.container.lineMaxWidth||0)<this.width&&(this.container.lineMaxWidth=this.width)},e.refreshXAlign=function(){if(this.isInline){var t=this.container.contentSize.width-this.width,e=this.container.style.textAlign;"center"===e?t/=2:"left"===e&&(t=0),this.offsetX=t}},e.getOffsetY=function(t){if(!t||!t.style)return 0;var e=(t.style||{}).verticalAlign;return"bottom"===e?this.height-t.contentSize.height:"middle"===e?(this.height-t.contentSize.height)/2:0},e.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){var n=i.elements[e-1],r=i.getOffsetY(t);t.style.top=i.top+r,t.style.left=n?n.offsetSize.left+n.offsetSize.width:i.left,t.getBoxPosition()}))},e.refreshLayout=function(){this.afterElements=this.afterElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex})),this.beforeElements=this.beforeElements.sort((function(t,e){return t.computedStyle.zIndex-e.computedStyle.zIndex}))},t}(),J=((V={})[s]={width:"width",contentWidth:"width",lineMaxWidth:"lineMaxWidth",left:"left",top:"top",height:"height",lineMaxHeight:"lineMaxHeight",marginLeft:"marginLeft"},V[a]={width:"height",contentWidth:"height",lineMaxWidth:"lineMaxHeight",left:"top",top:"left",height:"width",lineMaxHeight:"lineMaxWidth",marginLeft:"marginTop"},V),Q=function(t){var e,i;function n(){var e;return H(function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(e=t.call(this)||this),"outerWidth",0),e.exactValue=0,e.flexTotal=0,e.key=null,e.flexDirection="row",e}i=t,(e=n).prototype=Object.create(i.prototype),e.prototype.constructor=e,Y(e,i);var r=n.prototype;return r.bind=function(t){console.warn("[painter] flex-box 功能未完善谨慎使用"),this.container=t.parent,this.container.line=this,this.container.lines?(this.container.lines.push(this),this.pre=this.getPreLine(),this.top=this.pre.top+this.pre.height,this.left=this.container.contentSize.left):(this.top=this.container.contentSize.top,this.left=this.container.contentSize.left,this.container.lines=[this]),t.parent&&(this.flexDirection=t.parent.style.flexDirection,this.key=J[this.flexDirection]),this.initHeight(t),this.outerWidth=t.parent&&t.parent.contentSize[this.key.contentWidth]?t.parent.contentSize[this.key.contentWidth]:1/0,this.add(t)},r.add=function(t){this.ids.push(t.id),_(t.style.flex)?this.flexTotal+=t.style.flex:_(t.style[this.key.width])&&(this.exactValue+=t.contentSize[this.key.width]),this.elements.push(t),this.refreshWidthHeight(t),t.next||this.closeLine()},r.closeLine=function(){this.calcFlex()},r.initHeight=function(t){this[this.key.height]=0},r.calcFlex=function(){var t=this,e=this.container.contentSize[this.key.contentWidth],i=0;this.elements.forEach((function(n){var r=n.style[t.key.width]||n.contentSize[t.key.width]||0;_(n.style.flex)&&(r=n.style.flex/t.flexTotal*(e-t.exactValue)),n.computedStyle[t.key.width]=r,delete n.line,delete n.lines,delete n.lineMaxWidth,n.getBoxWidthHeight(),i+=n.offsetSize.height})),this.container.height||"column"!=this.flexDirection||(this.height=i)},r.refreshWidthHeight=function(t){this.container.style.alignItems&&!t.style.alignSelf&&(t.style.alignSelf=this.container.style.alignItems),t.offsetSize[this.key.height]>this[this.key.height]&&(this.container[this.key.lineMaxHeight]=this[this.key.height]=t.offsetSize[this.key.height]),this[this.key.width]+=t.offsetSize[this.key.width];var e=Math.min(this[this.key.width],!this.container.contentSize[this.key.width]&&1/0);(this.container[this.key.lineMaxWidth]||0)<e&&(this.container[this.key.lineMaxWidth]=e)},r.refreshXAlign=function(){var t=this,e=this.elements.reduce((function(e,i){return e+i.offsetSize[t.key.width]}),0),i=this.outerWidth==1/0?0:this.outerWidth-Math.max(this[this.key.width],e),n=this.container.style.justifyContent;"center"===n?i/=2:"flex-start"===n?i=0:["space-between","space-around"].includes(n)&&(!function(e,i){void 0===i&&(i=0),i/=t.elements.length+(e?-1:1),t.elements.forEach((function(n,r){var o;e&&!r||(n.style.margin?n.style.margin[t.key.marginLeft]+=i:n.style.margin=((o={})[t.key.marginLeft]=i,o),n.getBoxPosition())})),i=0}("space-between"==n,i),i=0),this.offsetX=i||0,this.refreshYAlign()},r.refreshYAlign=function(){var t=this;if(1==this.container.lines.length)return 0;var e=this.container.lines.reduce((function(e,i){return e+i[t.key.height]}),0);if("center"===this.container.style.alignItems){var i=(this.container.contentSize[this.key.height]-e)/(this.container.lines.length+1);this.container.lines.forEach((function(t){t.offsetY=i}))}if("flex-end"===this.container.style.alignItems){var n=this.container.contentSize[this.key.height]-e;this.container.lines[0].offsetY=n}},r.getOffsetY=function(t){return this.container.lines.length>1?0:"flex-end"===t.style.alignSelf?this.container.contentSize[this.key.height]-t.offsetSize[this.key.height]:"center"===t.style.alignSelf?(this.container.contentSize[this.key.height]-t.offsetSize[this.key.height])/2:0},r.layout=function(t,e){var i=this;this.refreshXAlign(),this.pre?(this.top=this.pre.top+this.pre.height+this.offsetY,this.left=e+this.offsetX):(this.top=Math.max(this.top,this.container.contentSize.top,t)+this.offsetY,this.left=Math.max(this.left,this.container.contentSize.left,e)+this.offsetX),this.elements.forEach((function(t,e){var n=i.elements[e-1],r=i.getOffsetY(t);t.style[i.key.top]=i[i.key.top]+r,t.style[i.key.left]=n?n.offsetSize[i.key.left]+n.offsetSize[i.key.width]:i[i.key.left],t.getBoxPosition()}))},n}(q),Z=v,K=g,et=p,it=y,nt=b,rt=x,ot=w,st=S,at=z,ht=0,dt={left:null,top:null,width:null,height:null},ct=function(){function t(t,e,i,n){var r=this;H(this,"id",ht++),H(this,"style",{left:null,top:null,width:null,height:null}),H(this,"computedStyle",{}),H(this,"originStyle",{}),H(this,"children",{}),H(this,"layoutBox",D({},dt)),H(this,"contentSize",D({},dt)),H(this,"clientSize",D({},dt)),H(this,"borderSize",D({},dt)),H(this,"offsetSize",D({},dt)),this.ctx=n,this.root=i,e&&(this.parent=e),this.name=t.name||t.type,this.attributes=this.getAttributes(t);var o=this.getComputedStyle(t,e&&e.computedStyle);this.isAbsolute=o.position==st,this.isFixed=o.position==at,this.originStyle=o,Object.keys(o).forEach((function(t){Object.defineProperty(r.style,t,{configurable:!0,enumerable:!0,get:function(){return o[t]},set:function(e){o[t]=e}})}));var s={contentSize:D({},this.contentSize),clientSize:D({},this.clientSize),borderSize:D({},this.borderSize),offsetSize:D({},this.offsetSize)};Object.keys(s).forEach((function(t){Object.keys(r[t]).forEach((function(e){Object.defineProperty(r[t],e,{configurable:!0,enumerable:!0,get:function(){return s[t][e]},set:function(i){s[t][e]=i}})}))})),this.computedStyle=this.style}var e=t.prototype;return e.add=function(t){t.parent=this,this.children[t.id]=t},e.getChildren=function(){var t=this;return Object.keys(this.children).map((function(e){return t.children[e]}))},e.prev=function(t){void 0===t&&(t=this);var e=t.parent.getChildren();return e[e.findIndex((function(e){return e.id==t.id}))-1]},e.getLineRect=function(t,e){var i={width:0,height:0},n=e?e.lines:this.parent&&this.parent.lines;return n&&n.find((function(e){return e.ids.includes(t)}))||i},e.getComputedStyle=function(t,e){var i=["color","fontSize","lineHeight","verticalAlign","fontWeight","textAlign"],n=t.css,r=void 0===n?{}:n,o=t.type,s=void 0===o?et:o,a=D({},I);if([K,Z,it].includes(s)&&!r.display&&(a.display=rt),e)for(var h=0;h<i.length;h++){var d=i[h];(r[d]||e[d])&&(r[d]=r[d]||e[d])}for(var c=function(){var t=f[l],e=r[t];if(/-/.test(t)&&(t=t.replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})),a.key=e),/^(box|text)?shadow$/i.test(t)){var i=[];return e.replace(/((\d+(rpx|px|vw|vh)?\s+?){3})(.+)/,(function(){for(var t=arguments.length,e=new Array(t),n=0;n<t;n++)e[n]=arguments[n];i=e[1].match(/\d+(rpx|px|vh|vw)?/g).map((function(t){return A(t)})).concat(e[4])})),/^text/.test(t)?a.textShadow=i:a.boxShadow=i,"continue"}if(/^border/i.test(t)&&!/radius$/i.test(t)){var n,o=t.match(/^border([BTRLa-z]+)?/)[0],h=t.match(/[W|S|C][a-z]+/),d=e.replace(/([\(,])\s+|\s+([\),])/g,"$1$2").split(" ").map((function(t){return/^\d/.test(t)?A(t,""):t}));if(a[o]||(a[o]={}),1==d.length&&h)a[o][o+h[0]]=d[0];else a[o]=((n={})[o+"Width"]=T(d[0])?d[0]:0,n[o+"Style"]=d[1]||"solid",n[o+"Color"]=d[2]||"black",n);return"continue"}if(/^background(color)?$/i.test(t))return a.backgroundColor=e,"continue";if(/^objectPosition$/i.test(t))return a[t]=e.split(" "),"continue";if(/padding|margin|radius/i.test(t)){var c=/radius$/i.test(t),u=c?"borderRadius":t.match(/[a-z]+/)[0],p=[0,0,0,0].map((function(t,e){return c?["borderTopLeftRadius","borderTopRightRadius","borderBottomRightRadius","borderBottomLeftRadius"][e]:[u+"Top",u+"Right",u+"Bottom",u+"Left"][e]})),g="margin";if("padding"===t||t===g||/^(border)?radius$/i.test(t)){var v,y=(""+e).split(" ").map((function(e){return/^\d+(rpx|px|vh|vw)?$/.test(e)?A(e):t!=g&&/auto/.test(e)?0:e}),[])||[0],b=c?"borderRadius":t,x=y[0],m=y[1],w=y[2],S=y[3];a[b]=((v={})[p[0]]=X(x)?0:x,v[p[1]]=T(m)||X(m)?m:x,v[p[2]]=X(T(w)?w:x)?0:T(w)?w:x,v[p[3]]=T(S)?S:m||x,v)}else{var z;if("object"==typeof a[u])a[u][t]=u==g&&X(e)||N(e)?e:A(e);else a[u]=((z={})[p[0]]=a[u]||0,z[p[1]]=a[u]||0,z[p[2]]=a[u]||0,z[p[3]]=a[u]||0,z),a[u][t]=u==g&&X(e)||N(e)?e:A(e)}return"continue"}if(/^transform$/i.test(t))return a[t]={},e.replace(/([a-zA-Z]+)\(([0-9,-\.%rpxdeg\s]+)\)/g,(function(e,i,n){var o=n.split(",").map((function(t){return t.replace(/(^\s*)|(\s*$)/g,"")})),s=function(t,e){return t.includes("deg")?1*t:e&&!N(e)?A(t,e):t};i.includes("matrix")?a[t][i]=o.map((function(t){return 1*t})):i.includes("rotate")?a[t][i]=1*n.match(/^-?\d+(\.\d+)?/)[0]:/[X, Y]/.test(i)?a[t][i]=/[X]/.test(i)?s(o[0],r.width):s(o[0],r.height):(a[t][i+"X"]=s(o[0],r.width),a[t][i+"Y"]=s(o[1]||o[0],r.height))})),"continue";/^font$/i.test(t)&&console.warn("font 不支持简写"),/^left|top$/i.test(t)&&![st,at].includes(r.position)?a[t]=0:a[t]=/^[\d\.]+(px|rpx|vw|vh)?$/.test(e)?A(e):/em$/.test(e)&&s==K?A(e,r.fontSize):e},l=0,f=Object.keys(r);l<f.length;l++)c();return a},e.setPosition=function(t,e){var i={left:"width",top:"height",right:"width",bottom:"height"};Object.keys(i).forEach((function(n){var r="right"==n?"left":"top";["right","bottom"].includes(n)&&void 0!==t.style[n]&&"number"!=typeof t.originStyle[r]?t.style[r]=e[i[n]]-t.offsetSize[i[n]]-A(t.style[n],e[i[n]]):t.style[n]=A(t.style[n],e[i[n]])}))},e.getAttributes=function(t){var e=t.attributes||{};return(t.url||t.src)&&(e.src=e.src||t.url||t.src),t.replace&&(e.replace=t.replace),t.text&&(e.text=t.text),e},e.getOffsetSize=function(t,e,i){void 0===i&&(i=o[3]);var n=e||{},r=n.margin,s=(r=void 0===r?{}:r).marginLeft,a=void 0===s?0:s,h=r.marginTop,d=void 0===h?0:h,c=r.marginRight,l=void 0===c?0:c,f=r.marginBottom,u=void 0===f?0:f,p=n.padding,g=(p=void 0===p?{}:p).paddingLeft,v=void 0===g?0:g,y=p.paddingTop,b=void 0===y?0:y,x=p.paddingRight,m=void 0===x?0:x,w=p.paddingBottom,S=void 0===w?0:w,z=n.border,M=(z=void 0===z?{}:z).borderWidth,k=void 0===M?0:M,I=n.borderTop,B=(I=void 0===I?{}:I).borderTopWidth,W=void 0===B?k:B,P=n.borderBottom,R=(P=void 0===P?{}:P).borderBottomWidth,L=void 0===R?k:R,O=n.borderRight,T=(O=void 0===O?{}:O).borderRightWidth,A=void 0===T?k:T,F=n.borderLeft,C=(F=void 0===F?{}:F).borderLeftWidth,j=void 0===C?k:C,E=a<0&&l<0?Math.abs(a+l):0,H=d<0&&u<0?Math.abs(d+u):0,D=a>=0&&l<0,Y=d>=0&&u<0;return i==o[0]&&(this[i].left=t.left+a+v+j+(D?2*-l:0),this[i].top=t.top+d+b+W+(Y?2*-u:0),this[i].width=t.width+(this[i].widthAdd?0:E),this[i].height=t.height+(this[i].heightAdd?0:H),this[i].widthAdd=E,this[i].heightAdd=H),i==o[1]&&(this[i].left=t.left+a+j+(D<0?-l:0),this[i].top=t.top+d+W+(Y?-u:0),this[i].width=t.width+v+m,this[i].height=t.height+b+S),i==o[2]&&(this[i].left=t.left+a+j/2+(D<0?-l:0),this[i].top=t.top+d+W/2+(Y?-u:0),this[i].width=t.width+v+m+j/2+A/2,this[i].height=t.height+b+S+L/2+W/2),i==o[3]&&(this[i].left=t.left+(D<0?-l:0),this[i].top=t.top+(Y?-u:0),this[i].width=t.width+v+m+j+A+a+l,this[i].height=t.height+b+S+L+W+u+d),this[i]},e.layoutBoxUpdate=function(t,e,i,n){var r=this;if(void 0===i&&(i=-1),"border-box"==e.boxSizing){var s=e||{},a=s.border,h=(a=void 0===a?{}:a).borderWidth,d=void 0===h?0:h,c=s.borderTop,l=(c=void 0===c?{}:c).borderTopWidth,f=void 0===l?d:l,u=s.borderBottom,p=(u=void 0===u?{}:u).borderBottomWidth,g=void 0===p?d:p,v=s.borderRight,y=(v=void 0===v?{}:v).borderRightWidth,b=void 0===y?d:y,x=s.borderLeft,m=(x=void 0===x?{}:x).borderLeftWidth,w=void 0===m?d:m,S=s.padding,z=(S=void 0===S?{}:S).paddingTop,M=void 0===z?0:z,k=S.paddingRight,I=void 0===k?0:k,B=S.paddingBottom,W=void 0===B?0:B,P=S.paddingLeft,R=void 0===P?0:P;i||(t.width-=R+I+b+w),1!==i||n||(t.height-=M+W+f+g)}this.layoutBox&&(o.forEach((function(i){return r.layoutBox[i]=r.getOffsetSize(t,e,i)})),this.layoutBox=Object.assign({},this.layoutBox,this.layoutBox.borderSize))},e.getBoxPosition=function(){var t=this.computedStyle,e=this.fixedLine,i=this.lines,n=t.left,r=void 0===n?0:n,o=t.top,s=void 0===o?0:o,a=t.padding||{},h=a.paddingBottom,d=void 0===h?0:h,c=a.paddingRight,l=void 0===c?0:c,f=D({},this.contentSize,{left:r,top:s}),u=this.contentSize.top-this.offsetSize.top,p=this.contentSize.left-this.offsetSize.left;if(this.root.fixedLine&&!this.root.isDone){this.root.isDone=!0;for(var g,v=$(this.root.fixedLine.elements);!(g=v()).done;){var y=g.value;y.setPosition(y,this.root.offsetSize),y.getBoxPosition()}}if(e)for(var b,x=$(e.elements);!(b=x()).done;){var m=b.value;m.setPosition(m,f),m.style.left+=r+p+l,m.style.top+=s+u+d,m.getBoxPosition()}if(i)for(var w,S=$(i);!(w=S()).done;){w.value.layout(f.top+u,f.left+p)}return this.layoutBoxUpdate(f,t),this.layoutBox},e.getBoxState=function(t,e){return this.isBlock(t)||this.isBlock(e)},e.isBlock=function(t){return void 0===t&&(t=this),t&&t.style.display==nt},e.isFlex=function(t){return void 0===t&&(t=this),t&&t.style.display==ot},e.isInFlow=function(){return!(this.isAbsolute||this.isFixed)},e.inFlexBox=function(t){return void 0===t&&(t=this),!!t.isInFlow()&&(!!t.parent&&(!(!t.parent||t.parent.style.display!==ot)||void 0))},e.isInline=function(t){return void 0===t&&(t=this),t&&t.style.display==rt},e.contrastSize=function(t,e,i){var n=t;return i&&(n=Math.min(n,i)),e&&(n=Math.max(n,e)),n},e.measureText=function(t,e){var i=this.ctx.measureText(t),n=i.width,r=i.actualBoundingBoxAscent,o=i.actualBoundingBoxDescent;return{ascent:r,descent:o,width:n,fontHeight:r+o||.7*e+1}},e.getBoxWidthHeight=function(){var t=this,e=this.name,i=this.computedStyle,n=this.attributes,r=this.parent,o=void 0===r?{}:r,s=this.ctx,a=this.getChildren(),h=i.left,d=void 0===h?0:h,c=i.top,l=void 0===c?0:c,f=i.bottom,u=i.right,p=i.width,g=void 0===p?0:p,v=i.minWidth,y=i.maxWidth,b=i.minHeight,x=i.maxHeight,m=i.height,w=void 0===m?0:m,S=i.fontSize,z=void 0===S?14:S,M=i.fontWeight,k=i.fontFamily,I=i.fontStyle,B=i.position,W=i.lineClamp,P=i.lineHeight,R=i.padding,L=void 0===R?{}:R,O=i.margin,T=void 0===O?{}:O,F=i.border,C=(F=void 0===F?{}:F).borderWidth,j=void 0===C?0:C,E=i.borderRight,H=(E=void 0===E?{}:E).borderRightWidth,D=void 0===H?j:H,Y=i.borderLeft,U=(Y=void 0===Y?{}:Y).borderLeftWidth,$=void 0===U?j:U,_=o.contentSize&&o.contentSize.width,X=o.contentSize&&o.contentSize.height;if(N(g)&&_&&(g=A(g,_)),N(g)&&!_&&(g=null),N(w)&&X&&(w=A(w,X)),N(w)&&!X&&(w=null),N(v)&&_&&(v=A(v,_)),N(y)&&_&&(y=A(y,_)),N(b)&&X&&(b=A(b,X)),N(x)&&X&&(x=A(x,X)),i.padding&&_)for(var V in i.padding)Object.hasOwnProperty.call(i.padding,V)&&(i.padding[V]=A(i.padding[V],_));var G=L.paddingRight,J=void 0===G?0:G,tt=L.paddingLeft,it=void 0===tt?0:tt;if(i.margin&&[i.margin.marginLeft,i.margin.marginRight].includes("auto"))if(g){var nt=_&&_-g-J-it-$-D||0;i.margin.marginLeft==i.margin.marginRight?i.margin.marginLeft=i.margin.marginRight=nt/2:"auto"==i.margin.marginLeft?i.margin.marginLeft=nt:i.margin.marginRight=nt}else i.margin.marginLeft=i.margin.marginRight=0;var rt=T.marginRight,ot=void 0===rt?0:rt,at=T.marginLeft,ht={width:g,height:w,left:0,top:0},dt=it+J+$+D+(void 0===at?0:at)+ot;if(this.offsetWidth=dt,e==K&&!this.attributes.widths){var ct=n.text||"";s.save(),s.setFonts({fontFamily:k,fontSize:z,fontWeight:M,fontStyle:I});var lt=new Map;ct.split("\n").map((function(e){var i=e.split("").map((function(e){var i=lt.get(e);if(i)return i;var n=t.measureText(e,z).width;return lt.set(e,n),n})),n=t.measureText(e,z),r=n.fontHeight,o=n.ascent,s=n.descent;t.attributes.fontHeight=r,t.attributes.ascent=o,t.attributes.descent=s,t.attributes.widths||(t.attributes.widths=[]),t.attributes.widths.push({widths:i,total:i.reduce((function(t,e){return t+e}),0)})})),s.restore()}if(e==Z&&null==g){var ft=n.width,ut=n.height;ht.width=this.contrastSize(Math.round(ft*w/ut)||0,v,y),this.layoutBoxUpdate(ht,i,0)}if(e==K&&null==g){var pt=this.attributes.widths,gt=Math.max.apply(Math,pt.map((function(t){return t.total})));if(o&&_>0&&(gt>_||this.isBlock(this))&&!this.isAbsolute&&!this.isFixed)gt=_-dt;if(o&&!_&&o.parent&&this.isFlex(o)){var vt=this.prev(),yt=this.prev(o),bt=o.parent.contentSize.width-yt.offsetSize.left-yt.offsetSize.width;if(gt>bt){if("row"==o.style.flexDirection){var xt=vt?vt.offsetSize.width:0;gt=bt-xt>0?bt-xt:bt}else gt=bt;o.style.boxSizing="",gt-=dt+o.offsetWidth}this.isCalc=!0}ht.width=this.contrastSize(gt,v,y),this.layoutBoxUpdate(ht,i,0)}if(e==K&&(o.style.flex||!this.attributes.lines)){var mt=this.attributes.widths.length;this.attributes.widths.forEach((function(t){return t.widths.reduce((function(t,e,i){return t+e>ht.width?(mt++,e):t+e}),0)})),mt=W&&mt>W?W:mt,this.attributes.lines=mt}if(e==Z&&null==w){var wt=n.width,St=n.height;ht.height=this.contrastSize(A(ht.width*St/wt)||0,b,x),this.layoutBoxUpdate(ht,i,1)}e==K&&null==w&&(P=A(P,z),ht.height=this.contrastSize(A(this.attributes.lines*P),b,x),this.layoutBoxUpdate(ht,i,1,!0)),o&&o.children&&_&&!this.isFlex(o)&&([et,K].includes(e)&&this.isFlex()||e==et&&this.isBlock(this)&&this.isInFlow())&&(ht.width=this.contrastSize(_-dt,v,y),this.layoutBoxUpdate(ht,i)),g&&!N(g)&&(ht.width=this.contrastSize(g,v,y),this.layoutBoxUpdate(ht,i,0)),w&&!N(w)&&(ht.height=this.contrastSize(ht.height,b,x),this.layoutBoxUpdate(ht,i,1));var zt=0;if(a.length){var Mt=null;a.forEach((function(e,n){e.getBoxWidthHeight();var r=a[n+1];if(r&&r.isInFlow()&&(e.next=r),e.isInFlow()&&!e.inFlexBox()){var o=t.getBoxState(Mt,e);t.line&&t.line.canIEnter(e)&&!o?t.line.add(e):(new q).bind(e),Mt=e}else e.inFlexBox()?t.line&&(t.line.canIEnter(e)||"nowrap"==i.flexWrap)?t.line.add(e):(new Q).bind(e):e.isFixed?t.root.fixedLine?t.root.fixedLine.fixedAdd(e):(new q).fixedBind(e):t.fixedLine?t.fixedLine.fixedAdd(e):(new q).fixedBind(e,1)})),this.lines&&(zt=this.lines.reduce((function(t,e){return t+e.height}),0))}var kt=0,It=0;if(!g&&(this.isAbsolute||this.isFixed)&&_){var Bt=B==st?_:this.root.width,Wt=Bt-(N(d)?A(d,Bt):d)-(N(u)?A(u,Bt):u);kt=i.left?Wt:this.lineMaxWidth}if(!w&&(null!=l?l:this.isAbsolute||this.isFixed&&X)){var Pt=B==st?X:this.root.height,Rt=Pt-(N(l)?A(l,Pt):l)-(N(f)?A(f,Pt):f);It=i.top?Rt:0}if(g&&!N(g)||ht.width||(ht.width=kt||this.contrastSize((this.isBlock(this)&&!this.isInFlow()?_||o.lineMaxWidth:this.lineMaxWidth)||this.lineMaxWidth,v,y),this.layoutBoxUpdate(ht,i,0)),w||!zt&&!It||(ht.height=It||this.contrastSize(zt,b,x),this.layoutBoxUpdate(ht,i)),i.borderRadius&&this.borderSize&&this.borderSize.width)for(var V in i.borderRadius)Object.hasOwnProperty.call(i.borderRadius,V)&&(i.borderRadius[V]=A(i.borderRadius[V],this.borderSize.width));return this.layoutBox},e.layout=function(){return this.getBoxWidthHeight(),this.root.offsetSize=this.offsetSize,this.getBoxPosition(),this.offsetSize},t}(),lt=function(){var t,e,i,n,r,o,s=[0,11,15,19,23,27,31,16,18,20,22,24,26,28,20,22,24,24,26,28,28,22,24,24,26,26,28,28,24,24,26,26,26,28,28,24,26,26,26,28,28],a=[3220,1468,2713,1235,3062,1890,2119,1549,2344,2936,1117,2583,1330,2470,1667,2249,2028,3780,481,4011,142,3098,831,3445,592,2517,1776,2234,1951,2827,1070,2660,1345,3177],h=[30660,29427,32170,30877,26159,25368,27713,26998,21522,20773,24188,23371,17913,16590,20375,19104,13663,12392,16177,14854,9396,8579,11994,11245,5769,5054,7399,6608,1890,597,3340,2107],d=[1,0,19,7,1,0,16,10,1,0,13,13,1,0,9,17,1,0,34,10,1,0,28,16,1,0,22,22,1,0,16,28,1,0,55,15,1,0,44,26,2,0,17,18,2,0,13,22,1,0,80,20,2,0,32,18,2,0,24,26,4,0,9,16,1,0,108,26,2,0,43,24,2,2,15,18,2,2,11,22,2,0,68,18,4,0,27,16,4,0,19,24,4,0,15,28,2,0,78,20,4,0,31,18,2,4,14,18,4,1,13,26,2,0,97,24,2,2,38,22,4,2,18,22,4,2,14,26,2,0,116,30,3,2,36,22,4,4,16,20,4,4,12,24,2,2,68,18,4,1,43,26,6,2,19,24,6,2,15,28,4,0,81,20,1,4,50,30,4,4,22,28,3,8,12,24,2,2,92,24,6,2,36,22,4,6,20,26,7,4,14,28,4,0,107,26,8,1,37,22,8,4,20,24,12,4,11,22,3,1,115,30,4,5,40,24,11,5,16,20,11,5,12,24,5,1,87,22,5,5,41,24,5,7,24,30,11,7,12,24,5,1,98,24,7,3,45,28,15,2,19,24,3,13,15,30,1,5,107,28,10,1,46,28,1,15,22,28,2,17,14,28,5,1,120,30,9,4,43,26,17,1,22,28,2,19,14,28,3,4,113,28,3,11,44,26,17,4,21,26,9,16,13,26,3,5,107,28,3,13,41,26,15,5,24,30,15,10,15,28,4,4,116,28,17,0,42,26,17,6,22,28,19,6,16,30,2,7,111,28,17,0,46,28,7,16,24,30,34,0,13,24,4,5,121,30,4,14,47,28,11,14,24,30,16,14,15,30,6,4,117,30,6,14,45,28,11,16,24,30,30,2,16,30,8,4,106,26,8,13,47,28,7,22,24,30,22,13,15,30,10,2,114,28,19,4,46,28,28,6,22,28,33,4,16,30,8,4,122,30,22,3,45,28,8,26,23,30,12,28,15,30,3,10,117,30,3,23,45,28,4,31,24,30,11,31,15,30,7,7,116,30,21,7,45,28,1,37,23,30,19,26,15,30,5,10,115,30,19,10,47,28,15,25,24,30,23,25,15,30,13,3,115,30,2,29,46,28,42,1,24,30,23,28,15,30,17,0,115,30,10,23,46,28,10,35,24,30,19,35,15,30,17,1,115,30,14,21,46,28,29,19,24,30,11,46,15,30,13,6,115,30,14,23,46,28,44,7,24,30,59,1,16,30,12,7,121,30,12,26,47,28,39,14,24,30,22,41,15,30,6,14,121,30,6,34,47,28,46,10,24,30,2,64,15,30,17,4,122,30,29,14,46,28,49,10,24,30,24,46,15,30,4,18,122,30,13,32,46,28,48,14,24,30,42,32,15,30,20,4,117,30,40,7,47,28,43,22,24,30,10,67,15,30,19,6,118,30,18,31,47,28,34,34,24,30,20,61,15,30],c=[255,0,1,25,2,50,26,198,3,223,51,238,27,104,199,75,4,100,224,14,52,141,239,129,28,193,105,248,200,8,76,113,5,138,101,47,225,36,15,33,53,147,142,218,240,18,130,69,29,181,194,125,106,39,249,185,201,154,9,120,77,228,114,166,6,191,139,98,102,221,48,253,226,152,37,179,16,145,34,136,54,208,148,206,143,150,219,189,241,210,19,92,131,56,70,64,30,66,182,163,195,72,126,110,107,58,40,84,250,133,186,61,202,94,155,159,10,21,121,43,78,212,229,172,115,243,167,87,7,112,192,247,140,128,99,13,103,74,222,237,49,197,254,24,227,165,153,119,38,184,180,124,17,68,146,217,35,32,137,46,55,63,209,91,149,188,207,205,144,135,151,178,220,252,190,97,242,86,211,171,20,42,93,158,132,60,57,83,71,109,65,162,31,45,67,216,183,123,164,118,196,23,73,236,127,12,111,246,108,161,59,82,41,157,85,170,251,96,134,177,187,204,62,90,203,89,95,176,156,169,160,81,11,245,22,235,122,117,44,215,79,174,213,233,230,231,173,232,116,214,244,234,168,80,88,175],l=[1,2,4,8,16,32,64,128,29,58,116,232,205,135,19,38,76,152,45,90,180,117,234,201,143,3,6,12,24,48,96,192,157,39,78,156,37,74,148,53,106,212,181,119,238,193,159,35,70,140,5,10,20,40,80,160,93,186,105,210,185,111,222,161,95,190,97,194,153,47,94,188,101,202,137,15,30,60,120,240,253,231,211,187,107,214,177,127,254,225,223,163,91,182,113,226,217,175,67,134,17,34,68,136,13,26,52,104,208,189,103,206,129,31,62,124,248,237,199,147,59,118,236,197,151,51,102,204,133,23,46,92,184,109,218,169,79,158,33,66,132,21,42,84,168,77,154,41,82,164,85,170,73,146,57,114,228,213,183,115,230,209,191,99,198,145,63,126,252,229,215,179,123,246,241,255,227,219,171,75,150,49,98,196,149,55,110,220,165,87,174,65,130,25,50,100,200,141,7,14,28,56,112,224,221,167,83,166,81,162,89,178,121,242,249,239,195,155,43,86,172,69,138,9,18,36,72,144,61,122,244,245,247,243,251,235,203,139,11,22,44,88,176,125,250,233,207,131,27,54,108,216,173,71,142,0],f=[],u=[],p=[],g=[],v=[],y=2;function b(t,e){var i;t>e&&(i=t,t=e,e=i),i=e,i*=e,i+=e,i>>=1,g[i+=t]=1}function x(t,i){var n;for(p[t+e*i]=1,n=-2;n<2;n++)p[t+n+e*(i-2)]=1,p[t-2+e*(i+n+1)]=1,p[t+2+e*(i+n)]=1,p[t+n+1+e*(i+2)]=1;for(n=0;n<2;n++)b(t-1,i+n),b(t+1,i-n),b(t-n,i-1),b(t+n,i+1)}function m(t){for(;t>=255;)t=((t-=255)>>8)+(255&t);return t}var w=[];function S(t,e,i,n){var r,o,s;for(r=0;r<n;r++)f[i+r]=0;for(r=0;r<e;r++){if(255!=(s=c[f[t+r]^f[i]]))for(o=1;o<n;o++)f[i+o-1]=f[i+o]^l[m(s+w[n-o])];else for(o=i;o<i+n;o++)f[o]=f[o+1];f[i+n-1]=255==s?0:l[m(s+w[0])]}}function z(t,e){var i;return t>e&&(i=t,t=e,e=i),i=e,i+=e*e,i>>=1,g[i+=t]}function M(t){var i,n,r,o;switch(t){case 0:for(n=0;n<e;n++)for(i=0;i<e;i++)i+n&1||z(i,n)||(p[i+n*e]^=1);break;case 1:for(n=0;n<e;n++)for(i=0;i<e;i++)1&n||z(i,n)||(p[i+n*e]^=1);break;case 2:for(n=0;n<e;n++)for(r=0,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 3:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=o,i=0;i<e;i++,r++)3==r&&(r=0),r||z(i,n)||(p[i+n*e]^=1);break;case 4:for(n=0;n<e;n++)for(r=0,o=n>>1&1,i=0;i<e;i++,r++)3==r&&(r=0,o=!o),o||z(i,n)||(p[i+n*e]^=1);break;case 5:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+!(!r|!o)||z(i,n)||(p[i+n*e]^=1);break;case 6:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(i&n&1)+(r&&r==o)&1||z(i,n)||(p[i+n*e]^=1);break;case 7:for(o=0,n=0;n<e;n++,o++)for(3==o&&(o=0),r=0,i=0;i<e;i++,r++)3==r&&(r=0),(r&&r==o)+(i+n&1)&1||z(i,n)||(p[i+n*e]^=1)}}function k(t){var e,i=0;for(e=0;e<=t;e++)v[e]>=5&&(i+=3+v[e]-5);for(e=3;e<t-1;e+=2)v[e-2]==v[e+2]&&v[e+2]==v[e-1]&&v[e-1]==v[e+1]&&3*v[e-1]==v[e]&&(0==v[e-3]||e+3>t||3*v[e-3]>=4*v[e]||3*v[e+3]>=4*v[e])&&(i+=40);return i}function I(){var t,i,n,r,o,s=0,a=0;for(i=0;i<e-1;i++)for(t=0;t<e-1;t++)(p[t+e*i]&&p[t+1+e*i]&&p[t+e*(i+1)]&&p[t+1+e*(i+1)]||!(p[t+e*i]||p[t+1+e*i]||p[t+e*(i+1)]||p[t+1+e*(i+1)]))&&(s+=3);for(i=0;i<e;i++){for(v[0]=0,n=r=t=0;t<e;t++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,a+=(r=o)?1:-1;s+=k(n)}a<0&&(a=-a);var h=a,d=0;for(h+=h<<2,h<<=1;h>e*e;)h-=e*e,d++;for(s+=10*d,t=0;t<e;t++){for(v[0]=0,n=r=i=0;i<e;i++)(o=p[t+e*i])==r?v[n]++:v[++n]=1,r=o;s+=k(n)}return s}var B=null;return{api:{get ecclevel(){return y},set ecclevel(t){y=t},get size(){return _size},set size(t){_size=t},get canvas(){return B},set canvas(t){B=t},getFrame:function(v){return function(v){var k,B,W,P,R,L,O,T;P=v.length,t=0;do{if(t++,W=4*(y-1)+16*(t-1),i=d[W++],n=d[W++],r=d[W++],o=d[W],P<=(W=r*(i+n)+n-3+(t<=9)))break}while(t<40);for(e=17+4*t,R=r+(r+o)*(i+n)+n,P=0;P<R;P++)u[P]=0;for(f=v.slice(0),P=0;P<e*e;P++)p[P]=0;for(P=0;P<(e*(e+1)+1)/2;P++)g[P]=0;for(P=0;P<3;P++){for(W=0,B=0,1==P&&(W=e-7),2==P&&(B=e-7),p[B+3+e*(W+3)]=1,k=0;k<6;k++)p[B+k+e*W]=1,p[B+e*(W+k+1)]=1,p[B+6+e*(W+k)]=1,p[B+k+1+e*(W+6)]=1;for(k=1;k<5;k++)b(B+k,W+1),b(B+1,W+k+1),b(B+5,W+k),b(B+k+1,W+5);for(k=2;k<4;k++)p[B+k+e*(W+2)]=1,p[B+2+e*(W+k+1)]=1,p[B+4+e*(W+k)]=1,p[B+k+1+e*(W+4)]=1}if(t>1)for(P=s[t],B=e-7;;){for(k=e-7;k>P-3&&(x(k,B),!(k<P));)k-=P;if(B<=P+9)break;x(6,B-=P),x(B,6)}for(p[8+e*(e-8)]=1,B=0;B<7;B++)b(7,B),b(e-8,B),b(7,B+e-7);for(k=0;k<8;k++)b(k,7),b(k+e-8,7),b(k,e-8);for(k=0;k<9;k++)b(k,8);for(k=0;k<8;k++)b(k+e-8,8),b(8,k);for(B=0;B<7;B++)b(8,B+e-7);for(k=0;k<e-14;k++)1&k?(b(8+k,6),b(6,8+k)):(p[8+k+6*e]=1,p[6+e*(8+k)]=1);if(t>6)for(P=a[t-7],W=17,k=0;k<6;k++)for(B=0;B<3;B++,W--)1&(W>11?t>>W-12:P>>W)?(p[5-k+e*(2-B+e-11)]=1,p[2-B+e-11+e*(5-k)]=1):(b(5-k,2-B+e-11),b(2-B+e-11,5-k));for(B=0;B<e;B++)for(k=0;k<=B;k++)p[k+e*B]&&b(k,B);for(R=f.length,L=0;L<R;L++)u[L]=f.charCodeAt(L);if(f=u.slice(0),R>=(k=r*(i+n)+n)-2&&(R=k-2,t>9&&R--),L=R,t>9){for(f[L+2]=0,f[L+3]=0;L--;)P=f[L],f[L+3]|=255&P<<4,f[L+2]=P>>4;f[2]|=255&R<<4,f[1]=R>>4,f[0]=64|R>>12}else{for(f[L+1]=0,f[L+2]=0;L--;)P=f[L],f[L+2]|=255&P<<4,f[L+1]=P>>4;f[1]|=255&R<<4,f[0]=64|R>>4}for(L=R+3-(t<10);L<k;)f[L++]=236,f[L++]=17;for(w[0]=1,L=0;L<o;L++){for(w[L+1]=1,O=L;O>0;O--)w[O]=w[O]?w[O-1]^l[m(c[w[O]]+L)]:w[O-1];w[0]=l[m(c[w[0]]+L)]}for(L=0;L<=o;L++)w[L]=c[w[L]];for(W=k,B=0,L=0;L<i;L++)S(B,r,W,o),B+=r,W+=o;for(L=0;L<n;L++)S(B,r+1,W,o),B+=r+1,W+=o;for(B=0,L=0;L<r;L++){for(O=0;O<i;O++)u[B++]=f[L+O*r];for(O=0;O<n;O++)u[B++]=f[i*r+L+O*(r+1)]}for(O=0;O<n;O++)u[B++]=f[i*r+L+O*(r+1)];for(L=0;L<o;L++)for(O=0;O<i+n;O++)u[B++]=f[k+L+O*o];for(f=u,k=B=e-1,W=R=1,T=(r+o)*(i+n)+n,L=0;L<T;L++)for(P=f[L],O=0;O<8;O++,P<<=1){128&P&&(p[k+e*B]=1);do{R?k--:(k++,W?0!=B?B--:(W=!W,6==(k-=2)&&(k--,B=9)):B!=e-1?B++:(W=!W,6==(k-=2)&&(k--,B-=8))),R=!R}while(z(k,B))}for(f=p.slice(0),P=0,B=3e4,W=0;W<8&&(M(W),(k=I())<B&&(B=k,P=W),7!=P);W++)p=f.slice(0);for(P!=W&&M(P),B=h[P+(y-1<<3)],W=0;W<8;W++,B>>=1)1&B&&(p[e-1-W+8*e]=1,W<6?p[8+e*W]=1:p[8+e*(W+1)]=1);for(W=0;W<7;W++,B>>=1)1&B&&(p[8+e*(e-7+W)]=1,W?p[6-W+8*e]=1:p[7+8*e]=1);return p}(v)},utf16to8:function(t){var e,i,n,r;for(e="",n=t.length,i=0;i<n;i++)(r=t.charCodeAt(i))>=1&&r<=127?e+=t.charAt(i):r>2047?(e+=String.fromCharCode(224|r>>12&15),e+=String.fromCharCode(128|r>>6&63),e+=String.fromCharCode(128|r>>0&63)):(e+=String.fromCharCode(192|r>>6&31),e+=String.fromCharCode(128|r>>0&63));return e},draw:function(t,i,n,r,o){i.drawView(n,r);var s=i.ctx,a=n.contentSize,h=a.width,d=a.height,c=a.left,l=a.top;r.borderRadius,r.backgroundColor;var f=r.color,u=void 0===f?"#000000":f;r.border,n.contentSize.left,n.borderSize.left,n.contentSize.top,n.borderSize.top;if(y=o||y,s){s.save(),i.setOpacity(r),i.setTransform(n,r);var p=Math.min(h,d);t=this.utf16to8(t);var g=this.getFrame(t),v=p/e;s.setFillStyle(u);for(var b=0;b<e;b++)for(var x=0;x<e;x++)g[x*e+b]&&s.fillRect(c+v*b,l+v*x,v,v);s.restore(),i.setBorder(n,r)}else console.warn("No canvas provided to draw QR code in!")}}}}(),ft=v,ut=g,pt=y,gt=p,vt=h,yt=d,bt=c,xt=l,mt=f,wt=u,St=function(){function t(t){var e,i=this;this.v="1.9.3.5",this.id=null,this.pixelRatio=1,this.width=0,this.height=0,this.sleep=1e3/30,this.isInit=!1,this.count=0,this.isRate=!1,this.isDraw=!0,this.isCache=!0,this.fixed="",this.useCORS=!1,this.imageBus=[],this.createImage=function(t,e){return new Promise((function(n,r){var o=null;window||i.canvas.createImage?(o=i.canvas&&i.canvas.createImage?i.canvas.createImage():new Image,e&&o.setAttribute("crossOrigin","Anonymous"),o.src=t,o.onload=function(){n({width:o.naturalWidth||o.width,height:o.naturalHeight||o.height,path:o,src:this.src})},o.onerror=function(t){r(t)}):r({fail:"getImageInfo fail",src:t})}))},this.options=t,Object.assign(this,t),this.ctx=((e=t.context).setFonts=function(t){var i=t.fontFamily,n=void 0===i?"sans-serif":i,o=t.fontSize,s=void 0===o?14:o,a=t.fontWeight,h=void 0===a?"normal":a,d=t.fontStyle,c=void 0===d?"normal":d;R==r.MP_TOUTIAO&&(h="bold"==h?"bold":"",c="italic"==c?"italic":""),e.font="".concat(c," ").concat(h," ").concat(Math.round(s),"px ").concat(n)},e.draw&&e.setFillStyle?e:Object.assign(e,{setStrokeStyle:function(t){e.strokeStyle=t},setLineWidth:function(t){e.lineWidth=t},setLineCap:function(t){e.lineCap=t},setFillStyle:function(t){e.fillStyle=t},setFontSize:function(t){e.font="".concat(String(t),"px sans-serif")},setGlobalAlpha:function(t){e.globalAlpha=t},setLineJoin:function(t){e.lineJoin=t},setTextAlign:function(t){e.textAlign=t},setMiterLimit:function(t){e.miterLimit=t},setShadow:function(t,i,n,r){e.shadowOffsetX=t,e.shadowOffsetY=i,e.shadowBlur=n,e.shadowColor=r},setTextBaseline:function(t){e.textBaseline=t},createCircularGradient:function(){},draw:function(){}})),this.progress=0,this.root={width:t.width,height:t.height,fontSizeRate:1,fixedLine:null},this.size=this.root;var n=0;Object.defineProperty(this,"progress",{configurable:!0,set:function(t){n=t,i.lifecycle("onProgress",t/i.count)},get:function(){return n||0}})}return t.prototype.lifecycle=function(t,e){this.options.listen&&this.options.listen[t]&&this.options.listen[t](e)},t.prototype.setContext=function(t){t&&(this.ctx=t)},t.prototype.init=function(){if((this.canvas.height||r.WEB==R)&&!this.isInit){var t=this.size.height*this.pixelRatio,e=this.size.width*this.pixelRatio;if(this.canvas.height==t&&this.canvas.width==e)return;this.canvas.height=t,this.canvas.width=e,this.ctx.scale(this.pixelRatio,this.pixelRatio)}},t.prototype.clear=function(){this.ctx.clearRect(0,0,this.size.width,this.size.height)},t.prototype.clipPath=function(t,e,i,n,r,o,s){void 0===o&&(o=!1),void 0===s&&(s=!1);var a=this.ctx;if(/polygon/.test(r)){var h=r.match(/-?\d+(rpx|px|%)?\s+-?\d+(rpx|px|%)?/g)||[];a.beginPath(),h.map((function(r){var o=r.split(" "),s=o[0],a=o[1];return[A(s,i)+t,A(a,n)+e]})).forEach((function(t,e){0==e?a.moveTo(t[0],t[1]):a.lineTo(t[0],t[1])})),a.closePath(),s&&a.stroke(),o&&a.fill()}},t.prototype.roundRect=function(t,e,i,n,r,o,s){if(void 0===o&&(o=!1),void 0===s&&(s=!1),!(r<0)){var a=this.ctx;if(a.beginPath(),r){var h=r||{},d=h.borderTopLeftRadius,c=void 0===d?r||0:d,l=h.borderTopRightRadius,f=void 0===l?r||0:l,u=h.borderBottomRightRadius,p=void 0===u?r||0:u,g=h.borderBottomLeftRadius,v=void 0===g?r||0:g;a.arc(t+i-p,e+n-p,p,0,.5*Math.PI),a.lineTo(t+v,e+n),a.arc(t+v,e+n-v,v,.5*Math.PI,Math.PI),a.lineTo(t,e+c),a.arc(t+c,e+c,c,Math.PI,1.5*Math.PI),a.lineTo(t+i-f,e),a.arc(t+i-f,e+f,f,1.5*Math.PI,2*Math.PI),a.lineTo(t+i,e+n-p)}else a.rect(t,e,i,n);a.closePath(),s&&a.stroke(),o&&a.fill()}},t.prototype.setTransform=function(t,e){var i=e.transform,n=e.transformOrigin,r=this.ctx,o=i||{},s=o.scaleX,a=void 0===s?1:s,h=o.scaleY,d=void 0===h?1:h,c=o.translateX,l=void 0===c?0:c,f=o.translateY,u=void 0===f?0:f,p=o.rotate,g=void 0===p?0:p,v=o.skewX,y=void 0===v?0:v,b=o.skewY,x=void 0===b?0:b,m=t.left,w=t.top,S=t.width,z=t.height;l=A(l,S)||0,u=A(u,z)||0;var M={top:A("0%",1),center:A("50%",1),bottom:A("100%",1)},k={left:A("0%",1),center:A("50%",1),right:A("100%",1)};if(n=n.split(" ").filter((function(t,e){return e<2})).reduce((function(t,e){if(/\d+/.test(e)){var i=A(e,1)/(/px|rpx$/.test(e)?T(t.x)?z:S:1);return T(t.x)?Object.assign(t,{y:i}):Object.assign(t,{x:i})}return T(k[e])&&!T(t.x)?Object.assign(t,{x:k[e]}):Object.assign(t,{y:M[e]||.5})}),{}),(l||u)&&r.translate(l,u),(a||d)&&r.scale(a,d),g){var I=m+S*n.x,B=w+z*n.y;r.translate(I,B),r.rotate(g*Math.PI/180),r.translate(-I,-B)}(y||x)&&r.transform(1,Math.tan(x*Math.PI/180),Math.tan(y*Math.PI/180),1,0,0)},t.prototype.setBackground=function(t,e,i,n,o){var s=this.ctx;t&&t!=M?C(t)?j(t,e,i,n,o,s):s.setFillStyle(t):[r.MP_TOUTIAO,r.MP_BAIDU].includes(R)?s.setFillStyle("rgba(0,0,0,0)"):s.setFillStyle(M)},t.prototype.setShadow=function(t){var e=t.boxShadow,i=void 0===e?[]:e,n=this.ctx;if(i.length){var r=i[0],o=i[1],s=i[2],a=i[3];n.setShadow(r,o,s,a)}},t.prototype.setBorder=function(t,e){var i=this.ctx,n=t.width,r=t.height,o=t.left,s=t.top,a=e.border,h=e.borderBottom,d=e.borderTop,c=e.borderRight,l=e.borderLeft,f=e.borderRadius,u=e.lineCap,p=a||{},g=p.borderWidth,v=void 0===g?0:g,y=p.borderStyle,b=p.borderColor,x=h||{},m=x.borderBottomWidth,w=void 0===m?v:m,S=x.borderBottomStyle,z=void 0===S?y:S,M=x.borderBottomColor,k=void 0===M?b:M,I=d||{},B=I.borderTopWidth,W=void 0===B?v:B,P=I.borderTopStyle,L=void 0===P?y:P,O=I.borderTopColor,T=void 0===O?b:O,A=c||{},F=A.borderRightWidth,C=void 0===F?v:F,j=A.borderRightStyle,E=void 0===j?y:j,H=A.borderRightColor,D=void 0===H?b:H,Y=l||{},U=Y.borderLeftWidth,$=void 0===U?v:U,_=Y.borderLeftStyle,X=void 0===_?y:_,N=Y.borderLeftColor,V=void 0===N?b:N,G=f||{},q=G.borderTopLeftRadius,J=void 0===q?f||0:q,Q=G.borderTopRightRadius,Z=void 0===Q?f||0:Q,K=G.borderBottomRightRadius,tt=void 0===K?f||0:K,et=G.borderBottomLeftRadius,it=void 0===et?f||0:et;if(h||l||d||c||a){var nt=function(t,e,n){"dashed"==e?/mp/.test(R)?i.setLineDash([Math.ceil(4*t/3),Math.ceil(4*t/3)]):i.setLineDash([Math.ceil(6*t),Math.ceil(6*t)]):"dotted"==e&&i.setLineDash([t,t]),i.setStrokeStyle(n)},rt=function(t,e,n,r,o,s,a,h,d,c,l,f,p,g,v){i.save(),i.setLineCap(v?"square":u),i.setLineWidth(f),nt(f,p,g),i.beginPath(),i.arc(t,e,a,Math.PI*d,Math.PI*c),i.lineTo(n,r),i.arc(o,s,h,Math.PI*c,Math.PI*l),i.stroke(),i.restore()};if(i.save(),a&&!h&&!l&&!d&&!c)return i.setLineWidth(v),nt(v,y,b),this.roundRect(o,s,n,r,f,!1,!!b),void i.restore();w&&rt(o+n-tt,s+r-tt,o+it,s+r,o+it,s+r-it,tt,it,.25,.5,.75,w,z,k,$&&C),$&&rt(o+it,s+r-it,o,s+J,o+J,s+J,it,J,.75,1,1.25,$,X,V,W&&w),W&&rt(o+J,s+J,o+n-Z,s,o+n-Z,s+Z,J,Z,1.25,1.5,1.75,W,L,T,$&&C),C&&rt(o+n-Z,s+Z,o+n,s+r-tt,o+n-tt,s+r-tt,Z,tt,1.75,2,.25,C,E,D,W&&w)}},t.prototype.setOpacity=function(t){var e=t.opacity,i=void 0===e?1:e;this.ctx.setGlobalAlpha(i)},t.prototype.drawPattern=function(t,e,r){return i(this,void 0,void 0,(function(){var i=this;return n(this,(function(n){return[2,new Promise((function(n,o){i.drawView(e,r,!0,!1,!0);var s=i,a=s.ctx;s.canvas;var h=e.width,d=e.height,c=e.left,l=e.top,f=r||{},u=f.borderRadius,p=void 0===u?0:u,g=f.backgroundImage,v=f.backgroundRepeat,y=void 0===v?"repeat":v;g&&function(t){var o=a.createPattern(t.src,y);a.setFillStyle(o),i.roundRect(c,l,h,d,p,!0,!1),i.setBorder(e,r),n()}(t)}))]}))}))},t.prototype.drawView=function(t,e,i,n,r){void 0===i&&(i=!0),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=this.ctx,s=t.width,a=t.height,h=t.left,d=t.top,c=e||{},l=c.borderRadius,f=void 0===l?0:l,u=c.backgroundColor,p=void 0===u?M:u,g=c.overflow;e.opacity&&this.setOpacity(e),this.setTransform(t,e),r&&(o.save(),this.setShadow(e)),i&&this.setBackground(p,s,a,h,d),e.clipPath?this.clipPath(h,d,s,a,e.clipPath,i,!1):this.roundRect(h,d,s,a,f,i,!1),r&&o.restore(),n&&this.setBorder(t,e),"hidden"==g&&o.clip()},t.prototype.drawImage=function(t,e,o,s){return void 0===e&&(e={}),void 0===o&&(o={}),void 0===s&&(s=!0),i(this,void 0,void 0,(function(){var a=this;return n(this,(function(h){switch(h.label){case 0:return[4,new Promise((function(h,d){return i(a,void 0,void 0,(function(){var i,a,d,c,l,f,u,p,g,v,y,b,x,m,w,S,z,I,B,W,P,L=this;return n(this,(function(n){return i=this.ctx,a=o.borderRadius,d=void 0===a?0:a,c=o.backgroundColor,l=void 0===c?M:c,f=o.objectFit,u=void 0===f?k:f,p=o.backgroundSize,g=void 0===p?k:p,v=o.objectPosition,y=o.backgroundPosition,b=o.boxShadow,o.backgroundImage&&(u=g,v=y),b&&this.drawView(e,Object.assign(o,{backgroundColor:l||b&&(l||"#ffffff")}),!0,!1,!0),x=e.width,m=e.height,w=e.left,S=e.top,i.save(),z=e.contentSize.left-e.borderSize.left,I=e.contentSize.top-e.borderSize.top,s||(this.setOpacity(o),this.setTransform(e,o),this.setBackground(l,x,m,w,S),this.roundRect(w,S,x,m,d,!!(d||!b&&l),!1)),w+=z,S+=I,i.clip(),B=function(t){if(u!==k){var n=function(t,e,i){var n=t.objectFit,r=t.objectPosition,o=e.width/e.height,s=i.width/i.height,a=1;"contain"==n&&o>=s||"cover"==n&&o<s?a=e.height/i.height:("contain"==n&&o<s||"cover"==n&&o>=s)&&(a=e.width/i.width);var h=i.width*a,d=i.height*a,c=r||[],l=c[0],f=c[1],u=/^\d+px|rpx$/.test(l)?A(l,e.width):(e.width-h)*(F(l)?A(l,1):{left:0,center:.5,right:1}[l||"center"]),p=/^\d+px|rpx$/.test(f)?A(f,e.height):(e.height-d)*(F(f)?A(f,1):{top:0,center:.5,bottom:1}[f||"center"]),g=function(t,e){return[(t-u)/a,(e-p)/a]},v=g(0,0),y=v[0],b=v[1],x=g(e.width,e.height),m=x[0],w=x[1];return{sx:Math.max(y,0),sy:Math.max(b,0),sw:Math.min(m-y,i.width),sh:Math.min(w-b,i.height),dx:Math.max(u,0),dy:Math.max(p,0),dw:Math.min(h,e.width),dh:Math.min(d,e.height)}}({objectFit:u,objectPosition:v},e.contentSize,t),o=n.sx,s=n.sy,a=n.sh,h=n.sw,d=n.dx,c=n.dy,l=n.dh,f=n.dw;R==r.MP_BAIDU?i.drawImage(t.src,d+w,c+S,f,l,o,s,h,a):i.drawImage(t.src,o,s,h,a,d+w,c+S,f,l)}else i.drawImage(t.src,w,S,x,m)},W=function(){i.restore(),L.drawView(e,o,!1,!0,!1),h(1)},P=function(t){B(t),W()},P(t),[2]}))}))}))];case 1:return h.sent(),[2]}}))}))},t.prototype.drawText=function(t,e,i,n){var r=this.ctx,o=e.borderSize,s=e.contentSize,a=e.left,h=e.top,d=s.width,c=s.height,l=s.left-o.left,f=s.top-o.top,u=i.color,p=void 0===u?"#000000":u,g=i.lineHeight,v=void 0===g?"1.4em":g,y=i.fontSize,b=void 0===y?14:y,x=i.fontWeight,m=i.fontFamily,w=i.fontStyle,S=i.textAlign,z=void 0===S?"left":S,M=i.verticalAlign,k=void 0===M?yt:M,I=i.backgroundColor,B=i.lineClamp,W=i.backgroundClip,P=i.textShadow,R=i.textDecoration;if(this.drawView(e,i,W!=ut),v=A(v,b),t){r.save(),this.setShadow({boxShadow:P}),a+=l,h+=f;var L=n.fontHeight,O=n.descent+n.ascent;switch(r.setFonts({fontFamily:m,fontSize:b,fontWeight:x,fontStyle:w}),r.setTextBaseline(yt),r.setTextAlign(z),W?this.setBackground(I,d,c,a,h):r.setFillStyle(p),z){case xt:break;case mt:a+=.5*d;break;case wt:a+=d}var T=n.lines*v,F=Math.ceil((c-T)/2);switch(F<0&&(F=0),k){case vt:break;case yt:h+=F;break;case bt:h+=2*F}var C=(v-L)/2,j=v/2,E=function(t){var e=r.measureText(t),i=e.actualBoundingBoxDescent,n=void 0===i?0:i,o=e.actualBoundingBoxAscent;return k==vt?{fix:O?void 0===o?0:o:j-C/2,lineY:O?0:C-C/2}:k==yt?{fix:O?j+n/4:j,lineY:O?0:C}:k==bt?{fix:O?v-n:j+C/2,lineY:O?2*C:C+C/2}:{fix:0,height:0,lineY:0}},H=function(t,e,i){var o=t;switch(z){case xt:o+=i;break;case mt:o=(t-=i/2)+i;break;case wt:o=t,t-=i}if(R){r.setLineWidth(b/13),r.beginPath();var s=.1*n.fontHeight;/\bunderline\b/.test(R)&&(r.moveTo(t,e+n.fontHeight+s),r.lineTo(o,e+n.fontHeight+s)),/\boverline\b/.test(R)&&(r.moveTo(t,e-s),r.lineTo(o,e-s)),/\bline-through\b/.test(R)&&(r.moveTo(t,e+.5*n.fontHeight),r.lineTo(o,e+.5*n.fontHeight)),r.closePath(),r.setStrokeStyle(p),r.stroke()}};if(!n.widths||1==n.widths.length&&n.widths[0].total<=s.width){var D=E(t),Y=D.fix,U=D.lineY;return r.fillText(t,a,h+Y),H(a,h+U,n&&n.widths&&n.widths[0].total||n.text),h+=v,r.restore(),void this.setBorder(e,i)}for(var $=t.split(""),_=h,X=a,N="",V=0,G=0;G<=$.length;G++){var q=$[G]||"",J="\n"===q,Q=""==q,Z=N+(q=J?"":q),K=r.measureText(Z).width;if(V>=B)break;if(X=a,K>s.width||J||Q){if(V++,N=Q&&K<=s.width?Z:N,V===B&&K>d){for(;r.measureText("".concat(N,"...")).width>s.width&&!(N.length<=1);)N=N.substring(0,N.length-1);N+="..."}var tt=E(N);Y=tt.fix,U=tt.lineY;if(r.fillText(N,X,h+Y),H(X,h+U,K),N=q,(h+=v)>_+c)break}else N=Z}r.restore()}},t.prototype.source=function(t){return i(this,void 0,void 0,(function(){var e,i,r,o,s=this;return n(this,(function(n){switch(n.label){case 0:if(this.node=null,e=+new Date,"{}"==JSON.stringify(t))return[2];if(!t.type)for(i in t.type=gt,t.css=t.css||{},t)["views","children","type","css"].includes(i)||(t.css[i]=t[i],delete t[i]);return t.css.boxSizing||(t.css.boxSizing="border-box"),[4,this.create(t)];case 1:return(r=n.sent())?((o=r.layout()||{}).width!==this.size.width||o.height!==this.size.height?this.isInit=!1:this.isInit=!0,this.size=o,this.node=r,this.onEffectFinished().then((function(t){return s.lifecycle("onEffectSuccess",t)})).catch((function(t){return s.lifecycle("onEffectFail",t)})),console.log("布局用时："+(+new Date-e)+"ms"),[2,this.size]):[2,console.warn("no node")]}}))}))},t.prototype.getImageInfo=function(t){return this.imageBus[t]||(this.imageBus[t]=this.createImage(t,this.useCORS)),this.imageBus[t]},t.prototype.create=function(t,r){return i(this,void 0,void 0,(function(){var i,o,s,a,h,d,c,l,f,u,p,g,v,y,b,x,w;return n(this,(function(n){switch(n.label){case 0:if(i=t.type==ft,o=[ut,pt].includes(t.type),s=t.css||{},a=s.backgroundImage,h=s.display,i&&!t.src&&!t.url||o&&!t.text)return[2];if(h==m)return[2];if(o&&(t.text=String(t.text)),!(i||t.type==gt&&a))return[3,4];d=i?t.src:"",c=/url\((.+)\)/.exec(a),a&&c&&c[1]&&(d=c[1]||""),n.label=1;case 1:return n.trys.push([1,3,,4]),[4,this.getImageInfo(d)];case 2:return l=n.sent(),f=l.width,u=l.height,!(p=l.path)&&i?[2]:(p&&(t.attributes=Object.assign(t.attributes||{},{width:f,height:u,path:p,src:p,naturalSrc:d})),[3,4]);case 3:return g=n.sent(),t.type!=gt?[2]:(this.lifecycle("onEffectFail",e(e({},g),{src:d})),[3,4]);case 4:if(this.count+=1,v=new ct(t,r,this.root,this.ctx),!(y=t.views||t.children))return[3,8];b=0,n.label=5;case 5:return b<y.length?(x=y[b],[4,this.create(x,v)]):[3,8];case 6:(w=n.sent())&&v.add(w),n.label=7;case 7:return b++,[3,5];case 8:return[2,v]}}))}))},t.prototype.drawNode=function(t,e){return void 0===e&&(e=!1),i(this,void 0,void 0,(function(){var i,r,o,s,a,h,d,c,l,f,u,p,g,v,y,b,x,m,w,S,z,M,k;return n(this,(function(n){switch(n.label){case 0:return i=t.layoutBox,r=t.computedStyle,o=t.attributes,s=t.name,a=t.children,h=t.fixedLine,d=t.attributes,c=d.src,l=d.text,f=r.position,u=r.backgroundImage,p=r.backgroundRepeat,["fixed"].includes(f)&&!e?[2]:(this.ctx.save(),s!==gt?[3,7]:c&&u?p?[4,this.drawPattern(o,i,r)]:[3,2]:[3,5]);case 1:return n.sent(),[3,4];case 2:return[4,this.drawImage(o,i,r,!1)];case 3:n.sent(),n.label=4;case 4:return[3,6];case 5:this.drawView(i,r),n.label=6;case 6:return[3,10];case 7:return s===ft&&c?[4,this.drawImage(o,i,r,!1)]:[3,9];case 8:return n.sent(),[3,10];case 9:s===ut?this.drawText(l,i,r,o):s===pt&&lt.api&&lt.api.draw(l,this,i,r),n.label=10;case 10:if(this.progress+=1,v=(g=h||{}).beforeElements,y=g.afterElements,!v)return[3,14];b=0,x=v,n.label=11;case 11:return b<x.length?(k=x[b],[4,this.drawNode(k)]):[3,14];case 12:n.sent(),n.label=13;case 13:return b++,[3,11];case 14:if(!a)return[3,18];m=Object.values?Object.values(a):Object.keys(a).map((function(t){return a[t]})),w=0,S=m,n.label=15;case 15:return w<S.length?"absolute"===(k=S[w]).computedStyle.position?[3,17]:[4,this.drawNode(k)]:[3,18];case 16:n.sent(),n.label=17;case 17:return w++,[3,15];case 18:if(!y)return[3,22];z=0,M=y,n.label=19;case 19:return z<M.length?(k=M[z],[4,this.drawNode(k)]):[3,22];case 20:n.sent(),n.label=21;case 21:return z++,[3,19];case 22:return this.ctx.restore(),[2]}}))}))},t.prototype.render=function(t){var e=this;return void 0===t&&(t=30),new Promise((function(r,o){return i(e,void 0,void 0,(function(){var e,i,s,a,h,d,c,l,f,u;return n(this,(function(n){switch(n.label){case 0:return e=+new Date,this.init(),[4,(p=t,void 0===p&&(p=0),new Promise((function(t){return setTimeout(t,p)})))];case 1:n.sent(),n.label=2;case 2:if(n.trys.push([2,14,,15]),!this.node)return[3,12];if(i=this.root.fixedLine||{},s=i.beforeElements,a=i.afterElements,!s)return[3,6];h=0,d=s,n.label=3;case 3:return h<d.length?(f=d[h],[4,this.drawNode(f,!0)]):[3,6];case 4:n.sent(),n.label=5;case 5:return h++,[3,3];case 6:return[4,this.drawNode(this.node)];case 7:if(n.sent(),!a)return[3,11];c=0,l=a,n.label=8;case 8:return c<l.length?(f=l[c],[4,this.drawNode(f,!0)]):[3,11];case 9:n.sent(),n.label=10;case 10:return c++,[3,8];case 11:return r(this.node),[3,13];case 12:this.lifecycle("onEffectFail","node is empty"),n.label=13;case 13:return[3,15];case 14:return u=n.sent(),this.lifecycle("onEffectFail",u),o(u),[3,15];case 15:return console.log("渲染用时："+(+new Date-e-30)+"ms"),[2]}var p}))}))}))},t.prototype.onEffectFinished=function(){var t=this,e=Object.keys(this.imageBus).map((function(e){return t.imageBus[e]}));return Promise.all(e)},t.prototype.destroy=function(){this.node=[]},t.prototype.save=function(t){try{var e=t||{},i=e.fileType,n=void 0===i?"png":i,r=e.quality,o=void 0===r?1:r;return this.canvas.toDataURL("image/".concat(n),o)}catch(t){return this.lifecycle("onEffectFail","image cross domain"),t}},t}();r.WEB==R&&(window.Painter=St),t.Painter=St,t.default=St,Object.defineProperty(t,"__esModule",{value:!0})}));
