<template>
	<view class="store-credentials">
		<!-- 店内照 -->
		<view class="credential-card" v-if="storeInfo.accordingStore">
			<image class="card-image" :src="imgUrl + storeInfo.accordingStore" mode="widthFix" @load="imageLoaded" @error="imageError"></image>
		</view>

		<!-- 门面照 -->
		<view class="credential-card" v-if="storeInfo.storePicture">
			<image class="card-image" :src="imgUrl + storeInfo.storePicture" mode="widthFix" @load="imageLoaded" @error="imageError"></image>
		</view>

		<!-- 营业执照 -->
		<view class="credential-card" v-if="storeInfo.licenseForEnterprise">
			<image class="card-image" :src="imgUrl + storeInfo.licenseForEnterprise" mode="widthFix" @load="imageLoaded" @error="imageError"></image>
		</view>

		<!-- 无照片提示 -->
		<view class="empty-state" v-if="!hasCredentials">
			<text>暂无店铺证件信息</text>
		</view>
	</view>
</template>

<script setup>
	import { ref, computed } from 'vue';
	import { onLoad } from '@dcloudio/uni-app';

	const imgUrl = uni.env.IMAGE_URL;
	const storeInfo = ref({});

	// 计算是否有任何证件信息
	const hasCredentials = computed(() => {
		return !!(storeInfo.value.accordingStore || storeInfo.value.storePicture || storeInfo.value.licenseForEnterprise);
	});

	// 图片加载成功处理
	const imageLoaded = () => {
		console.log('图片加载成功');
	};

	// 图片加载失败处理
	const imageError = () => {
		console.error('图片加载失败');
		uni.showToast({
			title: '图片加载失败',
			icon: 'none',
			duration: 2000
		});
	};

	onLoad((options) => {
		// 设置页面标题
		uni.setNavigationBarTitle({
			title: '店铺证件信息'
		});
		// 接收传递过来的店铺信息
		console.log('接收到的参数:', options);
		if (options.storeInfo) {
			try {
				const decodedInfo = decodeURIComponent(options.storeInfo);
				console.log('解码后的信息:', decodedInfo);
				storeInfo.value = JSON.parse(decodedInfo);
				console.log('解析后的店铺信息:', storeInfo.value);
			} catch (e) {
				console.error('解析店铺信息失败', e);
				uni.showToast({
					title: '获取店铺信息失败',
					icon: 'none'
				});
				setTimeout(() => {
					uni.navigateBack();
				}, 1500);
			}
		} else {
			console.log('未接收到店铺信息参数');
			uni.showToast({
				title: '未获取到店铺信息',
				icon: 'none'
			});
			setTimeout(() => {
				uni.navigateBack();
			}, 1500);
		}
	});
</script>

<style lang="scss">
	.store-credentials {
		padding: 30rpx;
		background-color: #F5F5F5;
		min-height: 100vh;

		.credential-card {
			margin-bottom: 30rpx;
			background-color: #FFFFFF;
			border-radius: 16rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

			.card-image {
				width: 100%;
				display: block;
				background-color: #f8f8f8; /* 图片加载前的背景色 */
			}
		}

		.empty-state {
			display: flex;
			align-items: center;
			justify-content: center;
			height: 300rpx;
			background-color: #FFFFFF;
			border-radius: 16rpx;
			margin-top: 100rpx;

			text {
				color: #999;
				font-size: 28rpx;
			}
		}
	}
</style>
