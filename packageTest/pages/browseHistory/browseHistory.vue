<template>
	<view class="browseHistory">
		<view class="browseHistory-top">
			<view @click="cancel">
				清除所有记录
			</view>
		</view>
		<view class="browseHistory-cnt">
			<view v-for="(item,index) in resultList" :key="index">
				<productDetail :info='item' width="338rpx" height="460rpx" :showGoCart='false' sizeType='normal'>
				</productDetail>
			</view>
		</view>
		<empty v-if="!resultList?.length"></empty>
	</view>
</template>

<script setup>
	import productDetail from '../../components/productDetail/productDetail.vue';
	import {
		listGet
	} from '@/hooks'
	import {
		computed
	} from 'vue';

	const {
		mode,
		list,
		total,
		refresh
	} = listGet({
		apiUrl: uni.api.findBrowsingHistorys,
		pdIsLogin: true
	})

	const resultList = computed(() => {
		let l = []
		if (list.value?.length) {
			for (let item of list.value) {
				l = [...l, ...item?.goodsMap || []]
			}
		}
		return l
	})
	//确认删除记录
	async function cancel() {
		if (!resultList.value?.length) {
			uni.showToast({
				title: '暂无浏览记录',
				icon: 'none'
			})
			return;
		}
		uni.showLoading({
			mask: true
		})
		await uni.http.post(uni.api.delMemberBrowsingHistoryByIds, {
			ids: resultList.value.map(i => i.id).join(',')
		})
		uni.showToast({
			title: '操作成功~',
			icon: "none"
		})
		setTimeout(() => {
			refresh()
		}, 500)
	}
</script>

<style lang="scss">
	page {
		background-color: #F5F5F5;
	}

	.browseHistory {
		&-top {
			height: 100rpx;
			background-color: white;
			color: #333333;
			font-size: 32rpx;
			display: flex;
			align-items: center;
			padding-left: 30rpx;
			position: sticky;
			top: 0;
			z-index: 10;
		}

		&-cnt {
			display: grid;
			/* 三列，均分容器宽度 */
			grid-template-columns: repeat(2, 1fr);
			gap: 24rpx;
			padding: 24rpx;

			>view {
				background-color: white;
			}
		}
	}
</style>
