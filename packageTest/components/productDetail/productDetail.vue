<template>
	<view class="productDetail" @click="navToGoodDetail(info)">
		<image class="productDetail-img" :src="imgUrl + parseImgurl(info.mainPicture)?.[0]" mode=""></image>
		<view class="productDetail-info">
			<view class="productDetail-info-name">
				{{info.goodName}}
			</view>
			<view class="productDetail-info-other">
				<view class="productDetail-info-other-dz">
					<image src="@/static/index/i_1.png" mode=""></image>
					<view>
						{{info.price || info.smallPrice}}
					</view>
				</view>
				<view class="productDetail-info-other-ys">
					已售 {{info.salesVolume}}
					<image v-if="showGoCart" class="productDetail-info-other-ys-goCart" src="@/static/index/btn1.png"
						mode=""></image>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup lang="ts">
	import { computed, toRefs } from 'vue';
	import {
		navTo, navToGoodDetail
	} from '@/hooks';
	import {
		parseImgurl
	} from '@/utils'
	const imgUrl = uni.env.IMAGE_URL;
	const props = defineProps({
		width: {
			type: String,
			default: '200rpx'
		},
		height: {
			type: String,
			default: '285rpx'
		},
		showGoCart: {
			type: Boolean,
			default: false
		},
		//default   normal
		sizeType: {
			type: String,
			default: 'default'
		},
		info: {
			type: Object,
			default: () => ({})
		}
	})
	const { width, height, sizeType } = toRefs(props)
	const nameFontSize = computed(() => {
		if (sizeType.value === 'default') {
			return '24rpx'
		}
		return '26rpx'
	})
	const dzFontSize = computed(() => {
		if (sizeType.value === 'default') {
			return '22rpx'
		}
		return '26rpx'
	})
	const dzLvImgSize = computed(() => {
		if (sizeType.value === 'default') {
			return '20rpx'
		}
		return '40rpx'
	})
</script>

<style lang="scss" scoped>
	.productDetail {
		width: v-bind(width);
		height: v-bind(height);

		&-img {
			width: 100%;
			height: v-bind(width);
			border: 2rpx solid #f5f5f5;
			border-radius: 16rpx;
		}

		&-info {
			padding: 10rpx;
			color: #999999;

			&-name {
				font-size: v-bind(nameFontSize);
				margin-bottom: 10rpx;
				width: 100%;
				/* 设置容器的宽度 */
				white-space: nowrap;
				/* 防止文本换行 */
				overflow: hidden;
				/* 隐藏超出部分 */
				text-overflow: ellipsis;
				/* 显示省略号 */
			}

			&-other {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-dz {
					display: flex;
					align-items: center;
					color: #E6A600;
					font-size: v-bind(dzFontSize);

					image {
						width: v-bind(dzLvImgSize);
						height: v-bind(dzLvImgSize);
						margin-right: 6rpx;
					}
				}

				&-ys {
					font-size: v-bind(dzFontSize);
					color: #999999;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					&-goCart {
						width: 30rpx;
						height: 30rpx;
						margin-left: 20rpx;
					}
				}
			}
		}
	}
</style>
