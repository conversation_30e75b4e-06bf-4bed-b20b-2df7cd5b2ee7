<script>
	import {
		frontSetting,
		getAllBeShared
	} from '@/store';
	import {
		getUserInfos
	} from '@/hooks';

	export default {
		// 全局存储计时相关数据
		globalData: {
			backgroundStart: null, // 当前后台开始时间戳
			totalBackground: 0 // 累计后台时长（秒）
		},
		onLaunch: async function() {
			getUserInfos()
			//程序设置相关
			let {
				data
			} = await uni.http.get(uni.api.findMarketingDistributionSetting);
			frontSetting().setSetting(data.result)
		},
		onShow: function() {
			console.log('App Show')
			// 应用进入前台
			if (this.globalData.backgroundStart) {
				// 计算本次后台运行时长并累加
				const bgDuration = Math.floor((Date.now() - this.globalData.backgroundStart) / 1000);
				this.globalData.totalBackground += bgDuration;
				this.globalData.backgroundStart = null;
			}
			//如果进入后台时间过长，则清空分享相关信息
			if (this.globalData.totalBackground > uni.env.CLEAR_SHARE_CACHE_TIME * 60) {
				getAllBeShared().setInfo({});
				getAllBeShared().setShareDiscountRatio("");
			}

		},
		onHide: function() {
			// 记录本次后台开始时间
			this.globalData.backgroundStart = Date.now();
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	view,
	button,
	text {
		box-sizing: border-box;
	}

	image {
		display: block;
		will-change: transform;
	}

	button::after {
		border: none;
	}

	button {
		background-color: transparent;
		padding: 0;
	}
</style>