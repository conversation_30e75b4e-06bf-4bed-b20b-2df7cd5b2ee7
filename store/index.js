import {
	defineStore
} from 'pinia'
import {
	parseImgurl
} from '../utils';
//用户信息相关store
export const userStore = defineStore('userStore', {
	state: () => {
		return {
			userInfo: {},
			token: uni.getStorageSync('token') || ''
		}
	},
	getters: {},
	actions: {
		setUserInfo(userInfo) {
			this.userInfo = userInfo || {}
		},
		setToken(token) {
			this.token = token;
			uni.setStorageSync('token', token);
		},
		clearUserInfo() {
			this.userInfo = {}
		},
		clearToken() {
			this.token = ''
			uni.removeStorageSync('token')
		},
		logOut() {
			this.clearUserInfo();
			this.clearToken();
			recordIndexPopUp().setPop(false)
		}
	}
})
//程序设置相关
export const frontSetting = defineStore('frontSetting', {
	state: () => {
		return {
			setting: {}
		}
	},
	getters: {},
	actions: {
		setSetting(setting) {
			if (setting.frontLogo) setting.frontLogo = parseImgurl(setting.frontLogo)[0];
			this.setting = setting ? {
				...this.setting,
				...setting
			} : {}
		},
	}
})
//倒计时相关
export const countDownStore = defineStore('countDownStore', {
	state: () => {
		return {
			//获取手机验证码定时器
			timer: '',
			//手机倒计时时间
			timeCount: 60,
		}
	},
	getters: {},
	actions: {
		//创建验证码定时器
		createTimer() {
			let state = this;
			if (state.timeCount == 60) {
				state.timeCount--
				state.timer = setInterval(() => {
					state.timeCount--
					if (state.timeCount <= 0) {
						state.timeCount = 60
						clearInterval(state.timer)
					}
				}, 1000)
			}
		}
	}
})
//地理位置相关
export const locationInfo = defineStore('locationInfo', {
	state: () => {
		return {
			info: {
				//默认北京的经纬度
				longitude: uni.getStorageSync('locationInfo')?.longitude || '116.46', //经度
				latitude: uni.getStorageSync('locationInfo')?.latitude || '39.92', //维度
			},
			city: uni.getStorageSync('locationCity') || '北京' //城市
		}
	},
	getters: {},
	actions: {
		setInfo(info) {
			this.info = info || {}
			uni.setStorageSync('locationInfo', info)
			//经纬度改变后改变城市名称
		},
		setCity(city) {
			this.city = city
			uni.setStorageSync('locationCity', city)
		}
	}
})
//接收并各类分享中的推广人信息和店铺id等
export const getAllBeShared = defineStore('getAllBeShared', {
	state: () => {
		return {
			info: {},
			shareDiscountRatio: ""
		}
	},
	actions: {
		setInfo(info) {
			this.info = info || {}
		},
		setShareDiscountRatio(ratio) {
			this.shareDiscountRatio = ratio
		}
	}
})
//对销售渠道店铺信息的记录
export const setSaleChannelStoreInfo = defineStore('setSaleChannelStoreInfo', {
	state: () => {
		return {
			//接口得到的sysUserId修改
			sysUserId: '',
			//进入店铺得到的sysUserId修改
			intoStoreForSysUserId: ''
		}
	},
	actions: {
		setSysUserId(sysUserId) {
			this.sysUserId = sysUserId || ''
		},
		setIntoStoreForSysUserId(sysUserId) {
			this.intoStoreForSysUserId = sysUserId || ''
		}
	}
})

//对页面全路径的记录(当未登录状态下跳转到需要登录的页面后,记录原本要跳转到的页面全路径后,在跳转登录页);
export const recordFullPagePath = defineStore('recordFullPagePath', {
	state: () => {
		return {
			pageFullPath: '',
		}
	},
	actions: {
		setPageFullPath(pageFullPath) {
			this.pageFullPath = pageFullPath || ''
		}
	}
})

//记录首页的弹窗是否已经完成了弹窗的逻辑
export const recordIndexPopUp = defineStore('recordIndexPopUp', {
	state: () => {
		return {
			isPop: false
		}
	},
	actions: {
		setPop(bol) {
			this.isPop = bol
		}
	}
})