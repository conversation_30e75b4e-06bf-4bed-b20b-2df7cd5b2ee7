<template>
	<view class="orderView" v-if="info.consignee">
		<view class="orderView-top">
			<!-- 订单状态显示 -->
			<!-- 对于自提核销订单，显示核销状态文案；对于其他订单，显示普通订单状态 -->
			<view class="orderView-top-status" v-if="!info.pickUpQrCode || !isPickUpOrder">
				{{ options.name || '' }}
			</view>
			<view class="orderView-top-status" v-else>
				{{pickUpVerificationStatusText}}
			</view>
			<view class="orderView-top-spec">
				<template v-if="options.state === '1' || options.state === '7'">
					{{ options.name || '' }}，欢迎再次光临！~
				</template>
				<template v-if="options.state === '3' && countDonwOwnTimer * 1 > 0">
					<view class="timeCountDown">
						请您在<uni-countdown :day="countDownTimer.d" :hour="countDownTimer.h" :minute="countDownTimer.m"
							:second="countDownTimer.s" color='#676767'
							splitorColor='#676767'></uni-countdown>内完成支付，逾期订单将自动取消。
					</view>
				</template>
				<template v-if="options.state === '6' && countDonwOwnTimer * 1 > 0">
					<view class="timeCountDown">
						还剩
						<uni-countdown :day="1"></uni-countdown>
						自动确认收货
					</view>
				</template>
				<template v-if="options.state === '5'">
					马上就要发货了，请您耐心等待，谢谢！
				</template>
				<template v-if="options.state === '4'">
					请您成团
				</template>
				<template v-if="options.state === '2' && options.reason === '1'">
					超时关闭
				</template>
				<template v-if="options.state === '2' && options.reason === '2'">
					我不想买了
				</template>
			</view>
			<!-- 隐藏标题下的操作按钮 -->
			<view class="orderView-top-button" style="display: none;">
				<template v-if="!info.pickUpQrCode">
					<view class="bottomFixBtn" v-if="options.state === '3'" @click="toggleCancelOrderPop">
						取消订单
					</view>
					<!-- 	<view class="bottomFixBtn" v-if="options.state === '3'" @click="toPay">
						去付款
					</view> -->
					<view class="bottomFixBtn" v-if="options.state === '6' || options.state === '7'" @click="toLogistics">
						查看物流信息
					</view>
					<view class="bottomFixBtn" v-if="options.state === '6'" @click="takeGoods">
						确认收货
					</view>
				</template>
			</view>

			<scroll-view scroll-x="true" class="orderView-top-scrollView" v-if="info.orderStoreSubLists?.length">
				<view class="orderView-top-scrollView">
					<view class="orderView-top-scrollView-ct">
						<template v-if="info.orderStoreSubLists?.length">
							<view class="orderView-top-scrollView-ct-view"
								v-for="(item,index) in info.orderStoreSubLists" :key="index"
								@click="orderStoreSubListsSelectIndex = index"
								:class="{orderStoreSubListsSelectedClass:orderStoreSubListsSelectIndex === index}">
								<image class="orderView-top-scrollView-ct-view-img"
									:src="imgUrl + parseImgurl(item?.orderStoreGoodRecords[0]?.mainPicture)?.[0]"
									mode="">
								</image>
								<view class="orderView-top-scrollView-ct-view-right">
									<view>
										包裹{{index + 1}}
									</view>
									<view style="color: #999999;">
										共{{item?.orderStoreGoodRecords.length || 0}}件
									</view>
								</view>
							</view>
						</template>


						<template v-if="info.listOrderStoreSubList?.length && info.listOrderStoreSubList[0]?.orderStoreGoodRecords?.length">
							<view class="orderView-top-scrollView-ct-view" @click="orderStoreSubListsSelectIndex = -1"
								:class="{orderStoreSubListsSelectedClass:orderStoreSubListsSelectIndex === -1}">
								<image class="orderView-top-scrollView-ct-view-img"
									:src="info.listOrderStoreSubList[0]?.orderStoreGoodRecords?.length ? (imgUrl + (parseImgurl(info.listOrderStoreSubList[0].orderStoreGoodRecords[0]?.mainPicture)?.length ? parseImgurl(info.listOrderStoreSubList[0].orderStoreGoodRecords[0].mainPicture)[0] : '')) : ''"
									mode="">
								</image>
								<view class="orderView-top-scrollView-ct-view-right">
									<view>
										{{ isPickUpOrderComputed ? pickUpGoodsTitle : '未发货商品' }}
									</view>
									<view style="color: #999999;">
										共{{info.listOrderStoreSubList[0]?.orderStoreGoodRecords?.length || 0}}件
									</view>
								</view>
							</view>
						</template>

					</view>
				</view>
			</scroll-view>


			<!-- 核销状态信息区域 -->
			<view class="orderView-top-hx" v-if="info.pickUpQrCode && isPickUpOrder">
				<!-- 显示核销状态描述文字 -->
				<view class="orderView-top-hx-desc" v-if="info.pickUpVerificationStatus === '0'">
					出示二维码进行核销
				</view>
				<view class="orderView-top-hx-desc" v-else>
					您的订单{{pickUpVerificationStatusText}}
				</view>

				<!-- 仅在订单状态为"待核销"(未核销的自提订单)或"待收货"(状态码为"2")时显示核销二维码 -->
				<template v-if="showVerificationQrCode">
					<image class="orderView-top-hx-img" :src="imgUrl + info.pickUpQrAddr" mode=""></image>
					<!-- 凭证信息已隐藏 -->
				</template>
			</view>


			<image class="orderView-top-custom" src="@/static/custom.png" mode="" v-if="false"></image>

		</view>
		<view class="orderView-goods">
			<template v-if="info.orderStoreSubLists?.length || (info.listOrderStoreSubList?.length && info.listOrderStoreSubList[0]?.orderStoreGoodRecords?.length)">
				<view class="orderView-goods-specTop" v-if="orderStoreSubListsSelectIndex !== -1">
					<view class="orderView-goods-specTop-name">
						包裹{{orderStoreSubListsSelectIndex + 1}}
					</view>
					<!-- 对于自提核销订单，不显示查看物流信息按钮 -->
					<view class="orderView-goods-specTop-btn" v-if="!isPickUpOrderComputed"
						@click="toLogistics(info.orderStoreSubLists[orderStoreSubListsSelectIndex].id)">
						查看物流信息
					</view>
				</view>
				<view class="orderView-goods-specTop" v-else-if="info.listOrderStoreSubList?.length && info.listOrderStoreSubList[0]?.orderStoreGoodRecords?.length">
					<view>
						{{ isPickUpOrderComputed ? pickUpPackageTitle : '未发货包裹' }}
					</view>
					<view>

					</view>
				</view>
				<view class="orderView-goods-good"
					v-for="(itm,idx) in (orderStoreSubListsSelectIndex !== -1 ? (info.orderStoreSubLists[orderStoreSubListsSelectIndex]?.orderStoreGoodRecords || []) : (info.listOrderStoreSubList[0]?.orderStoreGoodRecords || []))"
					:key="itm.id">
					<image v-if="itm.mainPicture" class="orderView-goods-good-left"
						:src="imgUrl + parseImgurl(itm.mainPicture)?.[0]" mode="">
					</image>
					<image v-else-if="itm.goodMainPicture" class="orderView-goods-good-left"
						:src="imgUrl + parseImgurl(itm.goodMainPicture)?.[0]" mode=""> </image>
					<view class="orderView-goods-good-right">
						<view class="orderView-goods-good-right-name">
							{{itm.goodName}}
						</view>
						<view class="orderView-goods-good-right-spec">
							规格:{{itm.specification || itm.goodSpecification}}
						</view>
						<view class="orderView-goods-good-right-other">
							数量:{{itm.amount}}
							<view class="orderView-goods-good-right-other-right">
								<image src="@/static/index/i_1.png" mode=""></image>
								{{itm.price}}
							</view>
						</view>
						<!-- <view class="orderView-goods-good-right-goodStatus" v-if="itm.status != 0">
							{{getGoodStatusDetail(itm).name}}
						</view> -->
					</view>
				</view>
			</template>
			<template v-else>
				<view class="orderView-goods-good" v-for="(itm,idx) in info.goods" :key="itm.id">
					<image v-if="itm.mainPicture" class="orderView-goods-good-left"
						:src="imgUrl + parseImgurl(itm.mainPicture)?.[0]" mode="">
					</image>
					<image v-else-if="itm.goodMainPicture" class="orderView-goods-good-left"
						:src="imgUrl + parseImgurl(itm.goodMainPicture)?.[0]" mode=""> </image>
					<view class="orderView-goods-good-right">
						<view class="orderView-goods-good-right-name">
							{{itm.goodName}}
						</view>
						<view class="orderView-goods-good-right-spec">
							规格:{{itm.specification || itm.goodSpecification}}
						</view>
						<view class="orderView-goods-good-right-other">
							数量:{{itm.amount}}
							<view class="orderView-goods-good-right-other-right">
								<image src="@/static/index/i_1.png" mode=""></image>
								{{itm.price}}
							</view>
						</view>
						<view class="orderView-goods-good-right-goodStatus" v-if="itm.status != 0">
							{{getGoodStatusDetail(itm).name}}
						</view>
					</view>
				</view>





			</template>

			<view class="orderView-goods-hj">
				<view>

				</view>
				<view class="orderView-goods-hj-right">
					总助力值 <image src="@/static/index/i_1.png"></image> <text
						style="color: #E6A600;">{{ (info.balance + info.payWelfarePaymentsPrice + info.payPrice).toFixed(2) || '0.00' }}</text>
				</view>

			</view>



		</view>
		<!-- 对于快递订单，显示收货地址信息；对于自提核销订单，隐藏地址信息 -->
		<view class="orderView-wrap" v-if="!isPickUpOrderComputed">
			<view class="orderView-wrap-title">
				<view class="orderView-wrap-title-spec">

				</view>
				<view>
					收货地址
				</view>
			</view>
			<view class="orderView-wrap-lineOne">
				{{ info.shippingAddress || '' }}
			</view>
			<view class="orderView-wrap-lineOne">
				{{ info.consignee || '' }} {{ info.contactNumber || '' }}
			</view>
			<view class="orderView-wrap-lineOne">
				备注：{{info.message}}
			</view>
		</view>

		<!-- 对于所有订单类型，都显示备注信息 -->
		<view class="orderView-wrap" v-if="isPickUpOrderComputed && info.message">
			<view class="orderView-wrap-title">
				<view class="orderView-wrap-title-spec">

				</view>
				<view>
					订单备注
				</view>
			</view>
			<view class="orderView-wrap-lineOne">
				{{info.message}}
			</view>
		</view>

		<view class="orderView-wrap">
			<view class="orderView-wrap-title">
				<view class="orderView-wrap-title-spec">

				</view>
				<view>
					订单信息
				</view>
			</view>
			<view class="orderView-wrap-lineTwo">
				<view>
					包裹编号
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.orderNo}}
					<image src="@/static/copy.png" @click="copy(info.orderNo)"></image>
				</view>
			</view>
			<view class="orderView-wrap-lineTwo">
				<view>
					收货方式
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.listOrderStoreSubList && info.listOrderStoreSubList[0]?.distribution === '1' ? '自提核销' : '快递'}}
				</view>
			</view>
			<view class="orderView-wrap-lineTwo" v-if="judgeTime(2)">
				<view>
					贡献时间
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.payTime}}
				</view>
			</view>
			<view class="orderView-wrap-lineTwo" v-if="judgeTime(5)">
				<view>
					关闭时间
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.closeTime}}
				</view>
			</view>
			<view class="orderView-wrap-lineTwo" v-if="judgeTime(5) && info.closeType_dictText">
				<view>
					关闭类型
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.closeType_dictText}}
				</view>
			</view>
			<view class="orderView-wrap-lineTwo" v-if="judgeTime(5) && info.closeExplain">
				<view>
					关闭原因
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.closeExplain}}
				</view>
			</view>
			<view class="orderView-wrap-lineTwo" v-if="judgeTime(4)">
				<view>
					收货时间
				</view>
				<view class="orderView-wrap-lineTwo-right">
					{{info.deliveryTime}}
				</view>
			</view>
		</view>
	</view>



</template>
<script setup>
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		computed,
		ref
	} from "vue";
	import {
		getAllTime,
		getSafeBottom,
		parseObjToPath,
		parseImgurl
	} from "@/utils";
	import {
		navTo,
		redTo,
		toUpPage
	} from "@/hooks";
	const imgUrl = uni.env.IMAGE_URL
	let options = ref({});
	let info = ref({});
	//各种折扣支付
	let discounts = ref({})
	//倒计时时间
	let countDonwOwnTimer = ref(0)
	//取消订单点击记录item
	let cancelOrderItem = ref({})
	let cancelOrderPop = ref();
	let orderStoreSubListsSelectIndex = ref(0)
	//确认收货点击记录item
	let takeGoosItem = ref({})
	//提示弹窗的信息
	let popupMsg = ref({
		title: '确认收货',
		content: '请您收到货后再点击确认，否则可能钱货两空！确定收到货了吗？',
		confirmText: '确定',
		showClose: true
	})
	let alertDialog = ref();
	onLoad((op) => {
		options.value = op
		refresh()
	})
	let countDownTimer = computed(() => {
		return getAllTime(countDonwOwnTimer.value) || {}
	})

	//余额
	const balanceDiscountText = computed(() => {
		let balanceDiscount = discounts.value.balanceDiscount || 0
		if (balanceDiscount >= 1 || balanceDiscount == 0) {
			return '';
		} else {
			return `(${balanceDiscount * 10}折)`;
		}
	})
	//福利金
	const payWelfarePaymentsText = computed(() => {
		let integralDiscount = discounts.value.integralDiscount || 0
		if (integralDiscount >= 1 || integralDiscount == 0) {
			return '';
		} else {
			return `(${integralDiscount * 10}折)`;
		}
	})
	//微信,支付宝
	const modePaymentText = computed(() => {
		let wxDiscount = discounts.value.wxDiscount || 0;
		let alipayDiscount = discounts.value.alipayDiscount || 0;
		if (info.value.modePayment == 0 && wxDiscount < 1 && wxDiscount != 0) {
			return `(${wxDiscount * 10}折)`;
		} else if (info.value.modePayment == 1 && alipayDiscount < 1 && alipayDiscount != 0) {
			return `(${alipayDiscount * 10}折)`;
		} else {
			return '';
		}
	})
	// 判断是否是自提核销订单
	const isPickUpOrder = computed(() => {
		return info.value.pickUpQrCode && info.value.listOrderStoreSubList && info.value.listOrderStoreSubList[0]?.distribution === '1';
	})

	// 为模板提供的计算属性，判断是否是自提核销订单
	const isPickUpOrderComputed = computed(() => {
		return info.value.pickUpQrCode && info.value.listOrderStoreSubList && info.value.listOrderStoreSubList[0]?.distribution === '1';
	})

	// 判断是否显示核销二维码和凭证信息
	// 仅在订单状态为"待核销"(未核销的自提订单)或"待收货"(状态码为"2")时显示
	const showVerificationQrCode = computed(() => {
		return options.value.state === '2' || info.value.pickUpVerificationStatus === '0';
	})

	// 核销状态文本
	const pickUpVerificationStatusText = computed(() => {
		const l = ['未核销', '已核销', '已失效']
		return l[info.value?.pickUpVerificationStatus || 0]
	})

	// 自提核销订单商品标题文本
	const pickUpGoodsTitle = computed(() => {
		// 如果是自提核销订单且状态为已核销(1)，显示"已核销商品"
		if (isPickUpOrderComputed.value && info.value.pickUpVerificationStatus === '1') {
			return '已核销商品'
		}
		// 其他状态的自提核销订单显示"待核销商品"
		return '待核销商品'
	})

	// 自提核销订单包裹标题文本
	const pickUpPackageTitle = computed(() => {
		// 如果是自提核销订单且状态为已核销(1)，显示"已核销包裹"
		if (isPickUpOrderComputed.value && info.value.pickUpVerificationStatus === '1') {
			return '已核销包裹'
		}
		// 其他状态的自提核销订单显示"待核销包裹"
		return '待核销包裹'
	})
	//每个商品的订单状态返回 0=下单成功 1=已发货 2=退款中 3=退款成功 4=换货中 5=换货成功
	function getGoodStatusDetail(i) {
		let item = {}
		switch (i.status * 1) {
			case 1:
				item.name = '已发货';
				break;
			case 2:
				item.name = '退款中';
				break;
			case 3:
				item.name = '退款成功';
				break;
			case 4:
				item.name = '换货中';
				break;
			case 5:
				item.name = '换货成功';
				break;
		}
		return item
	}
	//核销状态
	function hxStatusText() {
		let text = ''
		//0=未核销 1=已核销 2=已失效
		if (info.value.pickUpVerificationStatus) {
			switch (info.value.pickUpVerificationStatus * 1) {
				case 0:
					text = '未核销'
					break;
				case 1:
					text = '已核销'
					break;
				case 2:
					text = '已失效'
					break;
				default:
					break;
			}
		}
		return text
	}

	//取消订单弹窗显示
	function toggleCancelOrderPop() {
		cancelOrderItem.value = info.value
		cancelOrderPop.value.open()
	}
	//取消成功回调
	function cancelSuccess(selectedReasonItem) {
		uni.showModal({
			title: '取消订单',
			content: '您确定要取消订单吗?',
			success: async function(res) {
				if (res.confirm) {
					uni.showLoading({
						mask: true
					})
					let data = await uni.http.get(uni.api.abrogateOrder, {
						params: {
							isPlatform: cancelOrderItem.value.isPlatform, //店铺或者平台
							id: cancelOrderItem.value.id, //订单id
							value: selectedReasonItem.value, //取消订单编号
						}
					})
					uni.showToast({
						title: '取消成功~',
						icon: "none"
					})
					cancelOrderPop.value.close()
					//触发全局自定义事件,刷新页面,在对应的页面监听
					uni.$emit('refreshMyOrder')
					setTimeout(() => {
						toUpPage()
					}, 500)
				}
			}
		})
	}
	//待支付订单支付
	function toPay() {
		let obj = {
			id: info.value.id,
			isPlatform: info.value.isPlatform,
			payClassic: 4
		};
		redTo(`/pages/cashier/cashier${parseObjToPath(obj)}`)
		// unpaidOrderSubmit
	}
	//查看物流
	function toLogistics(orderStoreSubListId = '') {
		let obj = {
			orderListId: info.value.id,
			isPlatform: info.value.isPlatform
		}
		if (orderStoreSubListId) {
			obj.orderStoreSubListId = orderStoreSubListId
		}
		navTo(`/packageOrder/pages/logistics/logistics${parseObjToPath(obj)}`)
	}

	//确认收货
	function takeGoods() {
		takeGoosItem.value = info.value
		alertDialog.value.open();
	}
	//提示弹窗的确定
	async function confirm() {
		if (popupMsg.value.title == '确认收货') {
			uni.showLoading({
				mask: true
			})
			let {
				data
			} = await uni.http.post(uni.api.affirmOrderDelivery, {
				id: takeGoosItem.value.id,
				isPlatform: takeGoosItem.value.isPlatform
			})
			uni.showToast({
				title: '收货成功~',
				icon: "none"
			})
			//触发全局自定义事件,刷新页面,在对应的页面监听
			uni.$emit('refreshMyOrder')
			setTimeout(() => {
				toUpPage()
			}, 500)
		}
	}

	//评价
	function toEvaluate() {
		let obj = {
			id: info.value.id,
			isPlatform: info.value.isPlatform
		}
		navTo(`/pages/evaluate/evaluate${parseObjToPath(obj)}`)
	}


	async function refresh() {
		uni.showLoading({
			mask: true
		})
		let {
			data
		} = await uni.http.post(uni.api.viewOrderInfo, {
			id: options.value.id,
			isPlatform: options.value.isPlatform
		})
		if (data.result.orderStoreSubLists?.length) {
			orderStoreSubListsSelectIndex.value = 0
		} else if (data.result.listOrderStoreSubList?.length && data.result.listOrderStoreSubList[0]?.orderStoreGoodRecords?.length) {
			orderStoreSubListsSelectIndex.value = -1
		} else {
			// 没有发货商品也没有未发货商品，设置为0避免索引错误
			orderStoreSubListsSelectIndex.value = 0
		}
		if (data.result.discount) {
			try {
				Object.values(JSON.parse(data.result.discount)).forEach((item, index) => {
					const lists = ['wxDiscount', 'alipayDiscount', 'balanceDiscount', 'integralDiscount'];
					discounts.value[lists[index]] = item * 1
				});
			} catch (e) {
				//TODO handle the exception
			}
		}
		info.value = data.result
		//倒计时
		let countDownApiUrl = ''
		if (options.value.state == 3) {
			countDownApiUrl = uni.api.prepaidOrderTimer
		}
		if (options.value.state == 6) {
			countDownApiUrl = uni.api.confirmReceiptTimer
		}
		if (countDownApiUrl && !data.result.pickUpQrCode) {
			let {
				data: countDownData
			} = await uni.http.post(countDownApiUrl, {
				orderId: data.result.id,
				isPlatform: data.result.isPlatform
			});
			countDonwOwnTimer.value = countDownData.result?.timer || 0

		}
		uni.hideLoading()
	}
	//复制
	function copy(data) {
		uni.setClipboardData({
			data
		});
	}
	//判断状态显示订单详情时间数据
	function judgeTime(index) {
		if (!options.value.state) return false
		let show = false
		let type = []
		switch (options.value.state * 1) {
			case 2:
				//交易关闭
				type = [0, 1, 5];
				break;
			case 3:
				//待付款
				type = [0, 1];
				break;
			case 5:
				//代发货
				type = [0, 1, 2];
				break;
			case 6:
				//代收货
				type = [0, 1, 2, 3];
				break;
			default:
				type = [0, 1, 2, 3, 4];
				break;
		}
		show = type.indexOf(index) != -1;
		return show
	}
</script>
<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.orderView {

		.orderStoreSubListsSelectedClass {
			border: 2rpx solid #1A69D1 !important;
		}

		.timeCountDown {
			display: flex;
			align-items: center;
			justify-content: flex-start;
			flex-wrap: wrap;
		}

		&-top {
			padding: 40rpx 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			border-bottom: 2rpx solid #E2E2E2;
			position: relative;
			background-color: white;

			&-hx {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;

				&-desc {
					color: #999999;
					font-size: 28rpx;
				}

				&-img {
					margin: 20rpx 0;
					width: 316rpx;
					height: 316rpx;
					border-radius: 16rpx;
				}

				&-show {
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: #999999;

					&-img {
						width: 30rpx;
						height: 30rpx;
					}
				}
			}

			&-status {
				color: #333333;
				font-size: 40rpx;
				margin-bottom: 14rpx;
			}

			&-spec {
				color: #999999;
				font-size: 28rpx;
			}

			&-custom {
				width: 44rpx;
				height: 44rpx;
				position: absolute;
				right: 30rpx;
				top: 30rpx;
				z-index: 1;
			}

			&-scrollView {
				width: 700rpx;
				height: 110rpx;
				white-space: nowrap;

				&-ct {
					display: flex;
					align-items: center;

					&-view {
						width: 240rpx;
						height: 100rpx;
						border: 2rpx solid #999999;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						padding: 0 10rpx;
						margin-right: 20rpx;

						&-img {
							width: 80rpx;
							height: 80rpx;
							border: 2rpx solid #f5f5f5;
							border-radius: 8rpx;
							margin-right: 15rpx;
						}

						&-right {
							height: 80rpx;
							font-size: 26rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							color: #000000;
						}
					}
				}
			}

			&-button {
				display: flex;
				align-items: center;
				margin-top: 20rpx;

				>view {
					height: 60rpx;
					line-height: 60rpx;
					padding: 0 20rpx;
					font-size: 26rpx;
					color: #999999;
					border: 2rpx solid #999999;
					border-radius: 100rpx;
					margin-right: 20rpx;
				}

				>view:last-child {
					margin-right: 0;
				}

			}
		}


		&-wrap {
			padding: 30rpx;
			background-color: white;
			margin-bottom: 20rpx;

			&-title {
				margin-bottom: 20rpx;
				display: flex;
				align-items: center;
				color: #000000;
				font-size: 28rpx;

				&-spec {
					width: 4rpx;
					height: 20rpx;
					background: #1a69d1;
					border-radius: 10rpx;
					margin-right: 14rpx;
				}

			}

			&-lineTwo {
				height: 100rpx;
				border-bottom: 2rpx solid #D9D9D9;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #666666;
				font-size: 28rpx;

				&-right {
					display: flex;
					align-items: center;
					justify-content: flex-end;

					>image {
						width: 30rpx;
						height: 30rpx;
						margin-left: 10rpx;
					}
				}
			}

			&-lineTwo:last-child {
				border-bottom: none;
			}

			&-lineOne {
				color: #666666;
				font-size: 28rpx;
				line-height: 40rpx;
				margin-bottom: 14rpx;
			}

			&-lineOne:last-child {
				margin-bottom: 0;
			}
		}


		&-goods {
			padding: 0 30rpx 30rpx;
			background-color: white;
			margin-bottom: 30rpx;

			&-specTop {
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-name {
					color: #333333;
					font-size: 28rpx;
				}

				&-btn {
					width: 200rpx;
					height: 56rpx;
					border: 2rpx solid #999999;
					border-radius: 100rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #999999;
					font-size: 24rpx;
				}
			}

			&-hj {
				// padding-top: 26rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-top: 2rpx solid #f6f6f6;


				&-right {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					height: 80rpx;
					font-size: 28rpx;
					color: #999999;

					>image {
						width: 30rpx;
						height: 30rpx;
						margin: 0 14rpx;
					}
				}
			}

			&-good {
				display: flex;
				align-items: center;
				padding: 20rpx 0;
				position: relative;

				&-left {
					width: 184rpx;
					height: 184rpx;
					margin-right: 14rpx;
				}

				&-right {
					height: 160rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					&-name {
						font-size: 28rpx;
						color: #000000;
					}

					&-spec,
					&-other,
					&-goodStatus {
						font-size: 28rpx;
						color: #999999
					}

					&-goodStatus {
						position: absolute;
						z-index: 10;
						right: 0;
						bottom: 30rpx;
					}

					&-other {
						display: flex;
						align-items: center;

						&-right {
							display: flex;
							align-items: center;
							margin-left: 20rpx;
							color: #E6A600;

							>image {
								width: 30rpx;
								height: 30rpx;
								// margin-right: 10rpx;
							}
						}
					}
				}
			}
		}
	}
</style>