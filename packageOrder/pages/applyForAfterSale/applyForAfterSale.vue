<template>

	<view class="applyForAfterSale" v-if="list?.length > 0">


		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-line">
				<view style="color: #333333;font-weight: bold;">
					申请换货
				</view>
				<!-- <view>
					寄回换货
				</view> -->
			</view>
			<view class="applyForAfterSale-wrap-line">
				<view>
					平台同意换货后，需要您寄回已收到的货物
				</view>
				<view>

				</view>
			</view>
		</view>
		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-title">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					原商品
				</view>
			</view>
			<view class="applyForAfterSale-wrap-info">
				<view class="applyForAfterSale-wrap-info-top">
					<view class="applyForAfterSale-wrap-info-top-left">
						<view>
							{{info.storeName}}
						</view>
						<view class="applyForAfterSale-wrap-info-top-left-label" v-if="storeTypeList?.length">
							{{storeTypeList.find(i=>info.storeType === i.value)?.title}}
						</view>

					</view>
					<view class="applyForAfterSale-wrap-info-top-right">

					</view>

				</view>

				<view class="applyForAfterSale-wrap-info-good" v-for="(itm,idx) in list" :key="itm.id">
					<image v-if="itm.mainPicture" class="applyForAfterSale-wrap-info-good-left"
						:src="imgUrl + parseImgurl(itm.mainPicture)?.[0]" mode="">
					</image>
					<image v-else-if="itm.goodMainPicture" class="applyForAfterSale-wrap-info-good-left"
						:src="imgUrl + parseImgurl(itm.goodMainPicture)?.[0]" mode=""> </image>
					<view class="applyForAfterSale-wrap-info-good-right">
						<view class="applyForAfterSale-wrap-info-good-right-name">
							{{itm.goodName}}
						</view>
						<view class="applyForAfterSale-wrap-info-good-right-spec">
							规格:{{itm.specification || itm.goodSpecification}}
						</view>
						<view class="applyForAfterSale-wrap-info-good-right-other">
							数量:{{itm.amount}}
							<view class="applyForAfterSale-wrap-info-good-right-other-right">
								<image src="@/static/index/i_1.png" mode=""></image>
								{{itm.price}}
							</view>
						</view>

					</view>
				</view>
			</view>

		</view>

		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-title">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					换货数量
				</view>
			</view>
			<view class="applyForAfterSale-wrap-line">
				<view>
					最多可退{{maxCanAfterSaled()}}件
				</view>
				<view>
					<numberInput v-model='list[0].refundAmount' @change="refundAmountChange($event)" :min="1"
						:max="maxCanAfterSaled()">
					</numberInput>
				</view>
			</view>
		</view>

		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-title">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					换货商品
				</view>
			</view>
			<view class="applyForAfterSale-wrap-line">
				<view>
					将使用原商品规格
				</view>
				<view class="applyForAfterSale-wrap-line-info">
					系统将自动使用当前订单商品规格
				</view>
			</view>
		</view>




		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-title">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					收货地址
				</view>
			</view>
			<view class="applyForAfterSale-wrap-line" @click="toSelectAddress">
				<view v-if="!otherSubmitInfoForHH.memberShippingAddressId">
					请选择收货地址
				</view>
				<view v-else>
					{{memberShippingAddressRes.areaExplan}}{{memberShippingAddressRes.areaAddress}}
				</view>
				<image style="width: 30rpx;height: 30rpx;" src="@/static/right-arrow-gray.png" mode=""></image>
			</view>
		</view>

		<!-- 平台订单才需要选择换货原因 -->
		<view class="applyForAfterSale-wrap" v-if="info.isPlatform === '1'">
			<view class="applyForAfterSale-wrap-title">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					换货原因 <text class="required-mark">*</text>
				</view>
			</view>
			<view class="applyForAfterSale-wrap-line" v-for="(item,index) in reasonList" :key='index'
				@click="selectedReason(index)">
				<view>
					{{item.text || item.name}}
				</view>
				<view class="applyForAfterSale-wrap-line-circle">
					<view v-if="selectedReasonIndex === index"></view>
				</view>
			</view>
		</view>

		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-title" style="margin-bottom: 20rpx;">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					换货凭证 <text class="required-mark">*</text>
				</view>
			</view>
			<view>
				<uni-file-picker fileMediatype="image" :image-styles="imageStyles" limit="5"
					@select="fileSelect($event)" @delete='fileDelete($event)' />
			</view>
			<view class="applyForAfterSale-wrap-tip">
				请上传换货凭证，以便商家更好地处理您的换货申请
			</view>
		</view>

		<view class="applyForAfterSale-wrap">
			<view class="applyForAfterSale-wrap-title" style="margin-bottom: 20rpx;">
				<view class="applyForAfterSale-wrap-title-spec">

				</view>
				<view>
					换货说明 <text class="required-mark">*</text>
				</view>
			</view>
			<view>
				<textarea class="applyForAfterSale-wrap-textarea" maxlength='150' placeholder="请填写申请说明，以便店铺客服更好的处理售后问题"
					v-model="list[0].remarks"></textarea>
			</view>
			<view class="applyForAfterSale-wrap-tip">
				请详细描述换货原因，以便商家更好地处理您的换货申请
			</view>
		</view>

		<view class="applyForAfterSale-btn">
			<view @click="submit">
				提交
			</view>
		</view>


		<!-- 优惠券 -->
		<uni-popup ref="couponShowRef" type="bottom" :safeArea="false">
			<view class="couponShowRef">
				<view class="couponShowRef-title">
					优惠券退回明细
				</view>
				<view class="couponShowRef-desc">
					订单内<text>全部使用优惠券的商品</text>申请退款时，可退回优惠券，退款成功后，您可以在<text>我的-优惠券</text>中查看。
				</view>
				<view>
					<discountCouponWrap :info='showCouponInfo'></discountCouponWrap>
				</view>
				<view class="couponShowRef-btnOpe btnOpe">
					<button class="bottomFixBtn" @click="closeAllCoupon">
						我已知晓
					</button>
				</view>
			</view>
		</uni-popup>
		<!-- 移除选择规格弹窗，默认使用原商品规格 -->

	</view>
</template>

<script setup>
	import {
		onLoad,
		onUnload,
	} from "@dcloudio/uni-app"
	import {
		computed,
		ref
	} from "vue";
	import {
		clone,
		getSafeBottom,
		parseImgurl
	} from "@/utils";
	import {
		navTo,
		redTo,
		specificationPopPub,
		toUpPage,
		uploadImg
	} from "@/hooks";
	import {
		locationInfo
	} from "@/store";
	let imageStyles = {
		width: 100,
		height: 100,
		border: {
			radius: '8px',
		}
	}
	let cancelOrderPop = ref();
	let cancelOrderRef = ref()
	let couponShowRef = ref();
	let storeTypeList = ref([]);
	let showCouponInfo = ref({})
	let reasonList = ref([])
	const {
		selectInfo,
		addCart,
		showSpecificationWrap,
		specificationWrapChange,
		btnLoading,
		specificationWrapRef,
		specificationPop
	} = specificationPopPub()
	//售后商品列表
	let list = ref([]);
	//当前订单信息
	let info = ref({});
	//类型 1 退款 2退货退款  3换货
	let tkType = ref(1)
	let selectedId = ref([])
	//凭证图片(未处理)
	let picutures = ref([])
	//点击退款原因记录当前索引
	let forReasonRecordIndex = ref('')
	//点击打开换货弹窗记录当前索引
	let hhResRecordIndex = ref('')
	//点击打开换货弹窗记录当前换货商品索引
	let hhResRecordHHIndex = ref('')
	//退款和换货的其他信息
	let otherSubmitInfo = ref({
		isPlatform: '',
		orderId: '',
		refundType: ''
	})
	const selectedReasonIndex = ref()
	//换货另外单独的信息
	let otherSubmitInfoForHH = ref({
		memberShippingAddressId: ''
	})
	//换货选择收货地址展示的信息
	let memberShippingAddressRes = ref({})
	//是否是修改售后
	let isEditAfter = ref('')
	const locat = locationInfo().info
	const imgUrl = uni.env.IMAGE_URL
	onLoad(() => {
		getStoreTypeDicts()
		getReasonList()
		uni.$once('applyForAfterSaleInfo', refresh)
	})
	onUnload(() => {
		//页面销毁 销毁监听
		uni.$off('selectAddresss', selectAddressCallback)
	})
	//优惠券id集合 判断是否有优惠券
	const marketingDiscountCouponIdList = computed(() => {
		return [...new Set(list.value.map(i => i.marketingDiscountCouponId || '').filter(i => i))]
	})
	//类型对应标题名
	const typeInfoText = computed(() => {
		if (tkType.value == 1) return '退款'
		if (tkType.value == 2) return '退货退款'
		if (tkType.value == 3) return '换货'
	})
	//选择收货地址
	function toSelectAddress() {
		navTo('/pages/myAddress/myAddress?type=select')
	}
	//选择地址监听
	function selectAddressCallback(data) {
		otherSubmitInfoForHH.value.memberShippingAddressId = data.id
		memberShippingAddressRes.value = data;
	}
	//将逗号分割字符串转化成数组
	function dhStrToSz(str, bd = ',') {
		if (typeof str === 'string') {
			if (str.indexOf(bd) == -1) return [str];
			return str.split(bd);
		}
		return []
	}

	function selectedReason(index) {
		selectedReasonIndex.value = index
		if (Object.prototype.toString.call(list.value[0].refundReasonResult) !== '[object Object]') {
			list.value[0].refundReasonResult = {
				name: reasonList.value[index].title,
				id: reasonList.value[index].value
			}
		} else {
			list.value[0].refundReasonResult.name = reasonList.value[index].title
			list.value[0].refundReasonResult.id = reasonList.value[index].value
		}

	}
	//获取店铺类型字典
	async function getStoreTypeDicts() {

		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=store_type`);
		storeTypeList.value = data.result || []
	}

	//获取原因
	async function getReasonList() {
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=order_refund_reason`);
		reasonList.value = data.result || []
	}

	//打开换货商品弹窗 index:第几个商品  i:第几个换货商品
	function hhRes(index, i) {
		hhResRecordIndex.value = index
		hhResRecordHHIndex.value = i
		showSpecificationWrap(clone({
			...list.value[index],
			isPlatform: info.value.isPlatform
		}))
	}
	//确认换货的信息
	function sureHH() {
		//找到当前商品的换货商品记录
		let nowExchangeGoodSpecificationId = list.value[hhResRecordIndex.value]?.exchangeGoodSpecificationId;
		let nowExchangeGoodSpecification = list.value[hhResRecordIndex.value]?.exchangeGoodSpecification
		//将数据转化成数组
		let resultListId = dhStrToSz(nowExchangeGoodSpecificationId) || []
		let resultList = dhStrToSz(nowExchangeGoodSpecification, '|||') || []
		//修改当前的换货记录
		resultListId[hhResRecordHHIndex.value] = specificationWrapRef.value.specificationId
		resultList[hhResRecordHHIndex.value] = specificationWrapRef.value.specification
		list.value[hhResRecordIndex.value].exchangeGoodSpecificationId = resultListId.join(',')
		list.value[hhResRecordIndex.value].exchangeGoodSpecification = resultList.join('|||')

		specificationPop.value.close()
	}
	//返回最多可退多少件
	function maxCanAfterSaled() {
		const item = list.value[0]
		let regonAmount = isEditAfter.value ? item.goodRecordAmount : item.amount;
		let ongoingRefundCount = item.ongoingRefundCount || 0
		return regonAmount - ongoingRefundCount
	}
	//返回最大金额
	function returenMaxPrice(item) {
		if (!isEditAfter.value) {
			if (!item.actualPayment) return 0
			return (item.actualPayment).toFixed(2)
		} else {
			if (!item.goodRecordActualPayment) return 0
			return (item.goodRecordActualPayment).toFixed(2)
		}

	}
	//展示优惠券弹窗
	async function showAllCoupon() {
		if (!showCouponInfo.value.id) {
			uni.showLoading({
				mask: true
			})
			let {
				data
			} = await uni.http.post(uni.api.findMarketingDiscountCouponInfo, {
				id: marketingDiscountCouponIdList.value[0],
				location: `${locat.latitude},${locat.longitude}`
			});
			showCouponInfo.value = data.result
			uni.hideLoading()
		}
		couponShowRef.value.open()

	}
	//关闭优惠券弹窗
	function closeAllCoupon() {
		couponShowRef.value.close()
	}


	function refresh(a) {
		let results = clone(a.info)
		console.log(results)
		if (results.memberShippingAddressId) {
			try {
				memberShippingAddressRes.value = JSON.parse(results.exchangeMemberShippingAddress)
				otherSubmitInfoForHH.value.memberShippingAddressId = results.memberShippingAddressId
			} catch (error) {
				//TODO handle the exception
			}
		}
		info.value = results;
		selectedId.value = clone(a.selectedId)
		list.value = (info.value.goods.filter(i => selectedId.value.indexOf(i.id) != -1) || []).map(i => {
			i.refundCertificate = ''
			return i
		})
		tkType.value = a.tkType
		isEditAfter.value = a.isEditAfter || ''
		if (a.tkType == 3) {
			selectInfo.value = clone({
				...list.value[0],
				isPlatform: info.value.isPlatform
			})
			//监听地址选择
			uni.$on('selectAddresss', selectAddressCallback)
		}
	}

	//退款金额变化（最大值限制）
	function refundPriceChange(e, item) {
		const val = e.detail.value;
		setTimeout(() => {
			if (val * 1 > returenMaxPrice(item)) {
				item.refundPrice = returenMaxPrice(item)
			}
		}, 10)
	}
	//退款商品数量变化
	function refundAmountChange(refundAmount) {
		const item = list.value[0]
		item.refundAmount = refundAmount
		// 不再需要清空规格，使用原商品规格
	}

	//关闭原因弹窗
	function closeCancelOrderPop() {
		cancelOrderPop.value.close()
	}


	//选择图片回调
	function fileSelect(e) {
		const item = list.value[0]
		item.refundCertificate = item.refundCertificate?.length > 0 ? [...item.refundCertificate, ...e.tempFilePaths] : e
			.tempFilePaths
	}
	//删除图片回调
	function fileDelete(e) {
		const item = list.value[0]
		let index = item.refundCertificate.indexOf(e.tempFilePath);
		if (index != -1) item.refundCertificate.splice(index, 1);
	}

	//弹出原因选择
	function showReason(index, item) {
		forReasonRecordIndex.value = index
		//如果是1688商品
		if (item.taoOrderId) {
			cancelOrderRef.value.changeReasonType(3, {
				taoOrderId: item.taoOrderId
			})
		} else {
			cancelOrderRef.value.changeReasonType(1)
		}
		cancelOrderPop.value.open()
	}

	//提交
	async function submit() {
		let canSubmit = false
		if (tkType.value != 3) {
			canSubmit = list.value.every(i => {
				if (i.refundReasonResult) {
					if (i.refundReasonResult.needVoucher && !i.refundCertificate) return false
					return true
				}
				return false
			})
		} else {
			if (!otherSubmitInfoForHH.value.memberShippingAddressId) {
				uni.showToast({
					title: '请选择收货地址',
					icon: 'none'
				})
				return;
			}

			// 检查换货凭证和换货说明是否已填写
			if (!list.value[0].refundCertificate || list.value[0].refundCertificate.length === 0) {
				uni.showToast({
					title: '请上传换货凭证',
					icon: 'none'
				})
				return;
			}

			if (!list.value[0].remarks) {
				uni.showToast({
					title: '请填写换货说明',
					icon: 'none'
				})
				return;
			}

			// 店铺订单换货不需要检查refundReasonResult
			if (info.value.isPlatform === '1') {
				// 平台订单需要检查换货原因
				canSubmit = list.value.every(i => {
					if (i.refundReasonResult) {
						return true
					}
					return false
				})
			} else {
				// 店铺订单直接通过
				canSubmit = true
			}
		}
		console.log(list.value)
		if (!canSubmit) {
			uni.showToast({
				title: '请将部分信息填写完整',
				icon: "none"
			})
			return;
		}

		let orderRefundListDtos = list.value.map(i => {
			if (tkType.value != 3) {
				// 退款和退货退款
				let refundReasonParse = {
					...i.refundReasonResult,
					id: i.refundReasonResult.id || i.refundReasonResult.value || '',
				}
				return {
					orderGoodRecordId: i.id,
					refundReason: refundReasonParse,
					refundPrice: i.refundPrice || 0,
					refundAmount: i.refundAmount || 1,
					remarks: i.remarks || '',
					refundCertificate: i.refundCertificate || '',
					taoOrderId: i.taoOrderId || '' //判断是否是1688商品，目前仅作上传图片用
				}
			} else {
				// 换货
				// 店铺订单换货不需要传递refundReason对象
				const isPlatform = info.value.isPlatform === '1';

				// 不传exchangeGoodSpecificationId和exchangeGoodSpecification，系统会自动使用当前订单商品规格
				const baseObj = {
					orderGoodRecordId: i.id,
					refundAmount: i.refundAmount || 1,
					remarks: i.remarks || '',
					refundCertificate: i.refundCertificate || '',
					taoOrderId: i.taoOrderId || '' //判断是否是1688商品，目前仅作上传图片用
				};

				// 只有平台订单才需要传递refundReason
				if (isPlatform && i.refundReasonResult) {
					let refundReasonParse = {
						...i.refundReasonResult,
						id: i.refundReasonResult.id || i.refundReasonResult.value || '',
					}
					baseObj.refundReason = refundReasonParse;
				}

				return baseObj;
			}

		})
		otherSubmitInfo.value.isPlatform = info.value.isPlatform
		otherSubmitInfo.value.orderId = info.value.id
		otherSubmitInfo.value.refundType = tkType.value * 1 - 1
		uni.showLoading({
			mask: true
		})
		for (let it of orderRefundListDtos) {
			if (it.refundCertificate.length > 0) {
				let sz = []
				for (let item of it.refundCertificate) {
					if (item.indexOf('tmp') != -1) {
						let data = await uploadImg(item, it.taoOrderId ? 2 : 1);
						sz.push(data.message)
					} else {
						sz.push(item)
					}

				}
				it.refundCertificate = sz.join(',')
			}
		}
		let subResObj = {
			orderRefundListDtos,
			...otherSubmitInfo.value
		}
		if (tkType.value == 3) {
			subResObj = {
				...subResObj,
				...otherSubmitInfoForHH.value
			}
		}
		let apiUrl = uni.api.refundApply
		if (isEditAfter.value) {
			apiUrl = uni.api.refundEdit
			subResObj = {
				refundType: subResObj.refundType,
				orderListId: subResObj.orderId,
				...subResObj.orderRefundListDtos[0],
				id: info.value.mainId
			}
			if (tkType.value == 3) {
				subResObj.exchangeMemberShippingAddress = JSON.stringify(memberShippingAddressRes.value)
				subResObj.memberShippingAddressId = otherSubmitInfoForHH.value.memberShippingAddressId
			}
		}
		console.log(subResObj)
		await uni.http.post(apiUrl, subResObj, {
			header: {
				'content-type': 'application/json'
			}
		})
		uni.showToast({
			title: '提交成功~',
			icon: "none"
		})
		setTimeout(() => {
			uni.$emit('refreshMyOrder')
			if (isEditAfter.value) {
				uni.$emit('afterSaleOrderDetailInfo')
				toUpPage()
			} else {
				// 修改跳转逻辑，直接跳转到包裹页面的售后页签
				uni.redirectTo({
					url: '/pages/myPackage/myPackage?tabIndex=4'
				})
			}
		}, 500)
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.applyForAfterSale {
		padding-bottom: 150rpx;

		.required-mark {
			color: #ff4d4f;
			margin-left: 4rpx;
		}

		&-wrap-tip {
			font-size: 24rpx;
			color: #999;
			margin-top: 10rpx;
			padding: 0 20rpx;
		}

		&-wrap-line-info {
			font-size: 24rpx;
			color: #999;
		}

		&-btnOpe {
			width: 100%;
			padding: 30rpx 100rpx;
			display: flex;
			align-items: center;
			background-color: white;

			&-btn {
				width: 100%;
				height: 80rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 28rpx;
			}
		}


		&-btn {
			width: 750rpx;
			height: 150rpx;
			position: fixed;
			left: 0;
			bottom: 0;
			z-index: 10;
			display: flex;
			align-items: center;
			justify-content: center;
			background-color: white;
			box-shadow: 0px -5px 30px 0px rgba(194, 193, 193, 0.25);

			>view {
				width: 500rpx;
				height: 80rpx;
				line-height: 80rpx;
				text-align: center;
				background: #22a3ff;
				border-radius: 100rpx;
				color: white;
				font-size: 28rpx;
			}
		}

		&-wrap {
			background-color: white;
			padding: 30rpx;
			margin-bottom: 20rpx;


			&-info {
				&-good {
					display: flex;
					align-items: center;
					padding: 20rpx 0;
					position: relative;

					&-left {
						width: 184rpx;
						height: 184rpx;
						margin-right: 14rpx;
					}

					&-right {
						height: 160rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						&-name {
							font-size: 28rpx;
							color: #000000;
						}

						&-spec,
						&-other,
						&-goodStatus {
							font-size: 28rpx;
							color: #999999
						}

						&-goodStatus {
							position: absolute;
							z-index: 10;
							right: 0;
							bottom: 30rpx;
						}

						&-other {
							display: flex;
							align-items: center;

							&-right {
								display: flex;
								align-items: center;
								margin-left: 20rpx;
								color: #E6A600;

								>image {
									width: 30rpx;
									height: 30rpx;
									// margin-right: 10rpx;
								}
							}
						}
					}
				}


				&-top {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding: 20rpx 0;
					border-bottom: 2rpx solid #E2E2E2;

					&-left {
						display: flex;
						align-items: center;
						color: #333333;
						font-size: 28rpx;

						&-label {
							height: 40rpx;
							line-height: 40rpx;
							color: #22A3FF;
							padding: 0 30rpx;
							background: #d5eeff;
							border-radius: 100px;
							margin: 0 20rpx;
						}

						&-arrow {
							width: 30rpx;
							height: 30rpx;
						}
					}

					&-right {
						font-weight: 600;
						color: #333333;
						font-size: 28rpx;
					}
				}
			}




			&-line {
				height: 100rpx;
				border-bottom: 2rpx solid #F5F5F5;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #666666;
				font-size: 28rpx;

				&-circle {
					width: 30rpx;
					height: 30rpx;
					border: 2rpx solid #999999;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					margin-right: 30rpx;

					>view {
						width: 14rpx;
						height: 14rpx;
						background-color: #28a7ff;
						border-radius: 50%;
					}
				}
			}

			>view:last-child {
				border-bottom: none;
			}

			&-textarea {
				padding: 30rpx;
				font-size: 28rpx;
			}

			&-title {
				display: flex;
				align-items: center;
				font-size: 28rpx;
				color: #000000;
				padding-bottom: 20rpx;
				border-bottom: 2rpx solid #F5F5F5;

				&-spec {
					width: 4rpx;
					height: 20rpx;
					background: #1a69d1;
					border-radius: 100rpx;
					margin-right: 15rpx;
				}
			}
		}



		.couponShowRef {
			width: 750rpx;
			background: #F9F9F9;
			border-radius: 32rpx 32rpx 0 0;
			padding: 32rpx;

			&-title {
				font-size: 32rpx;
				color: #333333;
				margin-bottom: 32rpx;
			}

			&-desc {
				color: #858585;
				font-size: 24rpx;
				margin-bottom: 32rpx;

				text {
					color: #222222;
				}
			}

			&-btnOpe {
				position: relative;
				width: 100%;
				padding-left: 0;
				padding-right: 0;
				padding-bottom: v-bind(getSafeBottom());
				background-color: transparent;
			}
		}

	}
</style>