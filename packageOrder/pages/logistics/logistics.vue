<template>
	<view class="logistics">
		<template v-if="info.orderProviderLists?.length > 0">
			<view class="logistics-tab" v-if="info.orderProviderLists?.length > 1">
				<view v-for="(item,index) in info.orderProviderLists" :key="index"
					:class="{tabSelected:index == tabSelectIndex}" @click="toggleTabSelectIndex(index)">
					包裹{{index + 1}}
				</view>
			</view>
			<view class="logistics-content" v-if="info.orderProviderLists?.[tabSelectIndex]">
				<view class="logistics-content-info">
					<view>
						<view>
							{{info.orderProviderLists[tabSelectIndex].logisticsCompany}}
						</view>
						<view>
						</view>
					</view>
					<view>
						<view>
							物流编号：{{info.orderProviderLists[tabSelectIndex].trackingNumber}}
						</view>
						<view>
						</view>
					</view>
				</view>
				<view v-if="info.orderProviderLists?.[tabSelectIndex]?.mainPicture">
					<scroll-view class="logistics-content-scroll" scroll-x="true">
						<image v-for="(item,index) in info.orderProviderLists[tabSelectIndex].mainPicture" :key="index"
							class="logistics-content-scroll-item" :src="imgUrl + item" mode="">
						</image>
					</scroll-view>
				</view>


				<view v-if="info.orderProviderLists[tabSelectIndex]?.logisticsTracking?.result?.list?.length > 0">
					<!-- 纵向排列 -->
					<uni-steps :options="info.orderProviderLists[tabSelectIndex].logisticsTracking.result.list"
						direction="column" titleDefaultKey='status' descDefaultKey="time"
						:active="info.orderProviderLists[tabSelectIndex].logisticsTracking.result.list.length - 1"
						active-color='#1A69D1' deactive-color="#999999"></uni-steps>
				</view>
			</view>


		</template>

	</view>
</template>
<script setup>
	import {
		ref
	} from "vue";
	import {
		onLoad
	} from "@dcloudio/uni-app"
	import {
		parseImgurl
	} from "@/utils";
	let options = ref({})
	let tabSelectIndex = ref(0);
	let info = ref({})
	const imgUrl = uni.env.IMAGE_URL;

	function toggleTabSelectIndex(index) {
		tabSelectIndex.value = index
	}
	onLoad((op) => {
		options.value = op
		refresh()
	})

	async function refresh() {
		uni.showLoading({
			mask: true
		})
		let {
			data
		} = await uni.http.get(uni.api.parcelInformation, {
			params: options.value
		})

		if (data.result.orderProviderLists?.length > 0) {
			data.result.orderProviderLists = data.result.orderProviderLists.map(item => {
				item.logisticsTracking = JSON.parse(item.logisticsTracking)
				item.mainPicture = parseImgurl(item.mainPicture)
				return item
			})
		}
		info.value = data.result
		uni.hideLoading()
	}
</script>
<style lang="scss">
	.logistics {
		width: 750rpx;

		&-tab {
			width: 100%;
			height: 80rpx;
			display: flex;
			align-items: center;
			position: sticky;
			top: 0;
			z-index: 5;

			>view {
				flex: 1;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 32rpx;
				color: #222222;
			}

			.tabSelected {
				font-weight: bold !important;
				color: #1A69D1 !important;

			}
		}

		&-content {
			width: 100%;
			padding: 24rpx;

			&-scroll {
				white-space: nowrap;
				width: 100%;
				height: 120rpx;

				&-item {
					display: inline-block;
					width: 120rpx;
					height: 120rpx;
					margin-right: 24rpx;
				}

				&-item:last-child {
					margin: 0;
				}
			}

			>view {
				width: 100%;
				background: #FFFFFF;
				border-radius: 24rpx;
				padding: 24rpx 20rpx;
				margin-bottom: 24rpx;
			}

			>view:last-child {
				margin-bottom: 0;
			}

			&-info {
				font-size: 26rpx;
				color: #222222;
				border-bottom: 2rpx solid #F5F5F5;

				>view {
					width: 100%;
					display: flex;
					align-items: center;
					justify-content: space-between;
					margin-bottom: 40rpx;
				}

				>view:last-child {
					margin-bottom: 0;
				}
			}
		}
	}
</style>