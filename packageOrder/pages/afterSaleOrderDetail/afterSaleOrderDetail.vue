<template>
	<view class="afterSaleOrderDetail">
		<view class="afterSaleOrderDetail-wrap"  >
			<view class="afterSaleOrderDetail-wrap-info">
				<view class="afterSaleOrderDetail-wrap-info-top">
					<view class="afterSaleOrderDetail-wrap-info-top-left">
						<view>
							{{info.storeName}}
						</view>
						<view class="afterSaleOrderDetail-wrap-info-top-left-label" v-if="storeTypeList?.length">
							{{storeTypeList.find(i=>info.storeType === i.value)?.title}}
						</view>
						
					</view>
					<view class="afterSaleOrderDetail-wrap-info-top-right">
						
					</view>

				</view>

				<view class="afterSaleOrderDetail-wrap-info-good" v-for="(itm,idx) in info.goods" :key="itm.id">
					<image v-if="itm.mainPicture" class="afterSaleOrderDetail-wrap-info-good-left"
						:src="imgUrl + parseImgurl(itm.mainPicture)?.[0]" mode="">
					</image>
					<image v-else-if="itm.goodMainPicture" class="afterSaleOrderDetail-wrap-info-good-left"
						:src="imgUrl + parseImgurl(itm.goodMainPicture)?.[0]" mode=""> </image>
					<view class="afterSaleOrderDetail-wrap-info-good-right">
						<view class="afterSaleOrderDetail-wrap-info-good-right-name">
							{{itm.goodName}}
						</view>
						<view class="afterSaleOrderDetail-wrap-info-good-right-spec">
							规格:{{itm.specification || itm.goodSpecification}}
						</view>
						<view class="afterSaleOrderDetail-wrap-info-good-right-other">
							数量:{{itm.amount}}
							<view class="afterSaleOrderDetail-wrap-info-good-right-other-right">
								<image src="@/static/index/i_1.png" mode=""></image>
								{{itm.price}}
							</view>
						</view>
						<view class="afterSaleOrderDetail-wrap-info-good-right-goodStatus" v-if="itm.status != 0">
							{{getGoodStatusDetail(itm).name}}
						</view>
					</view>
				</view>
			</view>
			<view class="afterSaleOrderDetail-wrap-bottom">
				<view class="afterSaleOrderDetail-wrap-bottom-left">
					合计 <view class="afterSaleOrderDetail-wrap-bottom-left-lv">
						<image src="@/static/index/i_1.png" mode=""></image>
						{{ info.actualPayment }}
					</view>
				</view>
				<view class="afterSaleOrderDetail-wrap-bottom-right">
					共 <text style="color: #333333;margin: 0 6rpx;">{{ info.allNumberUnits }}</text> 件商品
				</view>
			</view>


		</view>
		<view class="afterSaleOrderDetail-alWrap afterSaleOrderDetail-opera" style="padding: 0 20rpx;">
			<!-- <view class="afterSaleOrderDetail-opera-line" @click="toSale(1)">
				<image src="@/static/afterSaleOrderDetail/c2.png" mode=""></image>
				<view>
					<view>
						我要退款(无需退货)
					</view>
					<view>
						没收到货，或与客服协商后申请退款
					</view>
				</view>
			</view>
			<view class="afterSaleOrderDetail-opera-line" @click="toSale(2)">
				<image src="@/static/afterSaleOrderDetail/c1.png" mode=""></image>
				<view>
					<view>
						我要退货退款
					</view>
					<view>
						已收到货，需要退还收到的货物
					</view>
				</view>
			</view> -->
			<view class="afterSaleOrderDetail-opera-line" @click="toSale(3)">
				<image src="@/static/afterSaleOrderDetail/c1.png" mode=""></image>
				<view>
					<view>
						我要换货
					</view>
					<view>
						对货品不满意，与客服协商后换货
					</view>
				</view>
			</view>
		</view>


		<uni-popup ref="refundSelectPop" type="bottom" :safeArea="false">
			<refundOrAfterSale ref="refundOrAfterSaleWrap" :list='info.goods' @close='close' @sure='sure'
				:type='afterSaleType'>
			</refundOrAfterSale>
		</uni-popup>
	</view>
</template>

<script setup>
	import {
		onLoad,
		onUnload,
	} from "@dcloudio/uni-app"
	import {
		computed,
		ref
	} from "vue";
	import {
		navTo
	} from "@/hooks";
	import {
		parseImgurl
	} from '@/utils'
	let info = ref({})
	let storeTypeList = ref([]);
	let refundSelectPop = ref()
	let refundOrAfterSaleWrap = ref()
	const imgUrl = uni.env.IMAGE_URL
	//记录tkType
	let tkType = ref(1)
	const afterSaleType = computed(() => {
		return tkType.value * 1 == 3 ? 3 : 2
	})

	function refresh(item) {
		info.value = item
	}
	onLoad(() => {
		getStoreTypeDicts()
		uni.$once('afterSaleOrderDetailInfo', refresh)
		
	})
	
	//每个商品的订单状态返回 0=下单成功 1=已发货 2=退款中 3=退款成功 4=换货中 5=换货成功
	function getGoodStatusDetail(i) {
		let item = {}
		switch (i.status * 1) {
			case 1:
				item.name = '已发货';
				break;
			case 2:
				item.name = '退款中';
				break;
			case 3:
				item.name = '退款成功';
				break;
			case 4:
				item.name = '换货中';
				break;
			case 5:
				item.name = '换货成功';
				break;
		}
		return item
	}
	
	
	//获取店铺类型字典
	async function getStoreTypeDicts() {
	
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=store_type`);
		storeTypeList.value = data.result || []
	}

	function toSale(t) {
		tkType.value = t
		if (info.value.goods?.length > 1) {
			refundSelectPop.value.open()
			refundOrAfterSaleWrap.value.toEmpty()
			return;
		}
		navTo('/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				let obj = {
					selectedId: [info.value.goods[0].id],
					info: info.value,
					tkType: t
				}
				//如果是修改售后
				if (info.value.isEditAfter) {
					obj.isEditAfter = info.value.isEditAfter
				}
				uni.$emit('applyForAfterSaleInfo', obj)
			}, 300)
		})
	}

	function sure(id) {
		navTo('/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				uni.$emit('applyForAfterSaleInfo', {
					selectedId: id,
					info: info.value,
					tkType: tkType.value
				})
			}, 300)
		})
	}

	function close() {
		refundSelectPop.value.close()
	}
</script>

<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.afterSaleOrderDetail {
		width: 750rpx;
		padding: 24rpx;

		&-wrap {
			padding: 30rpx;
			background: #ffffff;
			border-radius: 16rpx;
			margin-bottom: 30rpx;

			&-operation {
				display: flex;
				align-items: center;
				padding-top: 30rpx;
				justify-content: flex-end;

				>view {
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: #999999;
					height: 60rpx;
					border: 2rpx solid #999999;
					border-radius: 100rpx;
					margin-left: 14rpx;
					padding: 0 20rpx;
				}
			}

			&-bottom {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding-top: 30rpx;
				border-top: 2rpx solid #E2E2E2;

				&-left,
				&-right {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #999999;
				}

				&-right {
					justify-content: flex-end;
				}

				&-left {
					&-lv {
						display: flex;
						align-items: center;
						margin-left: 10rpx;
						color: #E6A600;

						>image {
							width: 30rpx;
							height: 30rpx;
						}
					}
				}
			}

			&-info {
				&-good {
					display: flex;
					align-items: center;
					padding: 20rpx 0;
					position: relative;

					&-left {
						width: 184rpx;
						height: 184rpx;
						margin-right: 14rpx;
					}

					&-right {
						height: 160rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;

						&-name {
							font-size: 28rpx;
							color: #000000;
						}

						&-spec,
						&-other,
						&-goodStatus {
							font-size: 28rpx;
							color: #999999
						}

						&-goodStatus {
							position: absolute;
							z-index: 10;
							right: 0;
							bottom: 30rpx;
						}

						&-other {
							display: flex;
							align-items: center;

							&-right {
								display: flex;
								align-items: center;
								margin-left: 20rpx;
								color: #E6A600;

								>image {
									width: 30rpx;
									height: 30rpx;
									// margin-right: 10rpx;
								}
							}
						}
					}
				}


				&-top {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding-bottom: 20rpx;
					border-bottom: 2rpx solid #E2E2E2;

					&-left {
						display: flex;
						align-items: center;
						color: #333333;
						font-size: 28rpx;

						&-label {
							height: 40rpx;
							line-height: 40rpx;
							color: #22A3FF;
							padding: 0 30rpx;
							background: #d5eeff;
							border-radius: 100px;
							margin: 0 20rpx;
						}

						&-arrow {
							width: 30rpx;
							height: 30rpx;
						}
					}

					&-right {
						font-weight: 600;
						color: #333333;
						font-size: 28rpx;
					}
				}
			}
		}



		&-alWrap {
			width: 100%;
			padding: 20rpx;
			background: #FFFFFF;
			border-radius: 24rpx;

			>view {
				width: 100%;
			}
		}

		&-opera {
			&-line {
				width: 100%;
				height: 150rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-bottom: 2rpx solid #eeeeee;

				>image:nth-child(1) {
					width: 50rpx;
					height: 50rpx;
					margin-right: 20rpx;
				}

				>view:nth-child(2) {
					flex: 1;
					height: 100rpx;
					display: flex;
					flex-direction: column;
					justify-content: center;

					>view:nth-child(1) {
						font-size: 32rpx;
						color: #333333;
						line-height: 42rpx;
						margin-bottom: 16rpx;
					}

					>view:nth-child(2) {
						font-size: 24rpx;
						color: #333333;
						line-height: 22rpx;
					}
				}
			}

			>view:last-child {
				border-bottom: none;
			}
		}

		&-commodityList {
			margin-bottom: 20rpx;

			&-totalPrice {
				height: 80rpx;
				display: flex;
				align-items: center;
				justify-content: flex-end;
				font-size: 26rpx;
				color: #222222;
			}

			&-wrap {
				padding: 24rpx 0;
				border-bottom: 2rpx solid #eeeeee;
				position: relative;

				&-count {
					position: absolute;
					right: 0;
					bottom: 16rpx;
					z-index: 2;
					font-size: 22rpx;
					color: #666666;
				}
			}

			>view:last-child {
				border-bottom: none;
			}
		}
	}
</style>