<template>
	<view class="myPackage">
		<!-- 自定义导航栏 -->
		<uni-nav-bar
			title="我的包裹"
			leftIcon="left"
			@clickLeft="toUpPage"
			statusBar
			fixed
			backgroundColor="#f8f8f8"
			color="#333333"
			class="custom-nav-bar"
			rightIcon=""
		/>
		<!-- 固定的页签区域 -->
		<view class="tabs-container">
			<uv-tabs
				:current='tabSelectIndex'
				:scrollable='false'
				:list="tab"
				lineWidth="45rpx"
				lineHeight='6rpx'
				lineColor="#1A69D1"
				:activeStyle="{
					color: '#1A69D1',
					fontSize:'28rpx',
					fontWeight: 'bold'
				}"
				:inactiveStyle="{
					color: '#999999',
					fontSize:'28rpx'
				}"
				itemStyle="height:64rpx;"
				@click="changeTabSelectIndex">
			</uv-tabs>
		</view>

		<!-- 可滚动内容区域 -->
		<scroll-view
			class="scroll-container"
			scroll-y
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			@refresherrefresh="onRefresh"
			@scrolltolower="onLoadMore">

			<view class='myPackage-container'>
				<!-- 订单列表展示 -->
				<template v-if="tabSelectIndex !== 4">
					<view class="myPackage-container-wrap" v-for="(item,index) in list" :key="index"
						@click="toOrderDetail(item)">
						<view class="myPackage-container-wrap-info">
							<view class="myPackage-container-wrap-info-top">
								<view class="myPackage-container-wrap-info-top-left" @click.stop="navToStoreIndex(item)">
									<view class="store-name">
										{{item.storeName}}
									</view>
									<view class="myPackage-container-wrap-info-top-left-label" v-if="storeTypeList?.length">
										{{storeTypeList.find(i=>item.storeType === i.value)?.title}}
									</view>
									<image class="myPackage-container-wrap-info-top-left-arrow"
										src="@/static/right-arrow-black.png" mode=""></image>
								</view>
								<view class="myPackage-container-wrap-info-top-right">
									{{item.isSender == '1' && item.STATUS == 1 ? '部分发货' : getStatusDetail(item).name}}
								</view>
							</view>

							<view class="myPackage-container-wrap-info-good" v-for="itm in item.goods" :key="itm.id">
								<image v-if="itm.mainPicture" class="myPackage-container-wrap-info-good-left"
									:src="imgUrl + parseImgurl(itm.mainPicture)?.[0]" mode="">
								</image>
								<image v-else-if="itm.goodMainPicture" class="myPackage-container-wrap-info-good-left"
									:src="imgUrl + parseImgurl(itm.goodMainPicture)?.[0]" mode=""> </image>
								<view class="myPackage-container-wrap-info-good-right">
									<view class="myPackage-container-wrap-info-good-right-name">
										{{itm.goodName}}
									</view>
									<view class="myPackage-container-wrap-info-good-right-spec">
										<text class="spec-label">规格:</text>{{itm.specification || itm.goodSpecification}}
									</view>
									<view class="myPackage-container-wrap-info-good-right-other">
										<view class="myPackage-container-wrap-info-good-right-other-left">
											<text class="amount-label">数量:</text>{{itm.amount}}
										</view>
										<view class="myPackage-container-wrap-info-good-right-other-right">
											<image src="@/static/index/i_1.png" mode=""></image>
											{{itm.price}}
										</view>
									</view>
									<view class="myPackage-container-wrap-info-good-right-goodStatus"
										v-if="item.isSender == '1' && item.STATUS == 1">
										{{getGoodStatusDetail(itm).name}}
									</view>
								</view>
							</view>
						</view>
						<view class="myPackage-container-wrap-bottom">
							<view class="myPackage-container-wrap-bottom-left">
								<text class="total-label">合计</text>
								<view class="myPackage-container-wrap-bottom-left-lv">
									<image src="@/static/index/i_1.png" mode=""></image>
									{{ item.actualPayment }}
								</view>
							</view>
							<view class="myPackage-container-wrap-bottom-right">
								<text class="total-count-label">共</text> <text class="total-count-value">{{ item.allNumberUnits }}</text> <text class="total-count-unit">件商品</text>
							</view>
						</view>
						<view class="myPackage-container-wrap-operation">
							<view class="operation-btn confirm-btn" v-if="getStatusDetail(item).type == 6 && !item.pickUpQrCode" @click.stop="takeGoods(item)">
								确认收货
							</view>
							<template v-if="getStatusDetail(item).type == 3">
								<view class="operation-btn cancel-btn" @click.stop="toggleCancelOrderPop(item)">
									取消订单
								</view>
								<view class="operation-btn pay-btn" @click.stop="toPay(item)">
									去付款
								</view>
							</template>
							<view class="operation-btn logistics-btn" @click.stop="toLogistics(item)"
								v-if="(getStatusDetail(item).type == 1 || getStatusDetail(item).type == 6 || getStatusDetail(item).type == 7) && !item.pickUpQrCode">
								查看物流
							</view>
							<view class="operation-btn exchange-btn" @click.stop="showExchangeGoodsPopup(item)"
								v-if="getStatusDetail(item).type == 7 && !item.pickUpQrCode && item.goods && item.goods.length > 0">
								申请换货
							</view>
						</view>
					</view>
				</template>

				<!-- 售后列表展示 -->
				<template v-else>
					<view class="myPackage-container-wrap" v-for="(item,index) in list" :key="index"
						@click="toAfterSaleDetail(item)">
						<view class="myPackage-container-wrap-info">
							<view class="myPackage-container-wrap-info-top">
								<view class="myPackage-container-wrap-info-top-left">
									<view class="store-name">
										{{item.storeManage.storeName}}
									</view>
									<view class="myPackage-container-wrap-info-top-left-label" v-if="storeTypeList?.length">
										{{storeTypeList.find(i=>item.storeManage.storeType === i.value)?.title}}
									</view>
								</view>
								<view class="myPackage-container-wrap-info-top-right" :class="{'status-success': item.status == 4 || item.status == 8, 'status-pending': item.status == 0 || item.status == 1 || item.status == 2 || item.status == 3, 'status-closed': item.status == 5 || item.status == 6 || item.status == 7}">
									{{getAfterSaleStatusDetail(item).name}}
								</view>
							</view>

							<view class="myPackage-container-wrap-info-good">
								<image v-if="item.mainPicture" class="myPackage-container-wrap-info-good-left"
									:src="imgUrl + parseImgurl(item.mainPicture)?.[0]" mode="">
								</image>
								<image v-else-if="item.goodMainPicture" class="myPackage-container-wrap-info-good-left"
									:src="imgUrl + parseImgurl(item.goodMainPicture)?.[0]" mode=""> </image>
								<view class="myPackage-container-wrap-info-good-right">
									<view class="myPackage-container-wrap-info-good-right-name">
										{{item.goodName}}
									</view>
									<view class="myPackage-container-wrap-info-good-right-spec">
										<text class="spec-label">规格:</text>{{item.specification || item.goodSpecification}}
									</view>
									<view class="myPackage-container-wrap-info-good-right-other">
										<view class="refund-label" v-if="item.refundType == '2'">换货数量:</view>
										<view class="refund-label" v-else>退款金额:</view>
										<view class="myPackage-container-wrap-info-good-right-other-right" v-if="item.refundType == '2'">
											{{item.refundAmount}}
										</view>
										<view class="myPackage-container-wrap-info-good-right-other-right" v-else>
											<image src="@/static/index/i_1.png" mode=""></image>
											{{item.refundPrice}}
										</view>
									</view>
								</view>
							</view>
						</view>
						<view class="myPackage-container-wrap-operation">
							<view class="operation-btn delete-btn" @click.stop="deleteAfterSaleOrder(item)" v-if="item.status == 5 || item.status == 6 || item.status == 7">
								删除
							</view>
							<view class="operation-btn cancel-btn" @click.stop="dealAfterSaleOrder(item)"
								v-if="item.status == 0">
								撤销申请
							</view>
							<view class="operation-btn detail-btn" @click.stop="toAfterSaleDetail(item)">
								查看详情
							</view>
						</view>
					</view>
				</template>

				<empty v-if="!list?.length"></empty>

				<!-- 底部加载状态 -->
				<view class="load-more" v-if="list?.length">
					<text v-if="isLoadingMore">加载中...</text>
					<text v-else-if="isNoMore">没有更多数据了</text>
				</view>
			</view>
		</scroll-view>

		<!-- 提示弹窗 -->
		<uni-popup ref="alertDialog" type="dialog">
			<uni-popup-dialog type='info' mode="base" :title='popupMsg.title' :content="popupMsg.content"
				:duration="2000" :before-close="false" @confirm="confirm" :confirmText="popupMsg.confirmText"
				:showClose="popupMsg.showClose">
			</uni-popup-dialog>
		</uni-popup>

		<!-- 取消订单 退款原因 -->
		<uni-popup ref="cancelOrderPop" type="bottom" :safeArea="false">
			<cancelOrder @cancelSuccess='cancelSuccess' @close='closeCancelOrderPop' :type='cancelOrderPopType'>
			</cancelOrder>
		</uni-popup>

		<!-- 商品选择弹窗 -->
		<exchange-goods-popup
			ref="exchangeGoodsPopup"
			:order-info="currentOrder"
			:goods-list="currentOrderGoods"
			@confirm="handleExchangeGoodsConfirm"
			@close="handleExchangeGoodsClose"
		/>
	</view>
</template>

<script setup>
	import {
		computed,
		ref,
		nextTick,
		onMounted
	} from "vue";
	import {
		onLoad,
		onUnload,
		onPullDownRefresh,
		onReachBottom
	} from "@dcloudio/uni-app"
	import {
		cashier
	} from '@/hooks/cashier.js'
	import {
		listGet,
		navTo,
		redTo,
		navToStoreIndex,
		toUpPage
	} from "@/hooks";
	import {
		clone,
		getSafeBottom,
		parseObjToPath,
		parseImgurl
	} from "@/utils";
	import ExchangeGoodsPopup from '@/components/exchangeGoodsPopup/exchangeGoodsPopup.vue';

	// 滚动相关状态
	const isRefreshing = ref(false);
	const isLoadingMore = ref(false);
	const isNoMore = ref(false);
	const page = ref(1);
	const pageSize = ref(10);

	// 页面高度计算
	const screenHeight = ref(0);
	const navBarHeight = ref(0);
	const tabsHeight = ref(0);

	const {
		payResult
	} = cashier()
	const imgUrl = uni.env.IMAGE_URL
	const statusList = ['', 3, 1, 2, 'afterSale'] //全部 已收货 待发货 待收货 售后
	const tab = [{
		name: '全部',
		status: ''
	}, {
		name: '已收货',
		status: '3'
	}, {
		name: '待发货',
		status: '1'
	}, {
		name: '待收货',
		status: '2'
	}, {
		name: '售后',
		status: 'afterSale'
	}]
	let refundSuccess = ref();
	let alertDialog = ref();
	let alertDialogSec = ref()
	let cancelOrderPop = ref();
	let refundSelectPop = ref();
	let storeTypeList = ref([]);
	let refundOrAfterSaleWrap = ref()
	let tabSelectIndex = ref(-1);
	//取消订单和退款的点击记录item
	let cancelOrderItem = ref({})
	//1取消订单 2退款
	let cancelOrderPopType = ref(1)
	//退款原因点击记录item
	let refundedReason = ref({});
	//确认收货点击记录item
	let takeGoosItem = ref({})
	let options = ref({})
	//提示弹窗的信息
	let popupMsg = ref({
		title: '',
		content: '',
		confirmText: '',
		showClose: true
	})
	// 商品选择弹窗相关
	const exchangeGoodsPopup = ref(null);
	const currentOrder = ref({});
	const currentOrderGoods = ref([]);
	//退款发起成功弹窗的信息展示
	let orderRefundListDtosShow = ref([]);
	//复制
	function copy(data) {
		uni.setClipboardData({
			data
		});
	}

	// 计算屏幕和组件高度
	function calculateHeight() {
		const sysInfo = uni.getSystemInfoSync();
		screenHeight.value = sysInfo.windowHeight;

		// 获取导航栏高度
		const navBarQuery = uni.createSelectorQuery();
		navBarQuery.select('.uni-nav-bar').boundingClientRect(data => {
			if (data) {
				navBarHeight.value = data.height;
				console.log('导航栏高度:', data.height);
			}
		}).exec();

		// 获取页签高度
		const tabsQuery = uni.createSelectorQuery();
		tabsQuery.select('.tabs-container').boundingClientRect(data => {
			if (data) {
				tabsHeight.value = data.height;
				console.log('标签页高度:', data.height);
			}
		}).exec();
	}

	// 下拉刷新处理
	async function onRefresh() {
		isRefreshing.value = true;
		page.value = 1;
		isNoMore.value = false;

		try {
			await refresh();
		} catch (err) {
			console.error('刷新数据失败', err);
			// 显示错误提示
			uni.showToast({
				title: '刷新失败，请稍后再试',
				icon: 'none',
				duration: 1500
			});
		} finally {
			isRefreshing.value = false;
			// 确保停止下拉刷新动画
			uni.stopPullDownRefresh();
		}
	}

	// 上拉加载更多
	async function onLoadMore() {
		console.log('触发加载更多', { isLoadingMore: isLoadingMore.value, isNoMore: isNoMore.value });

		// 如果正在加载或已经没有更多数据，直接返回
		if (isLoadingMore.value || isNoMore.value) {
			console.log('跳过加载更多：正在加载或已无更多数据');
			return;
		}

		// 预先判断是否可能有更多数据
		// 对于订单列表，检查当前页的数据量是否小于页面大小
		const currentList = tabSelectIndex.value === 4 ? afterSaleList.value : orderList.value;

		// 如果当前页数据量小于页面大小，说明已经没有更多数据了
		if (currentList && currentList.length > 0 && currentList.length < pageSize.value) {
			console.log('当前页数据量小于页面大小，无需请求下一页');
			isNoMore.value = true;
			return;
		}

		// 设置加载状态
		isLoadingMore.value = true;

		// 保存当前页码，以便在加载失败时恢复
		const currentPage = page.value;
		page.value++;
		console.log('加载更多：页码增加到', page.value);

		try {
			if (tabSelectIndex.value === 4) {
				await loadMoreAfterSale();
			} else {
				await loadMoreOrder();
			}
		} catch (err) {
			console.error('加载更多数据失败', err);
			// 恢复页码
			page.value = currentPage;
			// 显示错误提示
			uni.showToast({
				title: '加载失败，请稍后再试',
				icon: 'none',
				duration: 1500
			});
		} finally {
			// 延迟重置加载状态，防止快速滚动时重复触发
			setTimeout(() => {
				isLoadingMore.value = false;
			}, 300);
		}
	}

	// 加载更多订单
	async function loadMoreOrder() {
		console.log('加载更多订单数据，当前页码:', page.value);

		try {
			// 保存当前列表数据的副本，以防加载失败时恢复
			const currentList = [...orderList.value];

			const { data } = await uni.http.get(uni.api.orderList, {
				params: {
					status: statusList[tabSelectIndex.value],
					pageNo: page.value,
					pageSize: pageSize.value
				}
			});

			const newRecords = data.result.records || [];
			console.log('获取到新订单数据:', newRecords.length, '条');

			// 如果没有新数据，标记为没有更多数据，但保留现有数据
			if (newRecords.length === 0) {
				isNoMore.value = true;
				console.log('没有更多订单数据，保留现有数据');
				return;
			}

			// 追加新数据到列表
			orderList.value = [...currentList, ...newRecords];
			console.log('订单列表更新后长度:', orderList.value.length);

			// 判断是否已加载全部数据
			if (newRecords.length < pageSize.value) {
				isNoMore.value = true;
				console.log('已加载全部订单数据');
			}
		} catch (err) {
			console.error('加载更多订单数据失败', err);
			// 恢复页码，但不清空已有数据
			page.value--;
			// 显示错误提示
			uni.showToast({
				title: '加载失败，请稍后再试',
				icon: 'none',
				duration: 1500
			});
		} finally {
			// 注意：isLoadingMore 状态在 onLoadMore 函数中统一处理
		}
	}

	// 加载更多售后
	async function loadMoreAfterSale() {
		console.log('加载更多售后数据，当前页码:', page.value);

		try {
			// 保存当前列表数据的副本，以防加载失败时恢复
			const currentList = [...afterSaleList.value];

			const { data } = await uni.http.get(uni.api.refundList, {
				params: {
					pageNo: page.value,
					pageSize: pageSize.value
				}
			});

			const newRecords = data.result.records || [];
			console.log('获取到新售后数据:', newRecords.length, '条');

			// 如果没有新数据，标记为没有更多数据，但保留现有数据
			if (newRecords.length === 0) {
				isNoMore.value = true;
				console.log('没有更多售后数据，保留现有数据');
				return;
			}

			// 追加新数据到列表
			afterSaleList.value = [...currentList, ...newRecords];
			console.log('售后列表更新后长度:', afterSaleList.value.length);

			// 判断是否已加载全部数据
			if (newRecords.length < pageSize.value) {
				isNoMore.value = true;
				console.log('已加载全部售后数据');
			}
		} catch (err) {
			console.error('加载更多售后数据失败', err);
			// 恢复页码，但不清空已有数据
			page.value--;
			// 显示错误提示
			uni.showToast({
				title: '加载失败，请稍后再试',
				icon: 'none',
				duration: 1500
			});
		} finally {
			// 注意：isLoadingMore 状态在 onLoadMore 函数中统一处理
		}
	}

	//获取店铺类型字典
	async function getStoreTypeDicts() {

		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=store_type`);
		storeTypeList.value = data.result || []
	}
	//确认收货
	function takeGoods(item) {
		takeGoosItem.value = item
		popupMsg.value = {
			title: '确认收货',
			content: '请您收到货后再点击确认，否则可能钱货两空！确定收到货了吗？',
			confirmText: '确定',
			showClose: true
		}
		alertDialog.value.open();
	}
	//提货码
	function takeGoodsNum(item) {
		popupMsg.value = {
			title: '您的商品已准备完成',
			// content: '401387',
			confirmText: '好的',
			showClose: false,
			pickUpQrCode: item.pickUpQrCode,
			pickUpVerificationStatus: item.pickUpVerificationStatus,
			pickUpQrAddr: item.pickUpQrAddr
		}
		alertDialogSec.value.open();
	}
	//去退款列表 - 修改为直接跳转到包裹页面的售后页签
	function toTkDetail() {
		closeRefundSuccessPop()
		// 直接切换到当前页面的售后页签
		changeTabSelectIndex({index: 4})
	}
	//催发货
	function showUrgeDelivery() {
		popupMsg.value = {
			title: '提醒发货成功',
			content: '已提醒商家尽快发货，请耐心等待~',
			confirmText: '好的',
			showClose: false
		}
		alertDialog.value.open();
	}

	//仅退款
	async function toRefund(refundCommodityId) {
		if (refundCommodityId.length <= 0) {
			uni.showToast({
				title: '请选择退款商品',
				icon: "none"
			})
			return;
		}
		closeRefundSelectPop()
		navTo('/packageOrder/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				let obj = {
					selectedId: refundCommodityId,
					info: cancelOrderItem.value,
					tkType: 1
				}
				uni.$emit('applyForAfterSaleInfo', obj)
			}, 300)
		})
	}
	//关闭取消订单弹窗
	function closeCancelOrderPop() {
		cancelOrderPop.value.close()
	}
	//关闭选择退款商品弹窗
	function closeRefundSelectPop() {
		refundSelectPop.value.close()
	}
	//关闭退款成功弹窗
	function closeRefundSuccessPop() {
		refundSuccess.value.close()
	}
	//每个商品的订单状态返回 0=下单成功 1=已发货 2=退款中 3=退款成功 4=换货中 5=换货成功
	function getGoodStatusDetail(i) {
		let item = {}
		// 使用数字转换确保正确比较
		// 注意：status是字符串类型，需要转换为数字进行比较
		const statusNum = parseInt(i.status);
		// 判断是否为换货场景（如果有refundType字段）
		const isExchange = i.refundType === '2';

		switch (statusNum) {
			case 1:
				item.name = '已发货';
				break;
			case 2:
				// 根据是否为换货场景显示不同文案
				item.name = isExchange ? '处理中' : '退款中';
				break;
			case 3:
				// 根据是否为换货场景显示不同文案
				item.name = isExchange ? '换货成功' : '退款成功';
				break;
			case 4:
				item.name = '换货中';
				break;
			case 5:
				item.name = '换货成功';
				break;
		}
		return item
	}
	//每条订单的状态返回
	function getStatusDetail(i) {
		let item = {}
		switch (i.STATUS * 1) {
			case 0:
				item.name = '待付款';
				item.type = '3';
				break;
			case 1:
				item.name = '待发货';
				item.type = '5';
				break;
			case 2:
				item.name = '待收货';
				item.type = '6';
				break;
			case 3:
				item.name = '已收货';
				item.type = '7';
				break;
			case 4:
				item.name = '交易关闭';
				item.type = '2';
				item.reason = '1';
				break;
			case 5:
				item.name = '交易完成';
				item.type = '7';
				break;
		}
		return item
	}
	//查看物流
	function toLogistics(item) {
		let info = {
			orderListId: item.id,
			isPlatform: item.isPlatform
		}
		navTo(`/packageOrder/pages/logistics/logistics${parseObjToPath(info)}`)
	}
	//去订单详情
	function toOrderDetail(item) {
		let info = {
			id: item.id,
			isPlatform: item.isPlatform,
			isEvaluate: item.isEvaluate,
			name: getStatusDetail(item).name,
			state: getStatusDetail(item).type,
			reason: item.reason || 0
		}
		navTo(`/packageOrder/pages/orderDetail/orderDetail${parseObjToPath(info)}`)
	}
	//提示弹窗的确定
	async function confirm() {
		if (popupMsg.value.title == '确认收货') {
			uni.showLoading({
				mask: true
			})
			// 发送确认收货请求
			await uni.http.post(uni.api.affirmOrderDelivery, {
				id: takeGoosItem.value.id,
				isPlatform: takeGoosItem.value.isPlatform
			})
			uni.showToast({
				title: '收货成功~',
				icon: "none"
			})
			setTimeout(() => {
				refresh()
			}, 500)
		}
	}
	//tab选择
	function changeTabSelectIndex(e) {
		const index = e.index
		console.log('切换页签', index)

		// 重置分页数据
		page.value = 1;
		isNoMore.value = false;

		// 如果是售后页签，则加载售后数据
		if (index === 4) {
			// 先加载售后数据，再切换页签
			uni.showLoading({
				title: '加载中...',
				mask: true
			})

			uni.http.get(uni.api.refundList).then(({data}) => {
				afterSaleList.value = data.result.records || []
				console.log('售后数据加载完成', afterSaleList.value)
				// 数据加载完成后再切换页签
				tabSelectIndex.value = index
				uni.hideLoading()
			}).catch(err => {
				console.error('加载售后数据失败', err)
				afterSaleList.value = []
				tabSelectIndex.value = index
				uni.hideLoading()
			})
		} else {
			// 其他页签直接切换
			tabSelectIndex.value = index
			// 加载当前页签的数据
			refresh();
		}
	}
	//去退款售后
	function toAfterSale(item) {
		let resItem = clone(item);
		if ('goods' in resItem) {
			resItem.goods = resItem.goods.filter(item => (item.ongoingRefundCount || 0) < item.amount)
		}
		if (resItem.goods.length <= 0) {
			uni.showToast({
				title: '暂无可申请售后的商品~',
				icon: 'none'
			})
			return;
		}
		navTo('/packageOrder/pages/afterSaleOrderDetail/afterSaleOrderDetail', () => {
			setTimeout(() => {
				uni.$emit('afterSaleOrderDetailInfo', resItem)
			}, 300)
		})
	}
	//单个商品退款售后
	function toAfterSaleSingle(item, idx) {
		navTo('/packageOrder/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				let obj = {
					selectedId: [item.goods[idx].id],
					info: item,
					tkType: 3
				}
				uni.$emit('applyForAfterSaleInfo', obj)
			}, 300)
		})
	}

	// 显示换货商品选择弹窗
	function showExchangeGoodsPopup(item) {
		if (!item.goods || item.goods.length === 0) {
			uni.showToast({
				title: '暂无可申请换货的商品',
				icon: 'none'
			});
			return;
		}

		// 过滤掉已经申请过售后的商品
		const availableGoods = item.goods.filter(good => (good.ongoingRefundCount || 0) < good.amount);

		if (availableGoods.length === 0) {
			uni.showToast({
				title: '暂无可申请换货的商品',
				icon: 'none'
			});
			return;
		}

		// 先设置当前订单和商品数据
		currentOrder.value = item;
		currentOrderGoods.value = availableGoods;

		// 在打开弹窗前先隐藏页签高亮
		hideTabHighlight();

		// 使用nextTick确保DOM更新后再打开弹窗
		nextTick(() => {
			exchangeGoodsPopup.value.open();
		});
	}

	// 处理商品选择确认
	function handleExchangeGoodsConfirm(selectedGoods, selectedIndex) {
		navTo('/packageOrder/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				let obj = {
					selectedId: [selectedGoods.id],
					info: currentOrder.value,
					tkType: 3
				};
				uni.$emit('applyForAfterSaleInfo', obj);
			}, 300);
		});
	}

	// 处理商品选择弹窗关闭
	function handleExchangeGoodsClose() {
		// 恢复页签高亮效果
		showTabHighlight();
	}
	//评价
	function toEvaluate(item) {
		let info = {
			id: item.id,
			isPlatform: item.isPlatform
		}
		navTo(`/pages/evaluate/evaluate${parseObjToPath(info)}`)
	}
	//取消订单弹窗显示
	function toggleCancelOrderPop(item) {
		cancelOrderPopType.value = 1
		cancelOrderItem.value = item
		cancelOrderPop.value.open()
	}
	//订单退款
	function toggleRefundOrderPop(item) {
		let resItem = clone(item);
		if ('goods' in resItem) {
			resItem.goods = resItem.goods.filter(item => (item.ongoingRefundCount || 0) < item.amount)
		}
		if (resItem.goods.length <= 0) {
			uni.showToast({
				title: '暂无可申请退款的商品~',
				icon: 'none'
			})
			return;
		}
		cancelOrderPopType.value = 2
		cancelOrderItem.value = resItem
		// cancelOrderPop.value.open()
		if (item.goods?.length > 1) {
			refundSelectPop.value.open()
			refundOrAfterSaleWrap.value.toEmpty()
			return;
		}
		navTo('/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				let obj = {
					selectedId: [item.goods[0].id],
					info: item,
					tkType: 1
				}
				uni.$emit('applyForAfterSaleInfo', obj)
			}, 300)
		})
	}
	//待支付订单支付
	function toPay(item) {
		let info = {
			id: item.id,
			isPlatform: item.isPlatform,
			payClassic: 4
		};
		payResult(info, true, () => {
			refresh()
		})
	}
	//取消成功回调
	function cancelSuccess(selectedReasonItem) {
		if (cancelOrderPopType.value == 1) {
			uni.showModal({
				title: '取消订单',
				content: '您确定要取消订单吗?',
				success: async function(res) {
					if (res.confirm) {
						uni.showLoading({
							mask: true
						})
						// 发送取消订单请求
						await uni.http.get(uni.api.abrogateOrder, {
							params: {
								isPlatform: cancelOrderItem.value.isPlatform, //店铺或者平台
								id: cancelOrderItem.value.id, //订单id
								value: selectedReasonItem.value, //取消订单编号
							}
						})
						cancelOrderPop.value.close()
						refresh()
						uni.showToast({
							title: '取消成功~',
							icon: 'none'
						})
					}
				}
			})
		}
		if (cancelOrderPopType.value == 2) {
			//记录退款item
			refundedReason.value = selectedReasonItem
			cancelOrderPop.value.close()
			//只有一件商品以及是自提则整单退款
			if (cancelOrderItem.value.goods.length == 1 || cancelOrderItem.value.pickUpQrCode) {
				let resultId = cancelOrderItem.value.goods.map(i => i.id)
				toRefund(resultId);
				return;
			}
			//选择退款商品
			refundSelectPop.value.open()
			refundOrAfterSaleWrap.value.toEmpty()
		}
	}
	// 售后列表数据
	let afterSaleList = ref([])

	// 售后状态
	function getAfterSaleStatusDetail(i) {
		let item = {}
		// 使用数字转换确保正确比较
		// 注意：status是字符串类型，需要转换为数字进行比较
		const statusNum = parseInt(i.status);
		// 判断是否为换货场景
		const isExchange = i.refundType === '2';

		switch (statusNum) {
			case 0:
				item.name = '待处理';
				break;
			case 1:
				item.name = '待买家退回';
				break;
			case 2:
				item.name = '换货中';
				break;
			case 3:
				// 根据是否为换货场景显示不同文案
				item.name = isExchange ? '处理中' : '退款中';
				break;
			case 4:
				// 根据是否为换货场景显示不同文案
				item.name = isExchange ? '换货成功' : '退款成功';
				break;
			case 5:
				// 根据是否为换货场景显示不同文案
				item.name = isExchange ? '换货已拒绝' : '退款已拒绝';
				break;
			case 6:
				// 根据是否为换货场景显示不同文案
				item.name = isExchange ? '换货关闭' : '退款关闭';
				break;
			case 7:
				item.name = '换货关闭';
				break;
			case 8:
				item.name = '换货完成';
				break;
		}
		return item
	}

	// 跳转到售后详情
	function toAfterSaleDetail(item) {
		navTo(`/packageOrder/pages/afterSaleDetail/afterSaleDetail?id=${item.id}`)
	}

	// 删除售后申请
	function deleteAfterSaleOrder(item) {
		uni.showModal({
			title: '确认删除申请吗',
			content: '删除后将无法恢复该申请',
			async success(res) {
				if (res.confirm) {
					await uni.http.post(uni.api.refundDelete, {
						id: item.id
					})
					uni.showToast({
						title: '删除成功~',
						icon: 'none'
					})
					setTimeout(() => {
						refresh()
					}, 500)
				}
			}
		})
	}

	// 撤销售后申请
	function dealAfterSaleOrder(item) {
		uni.showModal({
			title: '确认撤销申请吗',
			content: '撤销后将无法恢复该申请',
			async success(res) {
				if (res.confirm) {
					await uni.http.post(uni.api.refundUndo, {
						id: item.id
					})
					uni.showToast({
						title: '撤销成功~',
						icon: 'none'
					})
					setTimeout(() => {
						refresh()
					}, 500)
				}
			}
		})
	}

	// 请求参数
	let reqOptions = computed(() => {
		// 如果是售后页签，不需要设置参数，因为不会调用订单列表接口
		if (tabSelectIndex.value === 4) {
			return {}
		}
		return {
			status: statusList[tabSelectIndex.value],
			pageNo: page.value,
			pageSize: pageSize.value
		}
	})

	// 订单列表数据
	const {
		mode,
		list: orderList,
		refresh: orderRefresh
	} = listGet({
		options: reqOptions,
		apiUrl: uni.api.orderList,
		isReqTypeReq: false,
		pdIsLogin: true,
		// 添加自定义刷新函数，在售后页签不调用订单列表接口
		refresh: async (refreshType = 1) => {
			console.log('刷新订单列表，类型:', refreshType, '当前页签:', tabSelectIndex.value);

			// 如果是售后页签，不调用订单列表接口
			if (tabSelectIndex.value === 4) {
				console.log('当前是售后页签，跳过订单列表刷新');
				return
			}

			// 其他页签正常调用订单列表接口
			uni.showLoading({
				title: '加载中...',
				mask: true
			})

			try {
				// 保存当前列表数据，以便在加载失败时恢复
				const currentList = [...orderList.value];

				const { data } = await uni.http.get(uni.api.orderList, {
					params: {
						status: statusList[tabSelectIndex.value],
						pageNo: page.value,
						pageSize: pageSize.value
					}
				})

				const records = data.result.records || [];
				console.log('获取到订单数据:', records.length, '条');

				// 如果是首次加载或刷新，直接替换列表数据
				if (page.value === 1) {
					orderList.value = records;
					console.log('首次加载或刷新，替换订单列表数据');
				} else {
					// 否则追加数据
					orderList.value = [...currentList, ...records];
					console.log('加载更多，追加订单列表数据');
				}

				// 判断是否已加载全部数据
				if (records.length < pageSize.value) {
					isNoMore.value = true;
					console.log('已加载全部订单数据');
				} else {
					isNoMore.value = false;
					console.log('还有更多订单数据可加载');
				}
			} catch (err) {
				console.error('加载订单数据失败', err);

				// 只有在首次加载时才清空列表，否则保留现有数据
				if (page.value === 1) {
					orderList.value = [];
					console.log('首次加载失败，清空订单列表');
				} else {
					console.log('加载更多失败，保留现有数据');
				}

				// 显示错误提示
				uni.showToast({
					title: '加载失败，请稍后再试',
					icon: 'none',
					duration: 1500
				});
			} finally {
				uni.hideLoading();
				uni.stopPullDownRefresh();
			}
		}
	})

	// 刷新售后列表数据
	async function refreshAfterSaleList() {
		console.log('刷新售后数据，当前页码:', page.value);
		uni.showLoading({
			title: '加载中...',
			mask: true
		})

		try {
			// 保存当前列表数据，以便在加载失败时恢复
			const currentList = [...afterSaleList.value];

			const { data } = await uni.http.get(uni.api.refundList, {
				params: {
					pageNo: page.value,
					pageSize: pageSize.value
				}
			})

			const records = data.result.records || [];
			console.log('获取到售后数据:', records.length, '条');

			// 如果是首次加载或刷新，直接替换列表数据
			if (page.value === 1) {
				afterSaleList.value = records;
				console.log('首次加载或刷新，替换售后列表数据');
			} else {
				// 否则追加数据
				afterSaleList.value = [...currentList, ...records];
				console.log('加载更多，追加售后列表数据');
			}

			// 判断是否已加载全部数据
			if (records.length < pageSize.value) {
				isNoMore.value = true;
				console.log('已加载全部售后数据');
			} else {
				isNoMore.value = false;
				console.log('还有更多售后数据可加载');
			}
		} catch (err) {
			console.error('刷新售后数据失败', err);

			// 只有在首次加载时才清空列表，否则保留现有数据
			if (page.value === 1) {
				afterSaleList.value = [];
				console.log('首次加载失败，清空售后列表');
			} else {
				console.log('加载更多失败，保留现有数据');
			}

			// 显示错误提示
			uni.showToast({
				title: '加载失败，请稍后再试',
				icon: 'none',
				duration: 1500
			});
		} finally {
			uni.hideLoading();
			// 确保停止下拉刷新动画
			uni.stopPullDownRefresh();
		}
	}

	// 根据当前选中的页签返回对应的列表数据
	const list = computed(() => {
		if (tabSelectIndex.value === 4) { // 售后页签
			return afterSaleList.value
		} else { // 订单页签
			return orderList.value
		}
	})

	// 刷新当前页签的数据
	const refresh = () => {
		// 重置分页
		page.value = 1;
		isNoMore.value = false;

		console.log('当前页签索引', tabSelectIndex.value)
		if (tabSelectIndex.value === 4) { // 售后页签
			return refreshAfterSaleList()
		} else { // 订单页签
			return orderRefresh()
		}
	}

	// 弹窗状态管理
	const isPopupOpen = ref(false);

	// 处理弹窗打开时隐藏页签高亮效果
	function hideTabHighlight() {
		isPopupOpen.value = true;
		// 添加类名到页面，通过CSS控制页签样式
		setTimeout(() => {
			// 使用uni-app的选择器API
			const query = uni.createSelectorQuery();
			query.select('.tabs-container').boundingClientRect(data => {
				if (data) {
					// 直接修改页签容器的样式
					uni.pageScrollTo({
						duration: 0,
						scrollTop: 0
					});
					// 通过自定义类控制样式
					uni.$emit('add-page-class', { className: 'popup-opened' });
				}
			}).exec();
		}, 10);
	}

	// 处理弹窗关闭时恢复页签高亮效果
	function showTabHighlight() {
		isPopupOpen.value = false;
		// 移除类名，恢复页签样式
		setTimeout(() => {
			// 使用uni-app的选择器API
			const query = uni.createSelectorQuery();
			query.select('.tabs-container').boundingClientRect(data => {
				if (data) {
					// 恢复页签容器的样式
					uni.$emit('remove-page-class', { className: 'popup-opened' });
				}
			}).exec();
		}, 10);
	}

	onLoad((op) => {
		options.value = op
		console.log('页面加载参数', op)

		// 如果有tabIndex参数，直接切换到对应页签
		if (op.tabIndex) {
			// 将字符串转换为数字
			let index = Number(op.tabIndex)
			console.log('切换到页签', index)

			// 如果是售后页签，需要先加载售后数据
			if (index === 4) {
				// 先加载售后数据，再切换页签
				uni.showLoading({
					title: '加载中...',
					mask: true
				})

				uni.http.get(uni.api.refundList, {
					params: {
						pageNo: 1,
						pageSize: pageSize.value
					}
				}).then(({data}) => {
					afterSaleList.value = data.result.records || []
					console.log('售后数据加载完成', afterSaleList.value)
					// 数据加载完成后再切换页签
					tabSelectIndex.value = index
					uni.hideLoading()
				}).catch(err => {
					console.error('加载售后数据失败', err)
					afterSaleList.value = []
					tabSelectIndex.value = index
					uni.hideLoading()
				})
			} else {
				changeTabSelectIndex({
					index
				})
			}
		} else if (op.status) {
			// 从我的点入时
			statusList.forEach((item, index) => {
				if (op.status == item) {
					console.log(index)
					changeTabSelectIndex({
						index
					})
				}
			})
		} else {
			changeTabSelectIndex({
				index: 0
			})
			refresh()
		}

		getStoreTypeDicts()
		uni.$on('refreshMyOrder', refresh)

		// 监听弹窗事件
		uni.$on('popup-opened', (data) => {
			if (data.type === 'exchangeGoods') {
				// 隐藏页签高亮效果
				hideTabHighlight();
			}
		});

		uni.$on('popup-closed', (data) => {
			if (data.type === 'exchangeGoods') {
				// 恢复页签高亮效果
				showTabHighlight();
			}
		});

		// 监听添加/移除类名事件
		uni.$on('add-page-class', (data) => {
			if (data.className) {
				// 使用uni-app的选择器API添加类名
				const query = uni.createSelectorQuery();
				query.select('.tabs-container').boundingClientRect(res => {
					if (res) {
						// 直接设置样式
						const style = res.style || {};
						style.zIndex = '-1';
					}
				}).exec();
			}
		});

		uni.$on('remove-page-class', (data) => {
			if (data.className) {
				// 使用uni-app的选择器API移除类名
				const query = uni.createSelectorQuery();
				query.select('.tabs-container').boundingClientRect(res => {
					if (res) {
						// 直接设置样式
						const style = res.style || {};
						style.zIndex = 'auto';
					}
				}).exec();
			}
		});

		// 计算高度
		setTimeout(calculateHeight, 100);
	})

	onMounted(() => {
		// 页面挂载后计算高度
		setTimeout(calculateHeight, 300);
	})

	onUnload(() => {
		uni.$off('refreshMyOrder', refresh)
		uni.$off('popup-opened')
		uni.$off('popup-closed')
		uni.$off('add-page-class')
		uni.$off('remove-page-class')
	})
</script>



<style lang="scss">
	page {
		background-color: #f8f8f8;
		height: 100%;
		overflow: hidden;
	}

	/* 弹窗打开时隐藏页签高亮效果 */
	.popup-opened .tabs-container {
		z-index: -1 !important;
	}

	/* 确保弹窗在页签上方显示 */
	.uni-popup {
		z-index: 999 !important;
	}

	/* 自定义导航栏样式 */
	.custom-nav-bar {
		margin-bottom: 0 !important; /* 移除导航栏底部边距 */
	}

	.myPackage {
		height: 100vh;
		display: flex;
		flex-direction: column;

		.tabs-container {
			padding-top: 0; /* 完全移除顶部间距 */
			margin-top: 0; /* 确保没有上边距 */
			background-color: #f8f8f8;
			position: relative;
			z-index: 100;
			padding-bottom: 16rpx;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
		}

		.scroll-container {
			flex: 1;
			height: 0;
			overflow: hidden;
			padding-top: 5rpx; /* 添加一点上内边距 */
		}

		.tabSelected {
			color: #1A69D1 !important;
			border-bottom: 2rpx solid #1A69D1;
		}

		&-container {
			padding: 20rpx 30rpx;
			padding-top: 20rpx; /* 增加顶部内边距，从5rpx改为20rpx */

			&-wrap {
				padding: 30rpx;
				background: #ffffff;
				border-radius: 16rpx;
				margin-bottom: 30rpx;
				margin-top: 5rpx; /* 添加一点上边距 */
				box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.03);

				&-operation {
					display: flex;
					align-items: center;
					padding-top: 30rpx;
					justify-content: flex-end;

					>view {
						display: flex;
						align-items: center;
						justify-content: center;
						font-size: 28rpx;
						color: #999999;
						height: 60rpx;
						border: 2rpx solid #999999;
						border-radius: 100rpx;
						margin-left: 14rpx;
						padding: 0 20rpx;
						transition: all 0.3s;
					}

					.operation-btn {
						&.confirm-btn {
							color: #1A69D1;
							border-color: #1A69D1;
							font-weight: 500;
						}

						&.pay-btn {
							color: #ffffff;
							background-color: #1A69D1;
							border-color: #1A69D1;
							font-weight: 500;
						}

						&.cancel-btn {
							color: #999999;
							border-color: #999999;
						}

						&.logistics-btn {
							color: #1A69D1;
							border-color: #1A69D1;
						}

						&.exchange-btn {
							color: #1A69D1;
							border-color: #1A69D1;
						}

						&.delete-btn {
							color: #FF5151;
							border-color: #FF5151;
						}

						&.detail-btn {
							color: #1A69D1;
							border-color: #1A69D1;
						}
					}
				}

				&-bottom {
					display: flex;
					align-items: center;
					justify-content: space-between;
					padding-top: 30rpx;
					border-top: 2rpx solid #E2E2E2;

					&-left,
					&-right {
						display: flex;
						align-items: center;
						font-size: 28rpx;
						color: #999999;
					}

					.total-label {
						margin-right: 6rpx;
					}

					.total-count-value {
						color: #333333;
						margin: 0 6rpx;
						font-weight: 500;
					}

					&-right {
						justify-content: flex-end;
					}

					&-left {
						&-lv {
							display: flex;
							align-items: center;
							margin-left: 10rpx;
							color: #E6A600;
							font-weight: 500;

							>image {
								width: 30rpx;
								height: 30rpx;
								margin-right: 4rpx;
							}
						}
					}
				}

				&-info {
					&-good {
						display: flex;
						align-items: center;
						padding: 20rpx 0;
						position: relative;

						&-left {
							width: 184rpx;
							height: 184rpx;
							margin-right: 20rpx;
							border-radius: 8rpx;
						}

						&-right {
							height: 160rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							flex: 1;

							&-name {
								font-size: 28rpx;
								color: #333333;
								font-weight: 500;
								line-height: 1.4;
								overflow: hidden;
								text-overflow: ellipsis;
								display: -webkit-box;
								-webkit-line-clamp: 2;
								line-clamp: 2;
								-webkit-box-orient: vertical;
							}

							&-spec,
							&-other,
							&-goodStatus {
								font-size: 28rpx;
								color: #999999;
							}

							.spec-label, .amount-label, .refund-label {
								margin-right: 6rpx;
							}

							&-goodStatus {
								position: absolute;
								z-index: 10;
								right: 0;
								bottom: 30rpx;
							}

							&-afterSale {
								position: absolute;
								z-index: 10;
								right: 0;
								bottom: 80rpx;
							}

							&-goodStatus {
								color: #FF5151;
							}

							&-afterSale {
								display: flex;
								align-items: center;
								justify-content: center;
								font-size: 28rpx;
								color: #999999;
								height: 60rpx;
								border: 2rpx solid #999999;
								border-radius: 100rpx;
								margin-left: 14rpx;
								padding: 0 20rpx;

								&.exchange-btn {
									color: #1A69D1;
									border-color: #1A69D1;
								}
							}

							&-other {
								display: flex;
								align-items: center;
								justify-content: space-between;

								&-left {
									display: flex;
									align-items: center;
								}

								&-right {
									display: flex;
									align-items: center;
									margin-left: 20rpx;
									color: #E6A600;
									font-weight: 500;

									>image {
										width: 30rpx;
										height: 30rpx;
										margin-right: 4rpx;
									}
								}
							}
						}
					}


					&-top {
						display: flex;
						align-items: center;
						justify-content: space-between;
						padding-bottom: 20rpx;
						border-bottom: 2rpx solid #E2E2E2;

						&-left {
							display: flex;
							align-items: center;
							color: #333333;
							font-size: 28rpx;

							.store-name {
								font-weight: 500;
							}

							&-label {
								height: 40rpx;
								line-height: 40rpx;
								color: #22A3FF;
								padding: 0 30rpx;
								background: #d5eeff;
								border-radius: 100px;
								margin: 0 20rpx;
							}

							&-arrow {
								width: 30rpx;
								height: 30rpx;
							}
						}

						&-right {
							font-weight: 600;
							color: #333333;
							font-size: 28rpx;

							&.status-success {
								color: #07C160;
							}

							&.status-pending {
								color: #1A69D1;
							}

							&.status-closed {
								color: #FF5151;
							}
						}
					}
				}
			}
		}

		.load-more {
			text-align: center;
			padding: 20rpx 0 40rpx;
			color: #999;
			font-size: 24rpx;
		}
	}
</style>