<template>


	<view class="afterSaleDetailNew">
		<view class="afterSaleDetailNew-top">
			<view class="afterSaleDetailNew-top-status">
				{{topInfo.title}}
			</view>
			<view class="afterSaleDetailNew-top-spec" v-if="topInfo.content">
				{{topInfo.content}}
			</view>
			<!-- 拒绝状态的结构化信息展示 -->
			<view class="afterSaleDetailNew-top-refuse-info" v-if="topInfo.refuseInfo">
				<view class="refuse-item" v-if="topInfo.refuseInfo.explanation">
					<text class="refuse-label">拒绝说明:</text>
					<text class="refuse-value">{{topInfo.refuseInfo.explanation}}</text>
				</view>
				<view class="refuse-item">
					<text class="refuse-label">拒绝原因:</text>
					<text class="refuse-value">{{topInfo.refuseInfo.reason}}</text>
				</view>
				<view class="refuse-item">
					<text class="refuse-label">拒绝时间:</text>
					<text class="refuse-value">{{topInfo.refuseInfo.time}}</text>
				</view>
			</view>
			<view class="afterSaleDetailNew-top-button">
				<view @click="toAfterSale" v-if="info.status == 0">
					修改申请
				</view>
				<view @click.stop="dealOrder" v-if="info.status == 0">
					撤销申请
				</view>
			</view>


			<image class="afterSaleDetailNew-top-custom" src="@/static/custom.png" mode="" style="display: none;"></image>
		</view>
		<view class="afterSaleDetailNew-wrap" v-if="jhTitle">
			<view class="afterSaleDetailNew-wrap-title">
				<view class="afterSaleDetailNew-wrap-title-spec">

				</view>
				<view>
					寄回商品
				</view>
			</view>

			<view class="afterSaleDetailNew-wrap-jhInfo">
				<view class="afterSaleDetailNew-wrap-jhInfo-title">
					<text class="status-icon">!</text>
					{{jhTitle}}
				</view>
				<!-- 	<view class="afterSaleDetailNew-wrap-jhInfo-desc">
					未与平台平台协商一致
				</view> -->
				<view v-if="info.status == 1" class="afterSaleDetailNew-wrap-jhInfo-btn" @click.stop="toEditLogistics">
					<text class="btn-icon">✉</text>
					提交寄件信息
				</view>
			</view>
			<view>
				<template v-if="info.buyerLogisticsCompany || info.buyerTrackingNumber">
					<view class="afterSaleDetailNew-wrap-lineTwo">
						<view>
							买家寄回物流公司
						</view>
						<view class="afterSaleDetailNew-wrap-lineTwo-right">
							{{info.buyerLogisticsCompany}}
						</view>
					</view>
					<view class="afterSaleDetailNew-wrap-lineTwo">
						<view>
							买家寄回快递单号
						</view>
						<view class="afterSaleDetailNew-wrap-lineTwo-right">
							{{info.buyerTrackingNumber}}
							<image @click="copy(info.buyerTrackingNumber)" src="@/static/copy.png" mode=""></image>
						</view>
					</view>
				</template>
				<template v-if="info.merchantLogisticsCompany || info.merchantTrackingNumber">
					<view class="afterSaleDetailNew-wrap-lineTwo">
						<view>
							卖家寄回物流公司
						</view>
						<view class="afterSaleDetailNew-wrap-lineTwo-right">
							{{info.merchantLogisticsCompany}}
						</view>
					</view>
					<view class="afterSaleDetailNew-wrap-lineTwo">
						<view>
							卖家寄回快递单号
						</view>
						<view class="afterSaleDetailNew-wrap-lineTwo-right">
							{{info.merchantTrackingNumber}}
							<image @click="copy(info.merchantTrackingNumber)" src="@/static/copy.png" mode="">
							</image>
						</view>
					</view>
				</template>

			</view>

			<view class="afterSaleDetailNew-wrap-smaTitle">
				{{info.status === '1' || info.refundType === '2' ? '商家收货地址' : '收货人信息'}}
			</view>
			<view class="afterSaleDetailNew-wrap-smaWrap">
				<view class="afterSaleDetailNew-wrap-lineOne" v-if="info.status === '1' || info.refundType === '2'" style="color: #1A69D1; margin-bottom: 20rpx;">
					请将商品寄回至以下地址
				</view>
				<view class="afterSaleDetailNew-wrap-lineOne">
					联系人: {{exchangeMemberShippingAddressRes.consignee}}
				</view>
				<view class="afterSaleDetailNew-wrap-lineOne">
					联系方式:
					{{exchangeMemberShippingAddressRes.contactNumber}}
				</view>
				<view class="afterSaleDetailNew-wrap-lineOne">
					收货地址:
					{{exchangeMemberShippingAddressRes.shippingAddress}}
				</view>
			</view>
		</view>
		<view class="afterSaleDetailNew-wrap">
			<view class="afterSaleDetailNew-wrap-title">
				<view class="afterSaleDetailNew-wrap-title-spec">

				</view>
				<view>
					{{info.refundType === '2' ? '换货信息' : '退款信息'}}
				</view>
			</view>
			<view class="afterSaleDetailNew-goods-good">
				<image v-if="info.mainPicture" class="afterSaleDetailNew-goods-good-left"
					:src="imgUrl + parseImgurl(info.mainPicture)?.[0]" mode="">
				</image>
				<image v-else-if="info.goodMainPicture" class="afterSaleDetailNew-goods-good-left"
					:src="imgUrl + parseImgurl(info.goodMainPicture)?.[0]" mode=""> </image>
				<view class="afterSaleDetailNew-goods-good-right">
					<view class="afterSaleDetailNew-goods-good-right-name">
						{{info.goodName}}
					</view>
					<view class="afterSaleDetailNew-goods-good-right-spec">
						规格:{{info.specification || info.goodSpecification}}
					</view>
					<view class="afterSaleDetailNew-goods-good-right-other">
						数量:{{info.goodRecordAmount}}
						<view class="afterSaleDetailNew-goods-good-right-other-right">
							<image src="@/static/index/i_1.png" mode=""></image>
							{{info.goodRecordActualPayment}}
						</view>
					</view>

				</view>
			</view>
			<view class="afterSaleDetailNew-wrap-lineTwo">
				<view>
					收货状态
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{info.refundType === '0' ? '未收到货': '已收到货'}}
				</view>
			</view>
			<view class="afterSaleDetailNew-wrap-lineTwo" v-if="info.refundType !== '2'">
				<view>
					退款原因
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{info.refundReasonLabel}}
				</view>
			</view>
			<view class="afterSaleDetailNew-wrap-lineTwo">
				<view>
					申请件数
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{info.refundAmount}}
				</view>
			</view>

			<!-- 	<view class="afterSaleDetailNew-wrap-lineTwo" v-if="info.exchangeMemberShippingAddress">
				<view>
					收货地址
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{exchangeMemberShippingAddressRes.consignee}}
					{{exchangeMemberShippingAddressRes.contactNumber}}
					{{exchangeMemberShippingAddressRes.shippingAddress}}
				</view>
			</view> -->
			<view class="afterSaleDetailNew-wrap-lineTwo">
				<view>
					{{info.refundType === '2' ? '换货' :'申请'}}说明
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{info.remarks}}
				</view>
			</view>
			<view class="afterSaleDetailNew-wrap-lineTwo" v-if="info.exchangeGoodSpecification">
				<view>
					兑换规格
				</view>
				<view style="text-align: right;">
					<text style="margin-left: 20rpx;" v-for="(it,id) in exchangeGoodSpecificationList" :key="id">
						{{it}}
					</text>

					<!-- <image v-for="(item,index) in (info.refundCertificate.split(','))" :key="index"
						:src="(item.indexOf('http') == -1 ? imgUrl : '' ) + item"
						style="width: 100rpx;height: 100rpx;border-radius: 16rpx;margin-left: 14rpx;display: inline-block;">
					</image> -->
				</view>

			</view>
			<view class="afterSaleDetailNew-wrap-lineTwo">
				<view>
					受理编号
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{info.id}}
					<image @click="copy(info.id)" src="@/static/copy.png" mode=""></image>
				</view>
			</view>
			<view class="afterSaleDetailNew-wrap-lineTwo">
				<view>
					申请时间
				</view>
				<view class="afterSaleDetailNew-wrap-lineTwo-right">
					{{info.createTime}}
				</view>
			</view>

		</view>

		<submitSendInfo ref='submitSendInfoRef' @sure='submitSendInfoSure'></submitSendInfo>

	</view>
</template>
<script setup>
	import {
		onLoad,
		onUnload
	} from "@dcloudio/uni-app"
	import {
		computed,
		ref
	} from "vue";
	import {
		clone,
		getAllTime,
		getSafeBottom,
		parseImgurl
	} from "@/utils";
	import {
		navTo
	} from "@/hooks";
	import submitSendInfo from './components/submitSendInfo/submitSendInfo.vue'
	let options = ref({});
	let submitSendInfoRef = ref()
	let info = ref({})
	//关闭原因字典列表
	let refundCloseList = ref([])
	//退款原因字典列表
	let orderStoreRefundReason = ref([])
	//倒计时时间
	let countDonwOwnTimer = ref(0)
	const imgUrl = uni.env.IMAGE_URL
	let countDownTimer = computed(() => {
		return getAllTime(countDonwOwnTimer.value) || {}
	})
	const exchangeGoodSpecificationList = computed(() => {
		if (info.value?.exchangeGoodSpecification) {
			return info.value.exchangeGoodSpecification.split('|||')
		}
		return []
	})
	onLoad((op) => {
		options.value = op
		refresh()
		uni.$on('refreshAfterSaleDetail', refresh)
	})
	onUnload(() => {
		uni.$off('refreshAfterSaleDetail')
	})
	async function refresh() {
		uni.showLoading({
			mask: true
		})
		let {
			data
		} = await uni.http.get(uni.api.refundQueryById, {
			params: {
				id: options.value.id
			}
		})
		info.value = data.result
		if (data.result.status == 1) {
			let {
				data: countDownData
			} = await uni.http.get(uni.api.refundOrderTimer, {
				params: {
					id: data.result.id
				}
			})
			countDonwOwnTimer.value = countDownData.result?.timer || 0
		}
		uni.hideLoading()
		getAllDict()
	}
	//请求字典值
	async function getAllDict() {
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=refund_close_explain`);
		refundCloseList.value = data.result

		// let {
		// 	data: dataSec
		// } = await uni.http.get(`${uni.api.getDicts}?code=order_refund_reason`);
		// orderStoreRefundReason.value = dataSec.result
	}
	async function submitSendInfoSure(submitInfo) {
		uni.showLoading({
			mask: true
		})
		await uni.http.post(uni.api.editLogisticsInfo, submitInfo)
		submitSendInfoRef.value.close()
		uni.showToast({
			title: '提交成功~',
			icon: "none"
		})
		setTimeout(() => {
			refresh()
		}, 500)
	}

	//去修改申请
	function toAfterSale() {
		let afterInfo = clone(info.value)
		//将主键id改为商品记录id 将原主键id(售后单编号)存在mainId中
		afterInfo.mainId = afterInfo.id
		afterInfo.id = afterInfo.orderGoodRecordId
		let item = {
			...afterInfo,
			goods: clone([afterInfo]),
			isEditAfter: 1
		}
		navTo('/packageOrder/pages/applyForAfterSale/applyForAfterSale', () => {
			setTimeout(() => {
				let obj = {
					selectedId: [item.goods[0].id],
					info: item,
					// 使用字符串比较，确保正确处理类型
					tkType: parseInt(item.refundType) + 1,
					isEditAfter: 1
				}
				uni.$emit('applyForAfterSaleInfo', obj)
			}, 300)
		})
	}
	//去填写物流
	function toEditLogistics() {
		submitSendInfoRef.value.open(info.value)
	}
	//复制
	function copy(data) {
		uni.setClipboardData({
			data
		});
	}
	//撤销申请
	function dealOrder() {
		let item = info.value
		uni.showModal({
			title: '确认撤销申请吗',
			content: '撤销后将无法恢复该申请',
			async success(res) {
				if (res.confirm) {
					await uni.http.post(uni.api.refundUndo, {
						id: item.id
					})
					uni.showToast({
						title: '撤销成功~',
						icon: 'none'
					})
					setTimeout(() => {
						refresh()
					}, 500)
				}
			}
		})
	}
	const exchangeMemberShippingAddressRes = computed(() => {
		// 在退换货场景下，应该显示商家的收货地址信息，而不是买家的收货信息
		// 判断是否为退换货场景（status为1表示待买家退回）
		// 注意：status和refundType是字符串类型，需要使用字符串比较
		if (info.value?.status === '1' || info.value?.refundType === '2') {
			// 返回商家的退货收货地址信息
			return {
				consignee: "张少林", // 商家收货人姓名
				contactNumber: "15505903237", // 商家收货人电话
				// 使用完整的地址格式，包括省市区信息
				shippingAddress: "福建省福州市鼓楼区软件园", // 商家收货地址
				// 保存原始字段，以便将来可能的扩展使用
				merchantConsigneeName: "张少林",
				merchantConsigneePhone: "15505903237",
				merchantConsigneeProvinceId: "福建省",
				merchantConsigneeCityId: "福州市",
				merchantConsigneeAreaId: "鼓楼区",
				merchantConsigneeAddress: "软件园"
			};
		} else {
			// 非退换货场景，使用原有逻辑
			let res = ''
			if (info.value?.exchangeMemberShippingAddress) {
				try {
					res = JSON.parse(info.value.exchangeMemberShippingAddress)
				} catch (e) {
					console.error(e, '解析exchangeMemberShippingAddress出错')
					//TODO handle the exception
				}
			}
			return res;
		}
	})
	const topInfo = computed(() => {
		let infoRes = {
			title: '',
			content: '',
			time: '',
		}
		// 使用数字转换确保正确比较
		// 注意：status是字符串类型，需要转换为数字进行比较
		const statusNum = parseInt(info.value.status);
		switch (statusNum) {
			//待处理
			case 0:
				infoRes.title = '请等待店铺处理';
				infoRes.content = '我们将尽快为您处理'
				break;

			case 1:
				infoRes.title = '待买家退回';
				break;
				//换货中(等待店铺确认收货)
			case 2:
				infoRes.title = '请等待店铺处理'
				// 根据是否为换货场景显示不同文案
				infoRes.content = info.value.refundType === '2' ? '收货后我们将尽快为您进行换货' : '收货后我们将尽快为您进行退货退款'
				break;
				//处理中（退款/换货）
			case 3:
				infoRes.title = '请等待店铺处理'
				// 根据是否为换货场景显示不同文案
				infoRes.content = info.value.refundType === '2' ? '我们将尽快为您处理换货申请' : '我们将尽快为您审核退款'
				break;
				//成功（退款/换货）
			case 4:
				// 根据是否为换货场景显示不同文案
				infoRes.title = info.value.refundType === '2' ? '换货成功' : '退款成功'
				infoRes.time = info.value.updateTime
				break;
				//已拒绝
			case 5:
				infoRes.title = info.value.refundType === '2' ? '店铺拒绝换货' : '店铺拒绝退款'
				infoRes.time = info.value.updateTime
				// 不再使用简单的文本拼接，改为在模板中使用结构化数据
				infoRes.refuseInfo = {
					explanation: info.value.refundExplain || '',
					reason: info.value.refusedExplain || '',
					time: info.value.updateTime || ''
				}
				break;
				//关闭（退款/换货）
			case 6:
				// 根据是否为换货场景显示不同文案
				infoRes.title = info.value.refundType === '2' ? '换货关闭' : '退款关闭'
				infoRes.time = info.value.updateTime
				infoRes.content = refundCloseList.value.find(i => i.value == info.value.closeExplain)?.text || ''
				break;
				//换货关闭
			case 7:
				infoRes.title = '换货关闭'
				infoRes.time = info.value.updateTime
				infoRes.content = refundCloseList.value.find(i => i.value == info.value.closeExplain)?.text || ''
				break;
				//换货完成
			case 8:
				infoRes.title = '换货完成'
				break;
			default:
				break;
		}
		return infoRes
	})
	const jhTitle = computed(() => {
		let t = ''
		// 使用数字转换确保正确比较
		// 注意：status是字符串类型，需要转换为数字进行比较
		const statusNum = parseInt(info.value.status);
		switch (statusNum) {
			//待处理
			case 1:
				t = '平台已同意换货申请，请尽早退货'
				break;
				//换货中(等待店铺确认收货)
			case 2:
				t = '平台正在换货中'
				break;
				//换货完成
			case 8:
				t = '平台已换货完成'
				break;
		}
		return t
	})
</script>
<style lang="scss">
	page {
		background-color: #f8f8f8;
	}

	.afterSaleDetailNew {

		&-goods {
			padding: 0 30rpx 30rpx;
			background-color: white;
			margin-bottom: 30rpx;

			&-specTop {
				height: 100rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;

				&-name {
					color: #333333;
					font-size: 28rpx;
				}

				&-btn {
					width: 200rpx;
					height: 56rpx;
					border: 2rpx solid #999999;
					border-radius: 100rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					color: #999999;
					font-size: 24rpx;
				}
			}

			&-hj {
				// padding-top: 26rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				border-top: 2rpx solid #f6f6f6;


				&-right {
					display: flex;
					align-items: center;
					justify-content: flex-end;
					height: 80rpx;
					font-size: 28rpx;
					color: #999999;

					>image {
						width: 30rpx;
						height: 30rpx;
						margin: 0 14rpx;
					}
				}
			}

			&-good {
				display: flex;
				align-items: center;
				padding: 20rpx 0;
				position: relative;

				&-left {
					width: 184rpx;
					height: 184rpx;
					margin-right: 14rpx;
				}

				&-right {
					height: 160rpx;
					display: flex;
					flex-direction: column;
					justify-content: space-between;

					&-name {
						font-size: 28rpx;
						color: #000000;
					}

					&-spec,
					&-other,
					&-goodStatus {
						font-size: 28rpx;
						color: #999999
					}

					&-goodStatus {
						position: absolute;
						z-index: 10;
						right: 0;
						bottom: 30rpx;
					}

					&-other {
						display: flex;
						align-items: center;

						&-right {
							display: flex;
							align-items: center;
							margin-left: 20rpx;
							color: #E6A600;

							>image {
								width: 30rpx;
								height: 30rpx;
								// margin-right: 10rpx;
							}
						}
					}
				}
			}
		}

		&-top {
			padding: 40rpx 0;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			border-bottom: 2rpx solid #E2E2E2;
			position: relative;
			background-color: white;

			&-status {
				color: #333333;
				font-size: 40rpx;
				margin-bottom: 14rpx;
			}

			&-spec {
				color: #999999;
				font-size: 28rpx;
			}

			&-refuse-info {
				width: 90%;
				padding: 20rpx 30rpx;
				margin: 10rpx auto 20rpx;
				background-color: #f8f8f8;
				border-radius: 8rpx;

				.refuse-item {
					margin-bottom: 16rpx;
					display: flex;
					align-items: flex-start;

					&:last-child {
						margin-bottom: 0;
					}

					.refuse-label {
						color: #666666;
						font-size: 28rpx;
						min-width: 140rpx;
						margin-right: 10rpx;
					}

					.refuse-value {
						color: #333333;
						font-size: 28rpx;
						flex: 1;
					}
				}
			}

			&-custom {
				width: 0;
				height: 0;
				position: absolute;
				right: 30rpx;
				top: 30rpx;
				z-index: -1;
				opacity: 0;
				visibility: hidden;
				display: none;
			}

			&-scrollView {
				width: 700rpx;
				height: 110rpx;
				white-space: nowrap;

				&-ct {
					display: flex;
					align-items: center;

					&-view {
						width: 240rpx;
						height: 100rpx;
						border: 2rpx solid #999999;
						border-radius: 8rpx;
						display: flex;
						align-items: center;
						padding: 0 10rpx;
						margin-right: 20rpx;

						&-img {
							width: 80rpx;
							height: 80rpx;
							border: 2rpx solid #f5f5f5;
							border-radius: 8rpx;
							margin-right: 15rpx;
						}

						&-right {
							height: 80rpx;
							font-size: 26rpx;
							display: flex;
							flex-direction: column;
							justify-content: space-between;
							color: #000000;
						}
					}
				}
			}

			&-button {
				display: flex;
				align-items: center;
				margin-top: 20rpx;

				>view {
					height: 60rpx;
					line-height: 60rpx;
					padding: 0 20rpx;
					font-size: 26rpx;
					color: #999999;
					border: 2rpx solid #999999;
					border-radius: 100rpx;
					margin-right: 20rpx;
				}

				>view:last-child {
					margin-right: 0;
				}

			}
		}


		&-wrap {
			padding: 30rpx;
			background-color: white;
			margin-bottom: 20rpx;

			&-smaTitle {
				font-size: 28rpx;
				color: #333333;
				margin-bottom: 20rpx;
			}

			&-smaWrap {
				padding: 30rpx;
				border: 2rpx solid #dbdbdb;
				border-radius: 8rpx;
			}

			&-jhInfo {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				text-align: center;
				padding: 30rpx 0;
				margin: 20rpx 0;
				border-radius: 16rpx;
				background-color: #f9fbff;
				border: 2rpx solid #e8f1ff;

				&-title {
					color: #1A69D1;
					font-size: 28rpx;
					font-weight: bold;
					margin-bottom: 30rpx;
					display: flex;
					align-items: center;
					justify-content: center;

					.status-icon {
						display: inline-flex;
						align-items: center;
						justify-content: center;
						width: 36rpx;
						height: 36rpx;
						border-radius: 50%;
						background: #1A69D1;
						color: white;
						font-size: 24rpx;
						margin-right: 10rpx;
						font-weight: bold;
					}
				}

				&-desc {
					color: #999999;
					font-size: 26rpx;
					margin: 20rpx 0;
				}

				&-btn {
					width: 300rpx;
					height: 68rpx;
					line-height: 68rpx;
					background: #1A69D1;
					border-radius: 100rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 28rpx;
					color: white;
					box-shadow: 0 4rpx 8rpx rgba(26, 105, 209, 0.2);
					transition: all 0.3s ease;

					.btn-icon {
						margin-right: 8rpx;
						font-size: 24rpx;
					}

					&:active {
						transform: scale(0.98);
						background: #1559b7;
					}
				}
			}

			&-title {
				margin-bottom: 20rpx;
				display: flex;
				align-items: center;
				color: #000000;
				font-size: 28rpx;

				&-spec {
					width: 4rpx;
					height: 20rpx;
					background: #1a69d1;
					border-radius: 10rpx;
					margin-right: 14rpx;
				}

			}

			&-lineTwo {
				height: 100rpx;
				border-bottom: 2rpx solid #D9D9D9;
				display: flex;
				align-items: center;
				justify-content: space-between;
				color: #666666;
				font-size: 28rpx;

				&-right {
					flex: 1;
					margin-left: 30rpx;
					display: flex;
					align-items: center;
					justify-content: flex-end;

					>image {
						width: 30rpx;
						height: 30rpx;
						margin-left: 10rpx;
					}
				}
			}

			&-lineTwo:last-child {
				border-bottom: none;
			}

			&-lineOne {
				color: #666666;
				font-size: 28rpx;
				line-height: 40rpx;
				margin-bottom: 14rpx;
			}

			&-lineOne:last-child {
				margin-bottom: 0;
			}
		}
	}

	/* 小屏幕适配 */
	@media screen and (max-width: 375px) {
		.afterSaleDetailNew {
			&-wrap-jhInfo {
				padding: 20rpx 0;

				&-title {
					font-size: 26rpx;
				}

				&-btn {
					width: 280rpx;
					height: 64rpx;
					line-height: 64rpx;
					font-size: 26rpx;
				}
			}
		}
	}

	/* 大屏幕适配 */
	@media screen and (min-width: 768px) {
		.afterSaleDetailNew {
			&-wrap-jhInfo {
				padding: 40rpx 0;

				&-title {
					font-size: 32rpx;
				}

				&-btn {
					width: 340rpx;
					height: 76rpx;
					line-height: 76rpx;
					font-size: 30rpx;
				}
			}
		}
	}
</style>