<template>
	<uni-popup ref="popup" type="bottom">
		<view class="submitSendInfo">
			<view class="submitSendInfo-title">
				提交寄件信息
			</view>
			<view class="submitSendInfo-line">
				<view class="submitSendInfo-line-title">
					请选择物流
				</view>
				<view class="submitSendInfo-line-cnt" @click="changeShowPicker">
					<view style="color: #999999;" v-if="!submitInfo.buyerLogisticsCompany">
						请选择物流
					</view>
					<view v-else>
						{{submitInfo.buyerLogisticsCompanyName}}
					</view>
					<image class="submitSendInfo-line-cnt-arrow" src="@/static/right-arrow-gray.png" mode=""></image>
				</view>
			</view>
			<view class="submitSendInfo-line">
				<view class="submitSendInfo-line-title">
					请输入物流单号
				</view>
				<view class="submitSendInfo-line-cnt">
					<input class="submitSendInfo-line-input" type="text" placeholder="请输入物流单号"
						v-model="submitInfo.buyerTrackingNumber" />
					<view>

					</view>
				</view>
			</view>
			<view class="submitSendInfo-ope">
				<button @click="close">取消</button>
				<button @click="sure">确定</button>
			</view>
		</view>
	</uni-popup>
	<w-picker v-if="wlList?.length" mode="selector" value="0" :visible.sync="showPicker" :options="wlList"
		@confirm='pickerConfirm' @cancel='pickerCancel'>
	</w-picker>

</template>

<script setup>
	import {
		ref
	} from 'vue'
	const emits = defineEmits(['sure'])
	const popup = ref()
	const showPicker = ref()
	//物流列表
	let wlList = ref([])
	//提交表单
	let submitInfo = ref({
		id: '',
		buyerLogisticsCompany: '',
		buyerTrackingNumber: '',
		buyerLogisticsCompanyName: ''
	})
	const open = (e) => {
		refresh()
		submitInfo.value.id = e.id
		popup.value.open('bottom')
	}
	const close = () => {
		popup.value.close()
	}
	//选择确认
	function pickerConfirm(e) {
		showPicker.value = false;
		submitInfo.value.buyerLogisticsCompany = e.obj.value
		submitInfo.value.buyerLogisticsCompanyName = e.obj.text
	}
	//选择取消
	function pickerCancel() {
		showPicker.value = false;
	}
	async function refresh() {
		let {
			data
		} = await uni.http.get(`${uni.api.getDicts}?code=logistics_company`);
		wlList.value = data.result
	}
	//显示选择弹窗
	function changeShowPicker() {
		showPicker.value = true;
	}

	function sure() {
		if (!submitInfo.value.buyerLogisticsCompany) {
			uni.showToast({
				title: '请选择物流公司',
				icon: "none"
			})
			return;
		}
		if (!submitInfo.value.buyerTrackingNumber) {
			uni.showToast({
				title: '请填写快递单号',
				icon: "none"
			})
			return;
		}
		emits('sure', submitInfo.value)
	}
	defineExpose({
		open,
		close
	})
</script>

<style lang="scss">
	.submitSendInfo {
		padding: 30rpx;
		background-color: white;
		border-radius: 40rpx 40rpx 0 0;

		&-title {
			color: #333333;
			font-size: 28rpx;
			margin: 0 auto;
			margin-bottom: 30rpx;
			text-align: center;
		}

		&-ope {
			display: flex;
			align-items: center;
			justify-content: space-around;

			>button {
				width: 280rpx;
				height: 80rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 28rpx;
			}
		}

		&-line {
			margin-bottom: 30rpx;

			&-title {
				color: #000000;
				font-size: 28rpx;
				margin-bottom: 20rpx;
			}

			&-cnt {
				height: 80rpx;
				border-radius: 16rpx;
				border: 2rpx solid #999999;
				padding: 0 20rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				font-size: 28rpx;
				color: black;

				&-arrow {
					width: 30rpx;
					height: 30rpx;
				}

				&-input {
					font-size: 28rpx;
				}
			}
		}
	}
</style>