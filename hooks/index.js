import {
	userStore,
	locationInfo,
	getAllBeShared,
	setSaleChannelStoreInfo,
	recordFullPagePath
} from '@/store/index.js';
import {
	computed,
	isRef,
	ref,
	watch
} from 'vue';
import {
	clone,
	parseObjToPath
} from '@/utils';
import {
	onReachBottom,
	onPullDownRefresh,
	onLoad,
	onUnload,
	onShow,
	onShareAppMessage
} from '@dcloudio/uni-app';

//登录成功后的公共方法
export async function loginSuccessCallBack(data) {
	console.log(data)
	const recordFullPagePathStore = recordFullPagePath();
	const users = userStore();
	users.setToken(data.result['X-AUTH-TOKEN']);
	getUserInfos();
	if (recordFullPagePathStore.pageFullPath) {
		uni.redirectTo({
			url: decodeURIComponent(recordFullPagePathStore.pageFullPath),
			success() {
				recordFullPagePathStore.setPageFullPath('');
			},
			fail() {
				uni.switchTab({
					url: decodeURIComponent(recordFullPagePathStore.pageFullPath),
					success() {
						recordFullPagePathStore.setPageFullPath('');
					},
					fail() {
						uni.switchTab({
							url: '/pages/index/index',
						});
					},
				});
			},
		});
	} else {
		uni.switchTab({
			url: '/pages/index/index',
		});
	}
}

//获取用户信息
export async function getUserInfos() {
	if (userStore().token) {
		let {
			data
		} = await uni.http.get(uni.api.getMemberInfo);
		userStore().setUserInfo(data.result);
	} else {
		userStore().clearUserInfo();
	}
}
//navigate跳转
export function navTo(url, callback = '') {
	uni.navigateTo({
		url,
		fail() {
			switchTo(url);
		},
		success() {
			callback && callback();
		},
	});
}
//redirectTo跳转
export function redTo(url) {
	uni.redirectTo({
		url,
	});
}
export function goBackIsTop() {
	let pages = getCurrentPages(); // 当前页面
	if (pages.length > 1) {
		// let beforePage = pages[pages.length - 2]; // 上一页
		uni.navigateBack({
			success: function() {
				// beforePage.$vm.reFresh();
			}
		})
	} else {
		uni.reLaunch({
			url: "/pages/index/index"
		})
	}
}
//switchTab跳转
export function switchTo(url) {
	uni.switchTab({
		url,
	});
}
//navigate跳转(包含登录判定)  shoudRecord:
export function navToBeforeLogin(url = '', shoudRecord = false) {
	if (!userStore().token) {
		if (shoudRecord) {
			let pages = getCurrentPages();
			let currentFullPath = pages[pages.length - 1]['$page']['fullPath']; //页面路径(带参数)
			const recordFullPagePathStore = recordFullPagePath();
			//如果当前页面不在登录页 则记录当前页面信息后跳转登录页
			if (currentFullPath.indexOf('login') == -1) {
				recordFullPagePathStore.setPageFullPath(currentFullPath);
			}
		}
		uni.reLaunch({
			url: '/packageSearch/pages/login/login',
		});
	} else if (url) {
		navTo(url);
	}
}
//跳转到商品详情
export function navToGoodDetail(item) {
	let obj = {
		...goodResultId(item),
		goodId: item.goodId || item.id,
		isPlatform: item.isPlatform || 0,
		isTransition: '1',
	};
	navTo(`/packageGoods/pages/productInfo/productInfo${parseObjToPath(obj)}`);
}

//跳转到店铺首页
export function navToStoreIndex(item) {
	navTo(
		`/packageGoods/pages/shopIndex/shopIndex?storeManageId=${item.storeManageId || item.id || item.storeId}&sysUserId=${item.sysUserId}`
	);
}
//页面拦截,先到验证手机号页面,再进行下一步 type:1跳转到交易密码
export function authPhone(type = 1) {
	navToBeforeLogin(`/pages/phoneAuthCode/phoneAuthCode?type=${type}`);
}

//返回上一页(当前页面如果为第一个页面 即无上一页 则返回首页)
export function toUpPage() {
	if (getCurrentPages().length == 1) {
		uni.switchTab({
			url: '/pages/index/index',
		});
	} else {
		uni.navigateBack();
	}
}

//请求列表页相关的公共方法开始(包括上拉加载,下拉刷新等)
export function listGet({
	method = 'get', //请求方法
	options = ref({}), //请求需要的参数
	apiUrl = '', //接口地址
	reqType = 0, //0 onLoad请求  1 onShow请求
	isReachBottom = true, //是否开启下拉加载
	isPullDownRefresh = true, //是否开启上拉刷新
	isWatchOptions = true, //是否开启对 options变量的监听
	isReqTypeReq = true, //是否开启对onload,onshow请求
	openLoadingShow = true, //是否开启loading加载 (仅对刷新时生效)
	pdIsLogin = false, //是否开启登陆判定
	pageSize = 10, //页码默认值
}) {
	//列表页公共参数
	let params = ref({
		pageNo: 1,
		pageSize,
	});
	//列表页数据
	let list = ref([]);
	//列表加载状态  more/loading/noMore
	let mode = ref('more');
	//数据总数
	let total = ref(0);
	// type: 1刷新   2下拉加载
	async function refresh(type = 1) {
		if (!apiUrl) {
			console.error('未设置请求接口地址apiUrl');
			return;
		}
		if (!isRef(options)) {
			console.error('options必须是ref的值');
			return;
		}
		if (pdIsLogin && !userStore().token) {
			console.error('暂未登录', apiUrl);
			return;
		}
		if (type == 1) {
			mode.value = 'more';
			params.value.pageNo = 1;
			if (openLoadingShow) {
				uni.showLoading({
					mask: true,
				});
			}
		} else {
			if (mode.value != 'more') {
				return;
			}
			params.value.pageNo += 1;
		}

		mode.value = 'loading';
		let requestObj = {
			...params.value,
			...options.value,
		};
		if (method == 'get') {
			requestObj = {
				params: {
					...params.value,
					...options.value,
				},
			};
		}
		let urlRes = apiUrl;
		if (isRef(apiUrl)) {
			urlRes = apiUrl.value;
		}
		let {
			data
		} = await uni.http[method](urlRes, requestObj);
		total.value = data.result?.total || 0;
		let records = data.result?.records || data.result;
		if (type == 1) {
			list.value = records;
		} else {
			list.value = [...list.value, ...records];
		}
		if (records.length < params.value.pageSize) {
			mode.value = 'noMore';
		} else {
			mode.value = 'more';
		}
		// 立即隐藏加载提示和停止下拉刷新动画
		uni.hideLoading();
		uni.stopPullDownRefresh();
	}
	if (isReqTypeReq) {
		const initReqTypeList = [onLoad, onShow];
		initReqTypeList[reqType](async () => {
			refresh();
		});
	}

	onReachBottom(() => {
		let isReachBottomRes = isReachBottom;
		if (isRef(isReachBottom)) {
			isReachBottomRes = isReachBottom.value;
		}
		if (isReachBottomRes) {
			refresh(2);
		}
	});

	onPullDownRefresh(() => {
		if (isPullDownRefresh) {
			refresh().catch(err => {
				console.error('下拉刷新失败', err);
				// 确保停止下拉刷新动画
				uni.stopPullDownRefresh();
			});
		} else {
			// 如果不需要下拉刷新，也要停止下拉刷新动画
			uni.stopPullDownRefresh();
		}
	});

	if (isWatchOptions) {
		watch(
			() => {
				return {
					...options.value,
				};
			},
			() => {
				refresh();
			}
		);
	}

	return {
		params,
		list,
		mode,
		total,
		refresh,
	};
}
//请求列表页结束

//支付公共方法(请求接口拿到数据后的处理)
//payClassic -1开通店铺 0商品 1礼包 2礼包团 3余额充值 4待支付订单 5兑换券支付 6批发卡充值
export function goPayDeal(d, payClassic = 0, callback = '', failCallback = '') {
	let respon = d.data.result;
	let resInfos = respon.jsonStr;
	//拿到notifyUrl后的操作
	async function notifyUrlToGet(url) {
		try {
			let {
				data
			} = await uni.http.request({
				method: 'GET',
				baseURL: '',
				url,
			});
			let navToObj = {
				type: 1,
				payClassic,
			};
			if (payClassic == 1 && d.id) {
				navToObj.id = d.id;
			}
			if (data.code == 200) {
				//支付成功
				// 隐藏加载提示
				uni.hideLoading();

				// 如果有回调函数，调用回调函数（显示支付成功弹窗）
				if (typeof callback === 'function') {
					callback(data)
				} else {
					// 如果没有回调函数，显示支付成功提示
					uni.showToast({
						title: '支付成功',
						icon: 'success',
						duration: 2000
					});

					// 延迟后跳转到订单列表页面
					setTimeout(() => {
						uni.redirectTo({
							url: '/packageOrder/pages/myPackage/myPackage',
							fail: (err) => {
								console.error('跳转订单列表失败:', err);
								// 如果跳转失败，尝试使用switchTab
								uni.switchTab({
									url: '/pages/index/index'
								});
							}
						});
					}, 2000);
				}
			} else {
				if (typeof failCallback === 'function') {
					failCallback(data)
				} else {
					uni.showToast({
						title: data.message,
						icon: 'none',
					});
				}
			}
		} catch (error) {
			console.error(error)
			if (typeof failCallback === 'function') {
				failCallback(data)
			}
			//TODO handle the exception
		}

	}
	try {
		resInfos = JSON.parse(resInfos);
	} catch (e) {
		//TODO handle the exception
		console.error('无法解析jsonStr');
	}
	if (resInfos == '0' || !resInfos) {
		//不需要调用支付
		notifyUrlToGet(respon.notifyUrl);
		return;
	}
	uni.requestPayment({
		provider: 'wxpay',
		...resInfos,
		async success(res) {
			notifyUrlToGet(respon.notifyUrl);
		},
		fail(e) {
			uni.showToast({
				title: '您取消了支付~',
				icon: 'none',
			});
			uni.hideLoading();
		},
		complete() {},
	});
}

//各种品类商品id返回参数id结果（专区，免单等）
function goodResultId(info) {
	console.log('info', info.value);

	let obj = {};
	const keys = ['marketingPrefectureId', 'marketingFreeGoodListId', 'marketingStorePrefectureGoodId',
		'marketingZoneGroupGoodId', 'marketingZoneGroupId', 'marketingRushGoodId',
		'marketingStoreGiftCardMemberListId', 'tMemberId'
	];
	for (let key of keys) {
		if (info[key]) {
			obj[key] = info[key];
		}
	}
	console.log('obj', obj.value);

	return obj;
}

//选择规格弹窗公共部分开始
export function specificationPopPub() {
	let selectInfo = ref({});
	let specificationPop = ref();
	let specificationWrapRef = ref();
	const btnLoading = computed(() => {
		return (specificationWrapRef.value && specificationWrapRef.value.loading) || false;
	});
	//请求加入购物车和立即购买的公共传参
	const requestCartAndBuyOp = computed(() => {
		return {
			...specificationWrapRef.value.requestInfo,
			quantity: specificationWrapRef.value.quality,
			specification: specificationWrapRef.value.specification,
		};
	});

	async function addCart(options = {}, success = '') {
		uni.showLoading({
			mask: true,
		});

		try {
			let {
				data
			} = await uni.http.post(uni.api.addGoodToShoppingCart, {
				...requestCartAndBuyOp.value,
				...options,
				...goodResultId(selectInfo.value),
			});
			
			console.log('addCart API 响应:', data);
			
			uni.showToast({
				title: data.message,
				icon: 'none',
			});
			
			// 立即执行成功回调，传递API返回的结果
			if (success && typeof success === 'function') {
				try {
					await success(data.result);
				} catch (callbackError) {
					console.error('addCart 回调执行失败:', callbackError);
				}
			}
			
			// 延迟关闭弹窗，确保回调执行完成
			setTimeout(() => {
				specificationPop.value?.close();
			}, 300);
		} catch (error) {
			console.error('addCart 失败:', error);
			uni.showToast({
				title: '添加失败',
				icon: 'none',
			});
		} finally {
			uni.hideLoading();
		}
	}
	//立即购买跳转到确认订单(单品购买)
	function toOrder(options = {}) {
		let info = {
			...requestCartAndBuyOp.value,
			...options,
			type: '1',
		};
		//marketingGroupRecordId :info.id
		info = {
			...info,
			...goodResultId(selectInfo.value),
		};
		navTo(`/pages/confirmOrder/confirmOrder${parseObjToPath(info)}`);
	}

	function showSpecificationWrap(item) {
		selectInfo.value = item;
		specificationPop.value.open();
	}

	function specificationWrapChange({
		show
	}) {
		if (!show) {
			selectInfo.value = {};
		}
	}

	return {
		selectInfo,
		addCart,
		toOrder,
		showSpecificationWrap,
		specificationWrapChange,
		btnLoading,
		specificationPop,
		specificationWrapRef,
	};
}
//选择规格弹窗公共部分结束

//获取当前位置的信息
export function getLocation() {
	//判断是否授权
	function authorize() {
		return new Promise((resolve, reject) => {
			uni.authorize({
				scope: 'scope.userLocation',
				success: () => {
					resolve();
				},
				fail: () => {
					reject();
				},
			});
		});
	}
	// 用户首次拒绝授权后(考虑是误点击)，弹框提示是否手动打开位置授权
	function openConfirm() {
		return new Promise((resolve, reject) => {
			uni.showModal({
				title: '获取当前位置',
				content: '我们需要获取您当前的地理位置信息以便为您提供更好的服务',
				success: res => {
					if (res.confirm) {
						uni.openSetting({
							success(res) {
								//如果设置成功
								if (res.authSetting['scope.userLocation'] === true) {
									resolve();
								} else {
									reject();
								}
							},
							fail() {
								reject();
							},
						});
					} else if (res.cancel) {
						reject();
					}
				},
			});
		});
	}
	//最终获取位置
	function getLocationRes(resolve) {
		uni.getLocation({
			type: 'wgs84',
			async success(res) {
				locationInfo().setInfo(res);
				let {
					data
				} = await uni.http.get(uni.api.geocoder, {
					params: {
						location: `${res.latitude},${res.longitude}`,
						address: '',
						pageNo: 1,
						pageSize: 1,
					},
				});
				if (data.result && data.result.length > 0) {
					locationInfo().setCity(data.result[0].ad_info.city);
				}
				resolve(res);
			},
			fail: function(res) {
				resolve({
					longitude: '',
					latitude: '',
				});
			},
		});
	}
	return new Promise(async (resolve, reject) => {
		authorize()
			.then(() => {
				getLocationRes(resolve);
			})
			.catch(() => {
				openConfirm()
					.then(() => {
						getLocationRes(resolve);
					})
					.catch(() => {
						resolve({
							longitude: '',
							latitude: '',
						});
					});
			});
	});
}

//上传单张图片  type 1 普通上传  type 2 1688上传
export function uploadImg(filePath, type = 1) {
	let apiUrl = uni.api.upload;
	if (type == 2) apiUrl = uni.api.uploadRefundVoucher;
	return new Promise(async resolve => {
		let {
			data
		} = await uni.http.upload(apiUrl, {
			fileType: 'file',
			filePath,
			name: 'file',
		});
		if (type == 1) resolve(data);
		if (type == 2)
			resolve({
				message: `${data.result?.imageDomain}/${data.result?.imageRelativeUrl}`,
			});
	});
}

//分享相关  --开始
//分享默认需要传递的参数
function getDefaultParam(shareType = 1) {
	return {
		TmemberName: userStore().userInfo.nickName || '', //推广人用户名
		TmemberHeadPortrait: userStore().userInfo.avatarUrl || '', //推广人用户头像
		phone: userStore().userInfo.phone || '', //推广人用户手机号
		tMemberId: userStore().userInfo.id || '', //推广人用户id
		sysUserId: setSaleChannelStoreInfo().sysUserId || setSaleChannelStoreInfo().intoStoreForSysUserId || userStore()
			.userInfo.sysUserId || '', //绑定店铺 如果有销售渠道店铺 优先取接口获得的sysUserId  再取进店的sysUserId ,没有则分享用户归属店铺
		shareType, //分享类型 1微信分享  2二维码分享
	};
}

//一个相当神奇的问题,重点记录:在页面使用此方法时 ,需要在页面先引入onShareAppMessage后,此方法才会生效
export function shareAbout({
	openWxShare = true, //是否开启微信分享
	page = '', //分享所需页面路径
	imageUrl = '', //图片完整地址
	title = '', //分享标题
	params = {}, //分享时携带的参数
	hbCallback = '', //海报方法
	wxShareCallBack = '', //点击分享时的回调
	shareImageUrl = '',
}) {
	let resParams = ref({});
	let resImageUrl = isRef(imageUrl) ? imageUrl : ref(imageUrl);
	let resshareImageUrl = isRef(shareImageUrl) ? shareImageUrl : ref(shareImageUrl);
	let resTitle = isRef(title) ? title : ref(title);
	let qrCode = ref(''); //生成的二维码信息
	let sharePop = ref(); //分享弹窗
	let getShareObj = ref({}); //接收到的分享信息(用于展示谁分享)
	let getOhterObj = ref({}); //接收到的其他分享信息(不包括分享人,店铺信息等)
	//微信分享携带的参数
	const shareObj = computed(() => {
		let resultPage = `${page}${parseObjToPath({ ...getDefaultParam(1), ...resParams.value })}`;
		let shareObj = {
			title: resTitle.value, // 默认是小程序的名称(可以写slogan等)
			path: resultPage, // 默认是当前页面，必须是以‘/’开头的完整路径
			imageUrl: resshareImageUrl.value || resImageUrl
				.value, //自定义图片路径，可以是本地文件路径、代码包文件路径或者网络图片路径，支持PNG及JPG，不传入 imageUrl 则使用默认截图。显示图片长宽比是 5:4
		};
		return shareObj;
	});

	function getQrCode() {
		//生成页面二维码
		qrCode.value = '';
		return new Promise(async (resolve, reject) => {
			let {
				data
			} = await uni.http.post(uni.api.getQrCodeByPage, {
				page: page.charAt(0) == '/' ? page.substr(1) : page,
				param: JSON.stringify({
					...getDefaultParam(2),
					...resParams.value,
				}),
			});
			qrCode.value = uni.env.IMAGE_URL + data.result;
			resolve(qrCode.value);
		});
	}
	//对于 resParams 带参过滤的处理,如果其中包含分享默认传递的参数 则删除
	function toDealParams(params) {
		const keyList = ['TmemberName', 'TmemberHeadPortrait', 'phone', 'tMemberId', 'shareType'];
		for (let key in params) {
			if (keyList.indexOf(key) != -1) {
				delete params[key];
			}
		}
		return params;
	}
	//控制右上角分享按钮是否可分享
	function shareButtonOriginControl(bol) {
		// #ifdef MP-WEIXIN
		if (bol) {
			uni.showShareMenu();
		} else {
			uni.hideShareMenu();
		}
		// #endif

	}
	//如果resOpenWxShare是动态的 动态判断是否可分享
	if (isRef(openWxShare)) {
		watch(
			() => openWxShare.value,
			() => {
				shareButtonOriginControl(openWxShare.value);
			}, {
				immediate: true,
				deep: true,
			}
		);
	} else {
		shareButtonOriginControl(openWxShare);
	}

	//如果params是动态的 则动态过滤
	if (isRef(params)) {
		watch(
			() => params.value,
			() => {
				resParams.value = toDealParams(params.value);
			}, {
				immediate: true,
				deep: true,
			}
		);
	} else {
		resParams.value = toDealParams(params);
	}

	onShareAppMessage(e => {
		wxShareCallBack && wxShareCallBack(e);
		return shareObj.value;
	});

	//打开分享弹窗
	function shareToggle() {
		sharePop.value.open();
	}
	//分享弹窗打开关闭的监听
	function sharePopChange({
		show
	}) {
		if (!show) {}
	}
	//分享弹窗选择
	async function sharePopSelect(obj) {
		let name = obj.item.name;
		if (name == 'hb') {
			//如果是海报分享
			hbCallback && hbCallback();
		}
	}
	onLoad(async o => {
		let param = clone(o);
		//如果是扫二维码进入,则
		if (o.scene) {
			let {
				data
			} = await uni.http.post(uni.api.findSysSmallcodeById, {
				id: o.scene,
			});
			if (data.result?.param) {
				try {
					param = JSON.parse(data.result?.param);
				} catch (e) {
					console.error(e, '解析二维码数据param错误');
					//TODO handle the exception
				}
			}
			param.scanQRCodesEnter = 1;
		}
		if (o.scene || o.shareType) {
			//将部分信息(推广人信息,店铺id)存入pinia
			let obj = {};
			for (let key in getDefaultParam(1)) {
				if (param[key] && key != 'shareType') {
					//根据isHeaderTMemberId判断tMember需不需要放头部 1不替换头部 0替换头部
					if ((param.isHeaderTMemberId != 1 && key == 'tMemberId') || key != 'tMemberId') {
						obj[key] = param[key] || '';
						delete param[key];
					}
				}
			}
			getAllBeShared().setInfo(obj);
			getShareObj.value = obj;
		}
		getOhterObj.value = param;
		if (param.shareDiscountRatio) {
			getAllBeShared().setShareDiscountRatio(param.shareDiscountRatio);
		}
	});
	return {
		qrCode,
		sharePopSelect,
		sharePopChange,
		shareToggle,
		sharePop,
		getQrCode,
		getShareObj,
		getOhterObj,
	};
}

//分享相关  --结束

//对于销售渠道绑定的统一处理  type:  1从接口中获取的sysUserId修改   2进店铺时sysUserId修改
export function saleChannelDealAbout(sysUserId, type = 1) {
	if (isRef(sysUserId)) {
		watch(
			() => sysUserId.value,
			() => {
				if (sysUserId.value) {
					if (type == 1) setSaleChannelStoreInfo().setSysUserId(sysUserId.value || '');
					if (type == 2) setSaleChannelStoreInfo().setIntoStoreForSysUserId(sysUserId.value || '');
				}
			}, {
				immediate: true,
			}
		);
	}
	onUnload(() => {
		if (sysUserId.value) {
			if (type == 1) setSaleChannelStoreInfo().setSysUserId('');
			if (type == 2) setSaleChannelStoreInfo().setIntoStoreForSysUserId('');
		}
	});
}

//广告点击
export function adNavTo(item) {
	if (item.goToType == 1) {
		//商品详情
		navToGoodDetail(item);
	} else if (item.goToType == 3) {
		//发现详情
		navTo(`/pages/findDetail/findDetail?id=${item.marketingMaterialListId}`);
	} else if (item.goToType == 2 && item.pictureDetails) {
		//详情图
		navTo(`/pages/piDetShow/piDetShow?pictureDetails=${item.pictureDetails}&title=${item.title}`);
	} else if (item.goToType == 4) {
		//封坛礼包跳转
		navTo(`/pages/ftGiftBagList/ftGiftBagList?sysUserId=${item.sysUserId}`);
	}
}

//监听经纬度变化,变化后重新dosomething
export function addeventLocalTodo(fc) {
	watch(
		() => locationInfo().info,
		() => {
			fc && fc();
		}, {
			deep: true,
		}
	);
}

//如果未登录，未登录并且要跳转到登陆页面，记录上一个页面并且登陆后能返回上一个页面的逻辑
export function toLoginAndBackPage() {
	let pages = getCurrentPages();
	let currentRoute = pages[pages.length - 1].route; //页面路径
	let currentFullPath = pages[pages.length - 1]['$page']['fullPath']; //页面路径(带参数)
	const recordFullPagePathStore = recordFullPagePath();
	//如果当前页面不在登录页 则记录当前页面信息后跳转登录页
	if (currentRoute.indexOf('login') == -1) {
		recordFullPagePathStore.setPageFullPath(currentFullPath);
		uni.reLaunch({
			url: '/packageSearch/pages/login/login',
		});
	}
}