import {
	ref
} from 'vue';
import {
	navTo
} from '@/hooks';
import {
	userStore
} from '@/store/index.js';
export const cartCountResult = ref(0)
export function cartCountHooks() {
	async function cartCountRefresh() {
		const users = userStore();
		if (userStore().token) {
			let {
				data: countRes
			} = await uni.http.get(uni.api.findCarGoods);
			cartCountResult.value = countRes.result
		}
	}
	return {
		cartCountRefresh
	}
}