import {
	nextTick
} from "vue";
import {
	ref,
	computed
} from "vue";
import {
	goPayDeal
} from '@/hooks'

export function cashier() {
	const info = ref({})
	const fn = {
		failCallback: '',
		callback: ''
	}
	const options = ref('')
	//结合payClassic使用   开始-----
	//请求页面的接口 
	const payApiList = [
		uni.api.submitOrder, //默认 商品
		uni.api.submitMarketingGiftBag, //礼包
		uni.api.toCashierDeskMarketingStoreGiftbag, //礼包团
		uni.api.balanceToCashierDesk, //余额充值
		uni.api.unpaidOrderSubmit, //待支付订单
		uni.api.submitCertificate, //兑换券支付
		uni.api.toCashierDeskMarketingStoreGiftCard //批发卡充值
	]
	//最终支付的接口
	const toPayedApiList = [
		uni.api.payOrderCarLog, //默认 商品
		uni.api.payMarketingGiftBagRecord, //礼包
		uni.api.payMarketingStoreGiftbag, //礼包团
		uni.api.blancePay, //余额充值
		uni.api.payOrderCarLog, //待支付订单
		uni.api.payCertificate, //兑换券支付
		uni.api.payMarketingStoreGiftCard //批发卡充值
	]
	//获取日志的键名
	const getPayOrderCarLogIdKeyList = [
		'payOrderCarLogId', //默认 商品
		'payGiftBagLogId', //礼包
		'payLogId', //礼包团
		'payBalanceLogId', //余额充值
		'payOrderCarLogId', //待支付订单
		'payCertificateLogId', //兑换券支付
		'payLogId', //批发卡支付
	]
	//结合payClassic使用   结束-----
	//日志id
	const payOrderCarLogId = computed(() => {
		if (!info.value.memberListId) return ''
		return info.value[getPayOrderCarLogIdKeyList[options.value.payClassic || 0]]
	})
	async function payResult(
		infoRes,
		again = false,
		callback = '',
		failCallback = ''
	) {
		fn.failCallback = failCallback
		fn.callback = callback
		if (again || !options.value) {
			let apiUrl = uni.api.submitOrder //默认商品支付
			if (infoRes.payClassic) {
				apiUrl = payApiList[infoRes.payClassic]
			}
			let {
				data
			} = await uni.http.post(apiUrl, {
				...infoRes
			})
			info.value = data.result;
			options.value = {
				...infoRes
			}
			await nextTick()
		}
		toPay()
	}

	//支付
	async function toPay() {
		try {
			let apiUrl = uni.api.payOrderCarLog;
			if (options.value.payClassic) {
				apiUrl = toPayedApiList[options.value.payClassic]
			}
			const allBalancePay = options.value.payClassic == '4' || options.value.payClassic == '0'
			let reqInfo = {
				payOrderCarLogId: payOrderCarLogId.value,
				payModel: 0, //，0：微信支付；1：支付宝支付；
				memberListId: info.value.memberListId,
				balance: allBalancePay ? info.value.allTotalPrice : '0',
				price: allBalancePay ? '0' : info.value.allTotalPrice,
				marketingStoreGiftCardMemberListId: options.value.marketingStoreGiftCardMemberListId || ''
			};
			let {
				data
			} = await uni.http.post(apiUrl, reqInfo);
			goPayDeal({
				data,
				id: options.value.id || ''
			}, options.value.payClassic, fn.callback, fn.failCallback)
		} catch (error) {
			if (typeof fn.failCallback === 'function') {
				fn.failCallback()
			}
			//TODO handle the exception
		}

	}
	return {
		payResult
	}
}