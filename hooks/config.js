/**
 * 环境配置文件
 * 根据微信小程序的运行模式自动选择对应环境
 * - 开发版和体验版使用测试环境配置
 * - 正式版使用生产环境配置
 */

// 环境类型常量
const ENV_TYPE = {
	PRODUCTION: 0, // 生产环境
	TEST: 1        // 测试环境
}

// 配置列表
const CONFIG_LIST = [
	// 生产环境配置
	{
		ENV_NAME: '生产环境',
		APP_NAME: '八闽助业集市', // 小程序名称
		APP_COMPANY: '福建兴禾电子商务有限公司', // 公司名称
		WX_MINI_APP_ID: 'wx8bb02b064cee39b4', // 微信小程序APPID
		WX_MINI_ORIGINAL_ID: 'gh_0ca485c04f2f', // 微信小程序原始ID
		OFFICIAL_WEBSITE_ADDRESS: 'https://app.gongkeshop.com', // 官网地址
		WX_MINI_SHARE_TYPE: 0, // 小程序的类型（包括分享） 微信小程序版本类型，可取值： 0-正式版； 1-测试版； 2-体验版。 默认值为0。
		IMAGE_URL: 'https://api.gongkeshop.com/heartful-mall-api/sys/common/view/', // 生产环境图片地址
		REQUEST_URL: 'https://api.gongkeshop.com/heartful-mall-api/', // 生产环境请求地址
		CLEAR_SHARE_CACHE_TIME: 30 // 单位分钟 进入后台总时长超过多久则清除请求头部的分享相关信息
	},
	// 测试环境配置
	{
		ENV_NAME: '测试环境',
		APP_NAME: '八闽助业集市', // 小程序名称
		APP_COMPANY: '福建兴禾电子商务有限公司', // 公司名称
		WX_MINI_APP_ID: 'wx8bb02b064cee39b4', // 微信小程序APPID
		WX_MINI_ORIGINAL_ID: 'gh_0ca485c04f2f', // 微信小程序原始ID
		OFFICIAL_WEBSITE_ADDRESS: 'http://app.gongkeshop.com', // 官网地址
		WX_MINI_SHARE_TYPE: 0, // 小程序的类型（包括分享） 微信小程序版本类型，可取值： 0-正式版； 1-测试版； 2-体验版。 默认值为0。
		IMAGE_URL: 'https://www.zehuaiit.top/heartful-mall-api/sys/common/view/', // 测试环境图片地址
		REQUEST_URL: 'https://www.zehuaiit.top/heartful-mall-api/', // 测试环境请求地址
		CLEAR_SHARE_CACHE_TIME: 30 // 单位分钟 进入后台总时长超过多久则清除请求头部的分享相关信息
	}
]

// 根据微信小程序运行环境自动选择配置
let CURRENT_ENV;

// #ifdef MP-WEIXIN
// 获取微信小程序运行环境
const accountInfo = uni.getAccountInfoSync();
const envVersion = accountInfo.miniProgram.envVersion;

// 根据环境版本选择配置
// develop: 开发版
// trial: 体验版
// release: 正式版
if (envVersion === 'release') {
	// 正式版使用生产环境配置
	CURRENT_ENV = ENV_TYPE.PRODUCTION;
	console.log('当前为小程序正式版，使用生产环境配置');
} else {
	// 开发版和体验版使用测试环境配置
	CURRENT_ENV = ENV_TYPE.TEST;
	console.log(`当前为小程序${envVersion === 'develop' ? '开发版' : '体验版'}，使用测试环境配置`);
}
// #endif

// 如果不是微信小程序或无法获取环境信息，默认使用生产环境
if (typeof CURRENT_ENV === 'undefined') {
	CURRENT_ENV = ENV_TYPE.PRODUCTION;
	console.log('无法确定运行环境，默认使用生产环境配置');
}

// 设置全局环境配置
uni.env = CONFIG_LIST[CURRENT_ENV];

// 输出当前环境信息到控制台，方便调试
console.log(`当前环境: ${uni.env.ENV_NAME}`);
console.log(`API地址: ${uni.env.REQUEST_URL}`);

// 导出环境类型常量，方便在其他地方使用
export const ENV = ENV_TYPE;