<template>
	<view class="numberInput">
		<image src="@/static/cart/reduce.png" @click="reduce"></image>
		<input type="number" v-model="num" @blur='blur' :disabled="disabled"> </input>
		<image src="@/static/cart/add.png" @click="add"></image>
	</view>
</template>

<script setup lang="ts">
	import { watch } from 'vue';

	const num = defineModel<number>({ required: true, default: 0 })
	const emits = defineEmits(['change'])
	const props = defineProps({
		min: {
			type: Number,
			default: 1
		},
		max: {
			type: Number,
			default: 999
		},
		disabled: {
			type: Boolean,
			default: false
		}
	})
	function add() {
		if (num.value >= props.max) return;
		num.value += 1
	}
	function reduce() {
		if (num.value <= props.min) return;
		num.value -= 1
	}

	function blur(e) {
		const val = e.detail.value
		if (val < props.min) {
			num.value = props.min
		}
		if (val > props.max) {
			num.value = props.max
		}
	}

	watch(num, () => {
		emits('change', num.value)
	})
</script>

<style lang="scss">
	.numberInput {
		display: flex;
		align-items: center;
		justify-content: center;

		>image {
			width: 40rpx;
			height: 40rpx;
		}

		>input {
			width: 90rpx;
			height: 30rpx;
			line-height: 30rpx;
			color: #333333;
			font-size: 28rpx;
			text-align: center;
			margin: 0 10rpx;
		}

	}
</style>