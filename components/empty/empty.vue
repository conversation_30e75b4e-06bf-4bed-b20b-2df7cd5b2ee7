<template>
	<view class="empty">
		<image :src="imgSrc" mode="widthFix"></image>
		<view>
			{{props.text}}
		</view>
	</view>
</template>

<script setup>
	const props = defineProps({
		text: {
			type: String,
			default: "空空如也~"
		},
		imgSrc:{
			type:String,
			default:'/static/empty.png'
		}
	})
</script>

<style lang="scss">
	.empty {
		width: 100%;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		color: #A0A0A0;
		text-align: center;
		padding-top: 100rpx;
		>image{
			width: 260rpx;
			margin-bottom: 16rpx;
		}
	}
</style>
