<template>
	<view class='triangle'>

	</view>
</template>

<script setup>
	import {
		computed
	} from 'vue';

	const props = defineProps({
		color: {
			type: String,
			default: '#999999'
		}
	})
	const borderBottom = computed(() => {
		return `10rpx solid ${props.color}`
	})
</script>

<style lang="scss">
	.triangle {
		width: 0;
		height: 0;
		border-left: 10rpx solid transparent;
		border-right: 10rpx solid transparent;
		border-bottom: v-bind(borderBottom);
		/* 三角形的颜色 */
	}
</style>