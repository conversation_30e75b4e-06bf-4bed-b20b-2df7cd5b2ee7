<template>
	<uni-popup ref='popup' type="center" @change='popupShowChange'>
		<view class="payAboutModal" v-if="type !== 2">
			<image class="payAboutModal-img" src="@/static/payAboutModal/star.png" mode=""></image>
			<template v-if="type === 1">
				<view class="payAboutModal-text">
					<view>
						使用<text style="color: #F54E01;font-weight: bold;">{{info.allTotalPrice}}</text>助力值
					</view>
					<view>
						感谢您的奉献
					</view>
				</view>
				<view class="payAboutModal-ope">
					<view @click="close">
						取消
					</view>
					<view @click='buy'>
						立即兑换
					</view>
				</view>
			</template>
			<template v-else>
				<view class="payAboutModal-text">
					<view>
						您的助力值不足哦
					</view>
					<view>
						快去助力我们的梦想吧！
					</view>
				</view>
				<view class="payAboutModal-ope">
					<view @click="close">
						再看看
					</view>
					<view @click='toRecharge'>
						去贡献
					</view>
				</view>
			</template>

			<view class="payAboutModal-desc">
				当前助力值:{{users?.userInfo?.balance || 0}}
			</view>
			<view class="payAboutModal-descSec" @click="navToDreamHelper">
				成为助梦家 点亮梦想灯>
			</view>
		</view>


		<view class="buySuccess" v-else>
			<image class="buySuccess-banner" :src="imgUrl + '/static/payAboutModal/banner.png'" mode="">
			</image>
			<view class="buySuccess-container">

				<view class="buySuccess-container-ct">
					<view class="buySuccess-container-ct-top">
						<view class="buySuccess-container-ct-top-name">
							助力成功
						</view>
						<view class="buySuccess-container-ct-top-right">
							<!-- 证书编号 <text style="color: #22A3FF;">12398422943</text> -->
						</view>
					</view>
					<view class="buySuccess-container-ct-user">
						<image class="buySuccess-container-ct-user-img"
							:src="users.userInfo.avatarUrl || '/static/missing-face.png'" mode=""></image>
						<view class="buySuccess-container-ct-user-name">
							{{users.userInfo.nickName}}
						</view>
						<view>
							谢谢您:
						</view>
					</view>
					<view class="buySuccess-container-ct-desc">
						“您的善举让我深受感动，感谢您伸出援手，您的爱心将被永远铭记。”
					</view>
					<view class="buySuccess-container-ct-time">
						<view style='margin-bottom: 10rpx;'>
							助力时间
						</view>
						<view>
							{{getCurrentDate()}}
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="closeIcon" v-if="type === 2">
			<uni-icons @click="close" color="white" type="close" size="30" />
		</view>

	</uni-popup>

</template>

<script setup>
	import {
		ref
	} from 'vue';
	import {
		userStore
	} from '@/store/index.js'
	import {
		navTo
	} from '@/hooks'
	const emits = defineEmits(['toBuy'])
	const users = userStore()
	const info = ref({})
	const popup = ref()
	//1购买提示弹窗  2购买成功弹窗 3购买失败弹窗
	const type = ref(1)
	const imgUrl = uni.env.IMAGE_URL
	// 是否是点击"成为助梦家"按钮触发的关闭
	const isDreamHelperClick = ref(false)

	function getCurrentDate() {
		const date = new Date();
		const year = date.getFullYear();
		const month = (date.getMonth() + 1).toString().padStart(2, '0'); // 月份从0开始，需加1，并确保是两位
		const day = date.getDate().toString().padStart(2, '0'); // 确保是两位数字
		return `${year}年${month}月${day}日`;
	}

	function toRecharge() {
		uni.navigateTo({
			url: '/packageRecharge/pages/recharge/recharge'
		})
	}

	function open(e, t = 1) {
		info.value = e
		type.value = t
		popup.value.open()
	}

	function close() {
		popup.value.close()
	}

	function popupShowChange({
		show
	}) {
		if (!show) {
			if (isDreamHelperClick.value) {
				// 如果是点击"成为助梦家"按钮触发的关闭，重置标记
				isDreamHelperClick.value = false
				// 跳转到助梦家权益页面的逻辑已在navToDreamHelper中处理
			} else if (type.value === 2) {
				// 如果是支付成功弹窗关闭，跳转到订单列表页面
				console.log('支付成功弹窗关闭，跳转到订单列表页面')
				// 使用setTimeout确保弹窗完全关闭后再跳转
				setTimeout(() => {
					uni.redirectTo({
						url: '/packageOrder/pages/myPackage/myPackage',
						fail: (err) => {
							console.error('跳转订单列表失败:', err)
							// 如果跳转失败，尝试使用switchTab
							uni.switchTab({
								url: '/pages/index/index'
							})
						}
					})
				}, 300)
			}
		}
	}

	function buy() {
		emits('toBuy')
		close()
	}

	function navToDreamHelper() {
		// 设置标记，表示是点击"成为助梦家"按钮触发的关闭
		isDreamHelperClick.value = true
		close()
		// 延迟执行跳转，确保弹窗完全关闭后再跳转
		setTimeout(() => {
			navTo('/pages/productionAbout/productionAbout')
		}, 100)
	}
	defineExpose({
		open,
		close,
		buy
	})
</script>

<style lang="scss">
	.closeIcon {
		margin: 0 auto;
		width: fit-content;
		margin-top: 200rpx;
	}

	.buySuccess {
		width: 640rpx;
		border-radius: 60rpx;
		overflow: hidden;
		position: relative;


		&-banner {
			width: 100%;
			height: 280rpx;
			position: relative;
			z-index: 2;
		}


		&-container {
			width: 100%;
			position: relative;
			z-index: 1;
			top: -70rpx;
			border-radius: 0 0 60rpx 60rpx;
			overflow: hidden;

			&-ct {
				background-color: white;
				padding: 20rpx 30rpx;
				padding-top: 82rpx;

				&-time {
					display: flex;
					flex-direction: column;
					align-items: flex-end;
					text-align: right;
					font-size: 28rpx;
					color: #333333;
				}

				&-desc {
					font-size: 28rpx;
					line-height: 40rpx;
					color: #666666;
				}

				&-user {
					display: flex;
					align-items: center;
					font-size: 28rpx;
					color: #666666;

					&-img {
						width: 62rpx;
						height: 62rpx;
						margin-right: 14rpx;
					}

					&-name {
						color: #333333;
						margin-right: 14rpx;
					}
				}

				&-top {
					display: flex;
					align-items: center;
					justify-content: space-between;

					&-name {
						font-size: 30rpx;
						color: #333333;
					}

					&-right {
						font-size: 28rpx;
						color: #666666;
					}
				}

				>view {
					margin-bottom: 20rpx;
				}
			}
		}
	}


	.payAboutModal {
		width: 590rpx;
		padding: 30rpx 0;
		background: linear-gradient(180deg, #d7f3ff, #ffffff 100%);
		border-radius: 40rpx;
		position: relative;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;

		>view,
		>image {
			margin-bottom: 20rpx;
		}

		>view:last-child {
			margin-bottom: 0;
		}

		&-img {
			width: 220rpx;
			height: 220rpx;
			position: relative;
			margin-top: -140rpx;
			z-index: 2;
		}

		&-text {
			font-size: 30rpx;
			line-height: 48rpx;
			color: #333333;
			text-align: center;
		}

		&-desc {
			color: #999999;
			font-size: 26rpx;
		}

		&-descSec {
			color: #F54E01;
			font-size: 26rpx;
		}

		&-ope {
			display: flex;
			align-items: center;
			justify-content: center;

			>view {
				width: 219rpx;
				height: 80rpx;
				background: #22a3ff;
				border-radius: 16rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 28rpx;
				margin-right: 20rpx;
			}

			>view:last-child {
				margin-right: 0;
			}
		}
	}
</style>