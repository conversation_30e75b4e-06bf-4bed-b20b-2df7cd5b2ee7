<template>
	<view class="cartBtn">
		<uni-badge size="small" :text="cartCountResult.toString()" absolute="rightTop" :offset="[20,10]" type="error" :show-zero="true">
			<image class="cartBtn-img" src="@/static/cart/cart.png" @click="navTo('/pages/cart/cart')"></image>
		</uni-badge>

	</view>
</template>

<script setup>
	import {
		cartCountResult,
		cartCountHooks
	} from '@/hooks/getCartCount.js'
	import {
		navTo
	} from '@/hooks'
	const {
		cartCountRefresh
	} = cartCountHooks()
	cartCountRefresh()
</script>

<style lang="scss">
	.cartBtn {
		position: fixed;
		right: 10rpx;
		z-index: 20;
		bottom: 200rpx;

		&-img {
			width: 120rpx;
			height: 120rpx;
		}
	}
</style>