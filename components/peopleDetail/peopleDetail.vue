<template>
	<view class="peopleDetail-outWrap">
		<view class="peopleDetail">
			<view class="peopleDetail-pic">
				<image :src="imgUrl + storeInfo.logoAddr" mode="aspectFill"></image>
			</view>

			<view class="peopleDetail-det">
				<view class="peopleDetail-det-title">
					{{storeInfo.storeName}}
				</view>
				<view class="peopleDetail-det-desc">
					{{storeInfo.introduce}}
				</view>
				<view class="peopleDetail-det-dz">
					创业积分:{{storeInfo.totalPerformance}}
				</view>
				<view class="peopleDetail-det-btn" @click="navToStoreIndex(storeInfo)">
					立即助力
				</view>
			</view>

		</view>
		<template v-if="storeInfo.storeGoods?.length">
			<view class="peopleDetail-driver">

			</view>
			<scroll-view scroll-x="true" class="peopleDetail-goods">
				<view class="peopleDetail-goods-outView">
					<view class="peopleDetail-goods-outView-ct" v-for="(item,index) in storeInfo.storeGoods"
						:key="item.id" @click="navToGoodDetail(item)">
						<image class="peopleDetail-goods-outView-ct-img"
							:src="imgUrl + parseImgurl(item.mainPicture)?.[0]" mode=""></image>
						<view class="peopleDetail-goods-outView-ct-text">
							{{item.goodName}}
						</view>
					</view>
				</view>
			</scroll-view>
		</template>

	</view>

</template>

<script lang="ts" setup>
	import { navTo, navToStoreIndex, navToGoodDetail } from '@/hooks';
	import {
		parseImgurl
	} from '@/utils'
	const imgUrl = uni.env.IMAGE_URL
	const props = defineProps({
		storeInfo: {
			type: Object,
			default() {
				return {}
			}
		}
	})
</script>

<style lang="scss">
	.peopleDetail {

		display: flex;
		align-items: center;

		&-driver {
			width: 100%;
			height: 2rpx;
			background-color: #F5F5F5;
			margin: 20rpx 0;
		}


		&-outWrap {
			min-height: 360rpx;
			padding: 12rpx;
			background: #feffff;
			border-radius: 16rpx;
			position: relative;
			z-index: 4;
			// margin-bottom: 20rpx;
		}

		&-goods {
			white-space: nowrap;
			width: 100%;
			// margin-top: 20rpx;

			&-outView {


				display: flex;
				align-items: center;

				&-ct {
					width: 110rpx;

					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: space-between;
					margin-right: 20rpx;

					&-img {
						border: 2rpx solid #f5f5f5;
						border-radius: 8rpx;
						width: 110rpx;
						height: 110rpx;
						margin-bottom: 10rpx;
					}

					&-text {
						width: 110rpx;
						font-size: 20rpx;
						line-height: 24rpx;
						height: 24rpx;
						text-align: center;
						color: #666666;
						white-space: nowrap;
						/* 禁止换行 */
						overflow: hidden;
						/* 隐藏溢出内容 */
						text-overflow: ellipsis;
						/* 显示省略符号 */
					}
				}
			}
		}

		&-pic {
			width: 256rpx;
			height: 331rpx;
			border-radius: 16rpx;
			overflow: hidden;
			border: 2rpx solid #f5f5f5;
			display: flex;
			align-items: center;
			justify-content: center;

			>image {
				width: 100%;
				height: 100%;
			}
		}

		&-det {
			height: 331rpx;
			padding: 0 20rpx;
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: space-between;

			&-title {
				color: #333333;
				font-size: 28rpx;
				padding-top: 10rpx;
				// margin-bottom: 16rpx;
			}

			&-desc {
				width: 100%;
				height: 110rpx;
				font-size: 24rpx;
				line-height: 28rpx;
				// margin-bottom: 25rpx;
				color: #999999;
				overflow: hidden;
				/* 隐藏超出部分 */
				display: -webkit-box;
				/* 使用 Webkit 的盒子模型 */
				-webkit-box-orient: vertical;
				/* 垂直排列子元素 */
				-webkit-line-clamp: 4;
				/* 限制显示的行数 */
				text-overflow: ellipsis;
				/* 末尾显示省略号 */
			}

			&-btn {
				width: 260rpx;
				height: 60rpx;
				background: #22a3ff;
				border-radius: 100rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				color: white;
				font-size: 24rpx;
				margin: 0 auto;
			}

			&-dz {
				display: flex;
				align-items: center;
				justify-content: center;
				// margin-bottom: 25rpx;
				font-size: 24rpx;
				color: #666666;

			}
		}
	}
</style>