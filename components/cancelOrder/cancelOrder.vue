<template>
	<view class="cancelOrder">
		<view class="cancelOrder-title">
			<view>
				请选择{{allText.title}}原因
			</view>
			<image src="@/static/closeIcon.png" mode="" @click="emits('close')"></image>
		</view>
		<!-- <view class="cancelOrder-attention">
			<view>
				温馨提示
			</view>
			<view v-for="(item,index) in allText.attenTionList" :key="index">
				{{index + 1}}、{{item}}
			</view>
		</view> -->
		<scroll-view scroll-y="true" class="cancelOrder-allLine">
			<view class="cancelOrder-allLine-item" v-for="(item,index) in list" :key="index"
				@click="changeSelectIndex(index)">
				<view>
					{{item.text || item.name}}
				</view>
				<view>
					<image class="cancelOrder-allLine-item_select" src="@/static/cart/selected.png"
						v-if="index == selectIndex">
					</image>
					<view class="cancelOrder-allLine-item_notSelect" v-else>

					</view>
				</view>
			</view>
		</scroll-view>
		<view class="cancelOrder-btnOpe btnOpe">
			<button class="bottomFixBtn" :loading='btnLoading' :disabled="btnLoading" @click="cancel">
				{{btnText || allText.btnText}}
			</button>
		</view>
	</view>
</template>

<script setup>
	import {
		getSafeBottom
	} from '@/utils';
	import {
		ref,
		computed,
		watch
	} from "vue";
	let list = ref([]);
	let selectIndex = ref(0);
	let reasonType = ref()
	let otherObj = ref()
	let btnLoading = ref(true);
	let reasonTypeResult = computed(() => {
		return reasonType.value || props.type
	})
	const emits = defineEmits(['cancelSuccess', 'close'])
	const props = defineProps({

		type: {
			//1  取消订单  2//仅退款 3//1688退款
			type: [String, Number],
			default: 1
		},
		btnText: {
			type: String,
			default: ''
		}
	})
	const allText = computed(() => {
		if (reasonTypeResult.value == 1 || reasonTypeResult.value == 3) {
			return {
				title: '取消订单',
				attenTionList: [
					'支付福利金取消后将返还；',
					'支付优惠券不予返还；',
					'订单一旦取消，无法恢复。'
				],
				btnText: '取消订单'
			}
		}
		if (reasonTypeResult.value == 2) {
			return {
				title: '退款',
				attenTionList: [
					'支付积分不予返还；',
					'支付优惠券不予返还；',
					'订单一旦退款，无法恢复。'
				],
				btnText: '退款'
			}
		}
	})
	async function refresh() {
		list.value = []
		let code = 'oder_close_explain'
		if (reasonTypeResult.value == 2) {
			code = 'order_refund_reason'
		}
		let apiUrl = `${uni.api.getDicts}?code=${code}`
		if (reasonTypeResult.value == 3) {
			apiUrl = uni.api.getRefundReasonList
		}
		btnLoading.value = true
		let {
			data
		} = await uni.http.get(apiUrl, {
			params: otherObj.value
		});
		list.value = data.result
		btnLoading.value = false
	}
	//选择原因
	function changeSelectIndex(index) {
		selectIndex.value = index
	}
	//取消
	function cancel() {
		if (!list.value[selectIndex.value]) {
			uni.showToast({
				title: '请选择原因',
				icon: 'none'
			})
			return;
		}
		emits('cancelSuccess', list.value[selectIndex.value])
	}
	//改变原因类型
	function changeReasonType(type, o) {
		otherObj.value = o
		reasonType.value = type || props.type
	}

	watch(() => reasonType.value, () => refresh(), {
		immediate: true
	})
	defineExpose({
		changeReasonType
	})
</script>

<style lang="scss">
	.cancelOrder {
		width: 750rpx;
		padding: 32rpx;
		background: #FFFFFF;
		border-radius: 32rpx 32rpx 0 0;
		padding-bottom: 0;
		
		.bottomFixBtn{
			height: 80rpx;
			background: #22a3ff;
			border-radius: 100rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			color: white;
			font-size: 28rpx;
		}

		&-btnOpe {
			position: relative;
			width: 100%;
			padding-left: 0;
			padding-right: 0;
			padding-bottom: v-bind(getSafeBottom());
		}

		&-attention {
			width: 100%;
			background: #F9F9F9;
			border-radius: 16rpx;
			padding: 24rpx;
			font-size: 24rpx;
			color: #333333;
			line-height: 40rpx;
			margin-bottom: 32rpx;
		}

		&-title {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 32rpx;

			>view:nth-child(1) {
				font-weight: bold;
				font-size: 32rpx;
				color: #333333;
			}

			>image:nth-child(2) {
				width: 40rpx;
				height: 40rpx;
			}
		}

		&-allLine {
			width: 100%;
			height: 520rpx;

			&-item {
				width: 100%;
				display: flex;
				align-items: center;
				justify-content: space-between;
				margin-bottom: 40rpx;

				>view:nth-child(1) {
					font-size: 28rpx;
					color: #333333;
				}

				&_notSelect {
					width: 32rpx;
					height: 32rpx;
					border: 1rpx solid #B3B3B3;
					border-radius: 50%;
				}

				&_select {
					width: 32rpx;
					height: 32rpx;
				}
			}
		}
	}
</style>