<template>
	<view class="activityBanner" v-if="(!users.token || (users.token && users.userInfo.isLoveAmbassador != 1)) && !fromDreamHelper">
		<view class="activityBanner-left">
			成为助梦家，点亮梦想灯
		</view>
		<view class="activityBanner-right" @click="navToBeforeLogin('/pages/productionAbout/productionAbout')">
			立即成为
		</view>
	</view>
</template>

<script setup>
	import {
		userStore
	} from '@/store';
	import {
		navToBeforeLogin
	} from '@/hooks';
	import { ref, onMounted } from 'vue';
	const users = userStore()
	
	// 是否从助梦家介绍页进入
	const fromDreamHelper = ref(false)
	
	onMounted(() => {
		// 获取当前页面路由参数
		const pages = getCurrentPages()
		const currentPage = pages[pages.length - 1]
		if (currentPage && currentPage.options) {
			// 检查是否有fromDreamHelper参数
			fromDreamHelper.value = currentPage.options.fromDreamHelper === 'true'
		}
	})
</script>

<style lang="scss">
	.activityBanner {
		min-width: 690rpx;
		height: 100rpx;
		background: linear-gradient(97deg, #f34949 0%, #ff7b7b 33%, #ffa0a0 67%, #f77f7f 100%);
		border-radius: 16px;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;

		&-left {
			font-size: 28rpx;
			color: white;
		}

		&-right {
			width: 154rpx;
			height: 64rpx;
			background: linear-gradient(90deg, #fff7f7 0%, #ffd7d7 100%);
			border-radius: 100rpx;
			box-shadow: 0rpx 4rpx 10rpx 0rpx rgba(207, 57, 57, 0.25);
			display: flex;
			align-items: center;
			justify-content: center;
			color: #F64B4B;
			font-size: 28rpx;
		}
	}
</style>