<template>
  <uni-popup ref="popup" type="bottom" :safeArea="true" :maskClick="true" backgroundColor="rgba(0, 0, 0, 0.7)" @change="handlePopupChange">
    <view class="exchange-goods-popup">
      <view class="exchange-goods-popup-header">
        <text class="exchange-goods-popup-title">选择换货商品</text>
        <view class="exchange-goods-popup-close" @click="close">
          <text class="icon-close">×</text>
        </view>
      </view>

      <scroll-view scroll-y class="exchange-goods-popup-content">
        <view
          v-for="(item, index) in goodsList"
          :key="index"
          class="exchange-goods-popup-item"
          :class="{ 'selected': selectedIndex === index }"
          @click="selectItem(index)"
        >
          <view class="exchange-goods-popup-item-content">
            <image class="exchange-goods-popup-item-image" :src="imgUrl + parseImgurl(item.mainPicture || item.goodMainPicture)[0]" mode="aspectFill"></image>
            <view class="exchange-goods-popup-item-info">
              <view class="exchange-goods-popup-item-name">{{ item.goodName }}</view>
              <view class="exchange-goods-popup-item-spec">规格: {{ item.specification || item.goodSpecification || '无' }}</view>
              <view class="exchange-goods-popup-item-amount">数量: {{ item.amount }}</view>
            </view>
          </view>
          <view class="exchange-goods-popup-item-select" :class="{ 'selected': selectedIndex === index }">
            <view class="select-icon" v-if="selectedIndex === index">✓</view>
          </view>
        </view>
      </scroll-view>

      <view class="exchange-goods-popup-footer">
        <button class="exchange-goods-popup-confirm" @click="confirm">确定</button>
      </view>

      <!-- 底部安全区域 -->
      <view class="exchange-goods-popup-safe-area"></view>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref } from 'vue';
import { parseImgurl } from '@/utils';

const props = defineProps({
  orderInfo: {
    type: Object,
    default: () => ({})
  },
  goodsList: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['confirm', 'close']);
const popup = ref(null);
const selectedIndex = ref(-1);
const imgUrl = uni.env.IMAGE_URL;

function open() {
  popup.value.open();
}

function close() {
  popup.value.close();
  selectedIndex.value = -1;
  emits('close');
}

// 处理弹窗状态变化
function handlePopupChange(e) {
  // 简化处理方式，只发送状态变化事件
  if (e.show) {
    // 弹窗打开时，发送自定义事件通知页面
    uni.$emit('popup-opened', {
      type: 'exchangeGoods',
      timestamp: Date.now() // 添加时间戳确保事件唯一性
    });
  } else {
    // 弹窗关闭时，发送自定义事件
    uni.$emit('popup-closed', {
      type: 'exchangeGoods',
      timestamp: Date.now() // 添加时间戳确保事件唯一性
    });
  }
}

function selectItem(index) {
  // 如果点击的是已选中的商品，则取消选中
  if (selectedIndex.value === index) {
    selectedIndex.value = -1;
  } else {
    selectedIndex.value = index;
  }
}

function confirm() {
  if (selectedIndex.value === -1) {
    uni.showToast({
      title: '请选择商品',
      icon: 'none'
    });
    return;
  }

  emits('confirm', props.goodsList[selectedIndex.value], selectedIndex.value);
  close();
}

defineExpose({
  open,
  close
});
</script>

<style lang="scss">
.exchange-goods-popup {
  width: 100%;
  background-color: #fff;
  border-radius: 24rpx 24rpx 0 0;
  overflow: hidden;

  &-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30rpx;
    border-bottom: 1rpx solid #f5f5f5;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      left: 50%;
      top: 16rpx;
      transform: translateX(-50%);
      width: 60rpx;
      height: 6rpx;
      background-color: #D8D8D8;
      border-radius: 3rpx;
    }
  }

  &-title {
    font-size: 32rpx;
    font-weight: 500;
    color: #333;
    margin-top: 16rpx;
  }

  &-close {
    width: 44rpx;
    height: 44rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 16rpx;

    .icon-close {
      font-size: 36rpx;
      color: #999;
      font-weight: 300;
    }
  }

  &-content {
    max-height: 700rpx;
    padding: 0 30rpx;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;
  }

  &-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx;
    border-bottom: 1rpx solid #f5f5f5;
    background-color: #FFFFFF;
    margin: 16rpx 0;
    border-radius: 12rpx;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
    position: relative;
    overflow: hidden;
    box-sizing: border-box;
    width: 100%;

    &.selected {
      background-color: #F0F7FF;
      border: 1rpx solid #D6E5FF;
      box-sizing: border-box;
    }

    &-content {
      display: flex;
      flex: 1;
      max-width: calc(100% - 70rpx);
      box-sizing: border-box;
    }

    &-image {
      width: 160rpx;
      height: 160rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
    }

    &-info {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }

    &-name {
      font-size: 28rpx;
      color: #333;
      margin-bottom: 10rpx;
      line-height: 1.4;
    }

    &-spec, &-amount {
      font-size: 24rpx;
      color: #666;
      margin-bottom: 6rpx;
    }

    &-select {
      width: 40rpx;
      height: 40rpx;
      border-radius: 50%;
      border: 1rpx solid #DDDDDD;
      background-color: #FFFFFF;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: 20rpx;
      margin-right: 10rpx;
      flex-shrink: 0;

      &.selected {
        border-color: #1A69D1;
        background-color: #1A69D1;
      }

      .select-icon {
        color: #fff;
        font-size: 20rpx;
      }
    }
  }

  &-footer {
    padding: 30rpx 30rpx 20rpx;
  }

  &-safe-area {
    height: 20rpx;
    width: 100%;
    background-color: #fff;
  }

  &-confirm {
    height: 90rpx;
    line-height: 90rpx;
    text-align: center;
    background-color: #1A69D1;
    color: #fff;
    border-radius: 45rpx;
    font-size: 32rpx;
    font-weight: 500;
    box-shadow: 0 4rpx 8rpx rgba(26, 105, 209, 0.2);
  }
}
</style>
