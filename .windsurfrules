# 📚 UniApp小程序项目开发规范

## 📋 目录

1. [项目结构规范](#1-项目结构规范)
2. [编码风格规范](#2-编码风格规范)
3. [组件开发规范](#3-组件开发规范)
4. [状态管理规范](#4-状态管理规范)
5. [网络请求规范](#5-网络请求规范)
6. [路由与页面管理规范](#6-路由与页面管理规范)
7. [条件编译规范](#7-条件编译规范)
8. [性能优化规范](#8-性能优化规范)
9. [安全规范](#9-安全规范)
10. [工程化与协作规范](#10-工程化与协作规范)

## 1. 项目结构规范

### 1.1 目录命名与组织方式

#### 规则描述
- 使用小写字母命名目录，多个单词使用连字符（-）连接
- 按功能模块组织文件，而非按文件类型
- 保持目录结构扁平化，避免过深的嵌套

#### 正确示例
```
heartful-mall-app/
├── components/       # 组件目录
│   ├── cart-action/  # 购物车操作组件
│   └── pay-modal/    # 支付弹窗组件
├── pages/            # 页面目录
│   ├── index/        # 首页
│   └── user/         # 用户页面
├── static/           # 静态资源
│   ├── images/       # 图片资源
│   └── icons/        # 图标资源
└── hooks/            # 公共逻辑钩子
```

#### 错误示例
```
heartful-mall-app/
├── Components/       # 大写开头（错误）
├── pages/
│   └── UserPages/    # 大写开头（错误）
│       └── Login/    # 大写开头（错误）
│           └── components/ # 组件嵌套过深（错误）
└── assets/images/icons/ # 嵌套过深（错误）
```

#### 规则依据
扁平化的目录结构和一致的命名规范可以减少开发者的认知负担，提高代码的可维护性和可读性。

### 1.2 特殊目录用途说明

| 目录名称 | 用途说明 | 注意事项 |
|---------|---------|---------|
| `pages` | 存放所有页面文件 | 必须在 pages.json 中注册 |
| `components` | 存放自定义组件 | 按功能分子目录 |
| `static` | 存放静态资源 | 不会被编译处理 |
| `uni_modules` | 存放uni扩展组件 | 不要手动修改其内容 |
| `hooks` | 存放可复用逻辑 | 按功能分文件 |
| `store` | 存放Pinia状态管理 | 按功能模块拆分 |
| `utils` | 存放工具函数 | 按功能分类 |

### 1.3 静态资源管理策略

#### 规则描述
- 图片资源统一放置在 `static/images` 目录下
- 图标资源优先使用字体图标或SVG图标
- 大型静态资源（如视频）应考虑CDN存储
- 按功能或模块对静态资源进行分类

#### 正确示例
```
static/
├── images/
│   ├── common/      # 通用图片
│   ├── product/     # 商品相关图片
│   └── user/        # 用户相关图片
├── icons/           # 图标资源
└── fonts/           # 字体资源
```

#### 错误示例
- 将大量图片直接放在 static 根目录
- 在组件目录中存放图片资源
- 使用绝对路径引用外部资源（不利于迁移）

#### 规则依据
合理的静态资源管理可以提高应用加载速度，减少包体积，便于资源复用和维护。

### 1.4 组件库组织方式

#### 规则描述
- 全局通用组件放在 `components` 目录下
- 页面专用组件放在对应页面目录下的 `components` 子目录
- 第三方组件统一放在 `uni_modules` 目录下
- 组件目录使用单数形式命名，如 `button` 而非 `buttons`

#### 正确示例
```
components/
├── empty/           # 空状态组件
│   ├── empty.vue
│   └── README.md    # 组件说明文档
├── number-input/    # 数字输入组件
└── pay-modal/       # 支付弹窗组件

pages/product-detail/
└── components/      # 页面专用组件
    └── spec-selector/ # 规格选择器
```

#### 错误示例
- 全局组件和页面组件混合存放
- 组件文件直接放在 components 目录下，没有独立子目录
- 组件目录使用复数形式命名

#### 规则依据
清晰的组件组织结构有助于提高组件的可复用性和可维护性，减少重复开发。

## 2. 编码风格规范

### 2.1 命名规范

| 类型 | 规范 | 示例 |
|------|------|------|
| 文件名 | 小写字母，连字符分隔 | `product-detail.vue` |
| 组件名 | PascalCase (在模板中使用) | `<ProductCard />` |
| 变量名 | camelCase | `const userInfo = ref({})` |
| 常量 | 大写，下划线分隔 | `const API_VERSION = 'v1'` |
| 函数名 | camelCase，动词开头 | `function getUserInfo()` |
| CSS类名 | 小写字母，连字符分隔 | `.nav-bar` |
| Store | camelCase，以Store结尾 | `userStore` |

#### 错误示例
```js
// 错误的变量命名
const UserInfo = ref({})  // 首字母大写
const user_info = ref({}) // 下划线分隔

// 错误的函数命名
function UserInfo() {}    // 首字母大写，不是动词开头
```

#### 规则依据
一致的命名规范可以提高代码的可读性和可维护性，减少团队成员之间的沟通成本。

### 2.2 注释规范

#### 规则描述
- 使用 JSDoc 风格的注释
- 复杂函数必须添加注释，说明功能、参数和返回值
- 复杂逻辑必须添加注释，说明实现思路
- 临时代码必须添加 TODO 注释

#### 正确示例
```js
/**
 * 获取用户信息
 * @param {string} userId - 用户ID
 * @returns {Promise<Object>} 用户信息对象
 */
async function getUserInfo(userId) {
  // 实现逻辑
}

// TODO: 后续需要优化请求逻辑
```

#### 错误示例
```js
// 获取用户信息
function getUserInfo(userId) {
  // 没有详细说明
}
```

#### 规则依据
良好的注释可以帮助其他开发者理解代码的意图和实现方式，提高代码的可维护性。

### 2.3 TypeScript/JavaScript编码规范

#### 规则描述
- 优先使用 ES6+ 语法特性
- 使用 async/await 处理异步逻辑，避免回调地狱
- 使用解构赋值简化代码
- 使用箭头函数保持 this 上下文
- 使用 const 和 let 声明变量，避免使用 var

#### 正确示例
```js
// 使用解构赋值
const { name, age } = userInfo

// 使用箭头函数
const handleClick = () => {
  this.loading = true
}

// 使用 async/await
async function fetchData() {
  try {
    const result = await api.getData()
    return result
  } catch (error) {
    console.error(error)
  }
}
```

#### 错误示例
```js
// 避免使用 var
var name = 'John'

// 避免使用回调函数处理异步
api.getData(function(result) {
  // 回调地狱
})
```

#### 规则依据
现代 JavaScript 语法可以提高代码的可读性和可维护性，减少常见的错误。

### 2.4 Vue组件编写规范

#### 规则描述
- 使用 Vue 3 组合式 API (Composition API)
- 组件属性顺序：name, components, props, emits, setup
- 使用 defineProps 和 defineEmits 定义属性和事件
- 使用 ref 和 reactive 管理响应式状态
- 使用 provide/inject 进行深层组件通信

#### 正确示例
```vue
<script setup>
import { ref, onMounted } from 'vue'

// 定义属性
const props = defineProps({
  title: {
    type: String,
    required: true
  }
})

// 定义事件
const emit = defineEmits(['update', 'delete'])

// 响应式状态
const count = ref(0)

// 生命周期钩子
onMounted(() => {
  console.log('组件已挂载')
})

// 方法
const increment = () => {
  count.value++
  emit('update', count.value)
}
</script>
```

#### 错误示例
```vue
<script>
export default {
  // 选项式 API 不推荐在新项目中使用
  data() {
    return {
      count: 0
    }
  },
  methods: {
    increment() {
      this.count++
    }
  }
}
</script>
```

#### 规则依据
Vue 3 组合式 API 提供了更好的代码组织和复用能力，有助于构建可维护的大型应用。

### 2.5 SCSS/样式编写规范

#### 规则描述
- 使用 SCSS 预处理器
- 遵循 BEM 命名规范（Block-Element-Modifier）
- 使用 scoped 属性限制样式作用域
- 全局样式放在 uni.scss 中
- 避免使用 !important

#### 正确示例
```vue
<template>
  <div class="product-card">
    <div class="product-card__image"></div>
    <div class="product-card__title">{{ title }}</div>
    <div class="product-card__price product-card__price--discount">{{ price }}</div>
  </div>
</template>

<style lang="scss" scoped>
.product-card {
  display: flex;
  flex-direction: column;
  
  &__image {
    width: 100%;
    height: 200px;
  }
  
  &__title {
    font-size: 16px;
    font-weight: bold;
  }
  
  &__price {
    color: $price-color;
    
    &--discount {
      color: $discount-color;
    }
  }
}
</style>
```

#### 错误示例
```vue
<style>
/* 不使用 scoped，可能污染全局样式 */
.card {
  /* 选择器太宽泛 */
  display: flex;
}

.title {
  /* 选择器太宽泛 */
  font-size: 16px !important; /* 避免使用 !important */
}
</style>
```

#### 规则依据
BEM 命名规范和 scoped 样式可以避免样式冲突，提高样式的可维护性和可读性。

## 3. 组件开发规范

### 3.1 组件设计原则

#### 规则描述
- 单一职责原则：一个组件只负责一个功能
- 高内聚低耦合：组件内部逻辑紧密相关，与外部耦合度低
- 可配置性：通过属性配置组件行为，而非硬编码
- 可测试性：组件逻辑应易于测试

#### 正确示例
```vue
<!-- 商品卡片组件 -->
<script setup>
// 单一职责：只负责展示商品信息
const props = defineProps({
  id: String,
  title: String,
  price: Number,
  image: String,
  discount: Number,
  // 可配置性：通过属性控制显示行为
  showDiscount: {
    type: Boolean,
    default: true
  }
})

// 事件向外传递，保持低耦合
const emit = defineEmits(['click', 'addToCart'])

const handleClick = () => {
  emit('click', props.id)
}

const addToCart = () => {
  emit('addToCart', {
    id: props.id,
    title: props.title,
    price: props.price
  })
}
</script>
```

#### 错误示例
```vue
<script setup>
// 错误：组件职责不单一，既处理商品展示又处理购物车逻辑
import { useCartStore } from '@/store/cart'
const cartStore = useCartStore()

const props = defineProps({
  product: Object // 传入整个对象，不明确具体需要哪些属性
})

// 错误：组件内部直接修改全局状态，耦合度高
const addToCart = () => {
  cartStore.addItem(props.product)
  uni.showToast({ title: '已加入购物车' })
}
</script>
```

#### 规则依据
遵循组件设计原则可以提高组件的可复用性、可维护性和可测试性，降低系统的复杂度。

### 3.2 组件通信方式选择标准

| 通信方式 | 适用场景 | 优点 | 缺点 |
|---------|---------|------|------|
| Props | 父组件向子组件传递数据 | 简单直接，数据流向清晰 | 层级深时需要逐层传递 |
| Emits | 子组件向父组件传递事件 | 符合单向数据流原则 | 只能逐层向上传递 |
| Provide/Inject | 跨多层组件传递数据 | 避免props逐层传递 | 数据来源不明确 |
| Pinia Store | 全局状态管理 | 可跨任意组件共享状态 | 可能导致过度使用全局状态 |
| EventBus | 非父子组件通信 | 简化组件间通信 | 事件流向难以追踪 |

#### 正确示例
```js
// 父子组件通信：使用 props 和 emits
// 子组件
const props = defineProps(['item'])
const emit = defineEmits(['update'])

// 多层组件通信：使用 provide/inject
// 父组件
provide('theme', ref('dark'))
// 深层子组件
const theme = inject('theme')

// 全局状态：使用 Pinia
// store/user.js
export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null
  })
})
// 组件中
import { useUserStore } from '@/store/user'
const userStore = useUserStore()
```

#### 规则依据
选择合适的通信方式可以简化组件间的数据流转，提高代码的可维护性和可读性。

## 4. 状态管理规范

### 4.1 Pinia store设计原则

#### 规则描述
- 按业务领域划分store模块
- 每个store只管理单一领域的状态
- 使用 defineStore 创建 store
- 提供清晰的 actions 方法操作状态
- 避免在组件中直接修改 store 状态

#### 正确示例
```js
// store/user.js
import { defineStore } from 'pinia'

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: '',
    isLogin: false
  }),
  getters: {
    hasLogin: (state) => state.isLogin,
    userName: (state) => state.userInfo?.nickName || '未登录'
  },
  actions: {
    setUserInfo(info) {
      this.userInfo = info
      this.isLogin = !!info
    },
    setToken(token) {
      this.token = token
      uni.setStorageSync('token', token)
    },
    clearUserInfo() {
      this.userInfo = null
      this.isLogin = false
    },
    clearToken() {
      this.token = ''
      uni.removeStorageSync('token')
    }
  }
})
```

#### 错误示例
```js
// 错误：store混合了多个不相关的业务领域
export const useGlobalStore = defineStore('global', {
  state: () => ({
    userInfo: null,
    cartItems: [],
    productList: [],
    orderList: []
  })
})

// 组件中错误地直接修改状态
const userStore = useUserStore()
userStore.userInfo = response.data // 错误：应该通过 action 修改
```

#### 规则依据
良好的 store 设计可以提高状态管理的可维护性和可测试性，减少状态管理的复杂度。

### 4.2 状态分类与组织方式

#### 规则描述
- 将状态分为本地状态和全局状态
- 本地状态：组件内部使用 ref/reactive 管理
- 全局状态：使用 Pinia store 管理
- 按业务领域组织 store 文件

#### 状态分类标准

| 状态类型 | 管理方式 | 适用场景 |
|---------|---------|----------|
| 组件UI状态 | ref/reactive | 组件内部UI状态（展开/折叠、激活项等） |
| 页面状态 | ref/reactive | 页面级别状态（表单数据、页面筛选条件等） |
| 共享业务状态 | Pinia store | 多组件共享的业务数据（用户信息、购物车等） |
| 应用配置状态 | Pinia store | 全局配置信息（主题、语言等） |

#### 正确示例
```js
// 组件内本地状态
const isExpanded = ref(false)
const activeTab = ref('info')

// 页面状态
const formData = reactive({
  name: '',
  phone: '',
  address: ''
})

// 全局状态
const userStore = useUserStore()
const cartStore = useCartStore()
```

#### 规则依据
合理的状态分类和组织可以简化状态管理，提高代码的可维护性和性能。

### 4.3 何时使用全局状态vs局部状态

#### 规则描述
- 使用全局状态的情况：
  - 多个不相关组件需要共享的数据
  - 需要持久化的数据
  - 跨页面传递的复杂数据
- 使用局部状态的情况：
  - 组件内部的UI状态
  - 表单数据
  - 临时性数据

#### 决策流程图
```
是否多个组件共享？ → 是 → 是否跨页面？ → 是 → 使用Pinia
                   ↓                  ↓
                   否                  否
                   ↓                  ↓
              仅组件内使用        使用provide/inject
                   ↓
              使用ref/reactive
```

#### 规则依据
合理选择状态管理方式可以避免全局状态的滥用，提高应用性能和代码可维护性。

### 4.4 状态持久化策略

#### 规则描述
- 需要持久化的数据：用户信息、token、应用配置等
- 持久化方式：uni.setStorage/getStorage
- 在 store 的 actions 中处理持久化逻辑
- 应用启动时从存储中恢复状态

#### 正确示例
```js
// store/user.js
export const useUserStore = defineStore('user', {
  state: () => ({
    token: uni.getStorageSync('token') || '',
    userInfo: uni.getStorageSync('userInfo') || null
  }),
  actions: {
    setToken(token) {
      this.token = token
      uni.setStorageSync('token', token)
    },
    setUserInfo(info) {
      this.userInfo = info
      uni.setStorageSync('userInfo', info)
    },
    clearUserData() {
      this.token = ''
      this.userInfo = null
      uni.removeStorageSync('token')
      uni.removeStorageSync('userInfo')
    }
  }
})
```

#### 错误示例
```js
// 错误：在组件中直接处理持久化逻辑
const saveUserInfo = (info) => {
  userStore.userInfo = info
  uni.setStorageSync('userInfo', info) // 应该在store中处理
}
```

#### 规则依据
集中处理状态持久化逻辑可以提高代码的可维护性，避免数据不一致的问题。

## 5. 网络请求规范

### 5.1 API管理与组织

#### 规则描述
- 集中管理API接口地址，避免硬编码
- 按业务模块组织API接口
- 使用常量定义API路径
- 在统一的地方配置API基础路径

#### 正确示例
```js
// api/index.js
export default {
  // 用户相关
  login: '/api/user/login',
  register: '/api/user/register',
  getUserInfo: '/api/user/info',
  
  // 商品相关
  getProductList: '/api/product/list',
  getProductDetail: '/api/product/detail',
  
  // 订单相关
  createOrder: '/api/order/create',
  getOrderList: '/api/order/list'
}

// main.js
import api from './api'
uni.api = api
```

#### 错误示例
```js
// 错误：在组件中硬编码API地址
const login = async () => {
  const res = await uni.http.post('/api/user/login', data) // 硬编码API地址
}
```

#### 规则依据
集中管理API接口可以提高代码的可维护性，当接口地址变更时只需修改一处，避免散落在各处的硬编码地址。

### 5.2 请求封装与配置

#### 规则描述
- 统一封装网络请求方法
- 配置请求拦截器处理请求头、认证信息等
- 配置响应拦截器统一处理错误、数据转换等
- 支持请求取消和超时处理

#### 正确示例
```js
// hooks/request.js
import Request from '@/js_sdk/luch-request/luch-request'

const http = new Request()

// 请求配置
http.setConfig(config => {
  config.baseURL = uni.env.REQUEST_URL
  config.header = {
    'content-type': 'application/x-www-form-urlencoded'
  }
  config.timeout = 60000
  return config
})

// 请求拦截器
http.interceptors.request.use(config => {
  const token = uni.getStorageSync('token')
  if (token) {
    config.header['Authorization'] = `Bearer ${token}`
  }
  return config
})

// 响应拦截器
http.interceptors.response.use(
  response => {
    const { statusCode, data } = response
    
    if (statusCode >= 200 && statusCode < 300) {
      if (data.code === 0) {
        return data
      } else {
        uni.showToast({
          title: data.msg || '请求失败',
          icon: 'none'
        })
        return Promise.reject(data)
      }
    }
    
    return Promise.reject(response)
  },
  error => {
    uni.showToast({
      title: '网络异常，请稍后再试',
      icon: 'none'
    })
    return Promise.reject(error)
  }
)

export default http
```

#### 错误示例
```js
// 错误：直接使用uni.request，没有统一处理
uni.request({
  url: 'https://api.example.com/user',
  method: 'GET',
  success: (res) => {
    if (res.statusCode === 200) {
      // 处理数据
    } else {
      uni.showToast({ title: '请求失败', icon: 'none' })
    }
  },
  fail: () => {
    uni.showToast({ title: '网络异常', icon: 'none' })
  }
})
```

#### 规则依据
统一封装网络请求可以简化请求调用，统一处理认证、错误等逻辑，提高代码的可维护性和一致性。

### 5.3 错误处理与状态管理

#### 规则描述
- 统一处理网络请求错误
- 区分业务错误和网络错误
- 提供友好的错误提示
- 特定错误码的特殊处理（如401未授权）

#### 正确示例
```js
// hooks/request.js 响应拦截器部分
http.interceptors.response.use(
  response => {
    const { statusCode, data } = response
    
    // 处理HTTP状态码
    if (statusCode >= 200 && statusCode < 300) {
      // 处理业务状态码
      if (data.code === 0) {
        return data
      } else if (data.code === 401) {
        // 未授权，清除token并跳转登录页
        uni.removeStorageSync('token')
        uni.navigateTo({ url: '/pages/login/login' })
        return Promise.reject(data)
      } else {
        // 其他业务错误
        uni.showToast({
          title: data.msg || '请求失败',
          icon: 'none'
        })
        return Promise.reject(data)
      }
    }
    
    // HTTP错误
    uni.showToast({
      title: `HTTP错误：${statusCode}`,
      icon: 'none'
    })
    return Promise.reject(response)
  },
  error => {
    // 网络错误或请求被取消
    const errMsg = error.errMsg || '网络异常，请稍后再试'
    uni.showToast({
      title: errMsg,
      icon: 'none'
    })
    return Promise.reject(error)
  }
)
```

#### 错误示例
```js
// 错误：每个请求单独处理错误，逻辑重复
const fetchData = async () => {
  try {
    const res = await uni.http.get('/api/data')
    if (res.code === 0) {
      return res.data
    } else if (res.code === 401) {
      // 重复的未授权处理逻辑
      uni.removeStorageSync('token')
      uni.navigateTo({ url: '/pages/login/login' })
    } else {
      uni.showToast({ title: res.msg, icon: 'none' })
    }
  } catch (err) {
    uni.showToast({ title: '网络异常', icon: 'none' })
  }
}
```

#### 规则依据
统一处理网络请求错误可以减少重复代码，提供一致的用户体验，简化错误处理逻辑。

### 5.4 请求调用规范

#### 规则描述
- 使用async/await处理异步请求
- 使用try/catch捕获异常
- 避免嵌套请求，优先使用Promise.all并行请求
- 合理使用loading状态

#### 正确示例
```js
// 组件中调用API
const loading = ref(false)

const fetchData = async () => {
  loading.value = true
  try {
    const { data } = await uni.http.get(uni.api.getProductList, { params })
    productList.value = data.result
  } catch (error) {
    // 错误已在拦截器中处理，这里可以做额外处理
    console.error('获取商品列表失败', error)
  } finally {
    loading.value = false
  }
}

// 并行请求
const fetchAllData = async () => {
  loading.value = true
  try {
    const [userRes, orderRes] = await Promise.all([
      uni.http.get(uni.api.getUserInfo),
      uni.http.get(uni.api.getOrderList)
    ])
    userInfo.value = userRes.data.result
    orderList.value = orderRes.data.result
  } catch (error) {
    console.error('获取数据失败', error)
  } finally {
    loading.value = false
  }
}
```

#### 错误示例
```js
// 错误：回调地狱，嵌套请求
const fetchData = () => {
  uni.showLoading({ title: '加载中' })
  uni.http.get(uni.api.getUserInfo).then(userRes => {
    userInfo.value = userRes.data.result
    uni.http.get(uni.api.getOrderList).then(orderRes => {
      orderList.value = orderRes.data.result
      uni.hideLoading()
    }).catch(err => {
      uni.hideLoading()
      console.error(err)
    })
  }).catch(err => {
    uni.hideLoading()
    console.error(err)
  })
}
```

#### 规则依据
使用现代JavaScript的异步处理方式可以提高代码的可读性和可维护性，减少回调地狱，简化错误处理。

### 5.5 数据缓存策略

#### 规则描述
- 合理使用数据缓存减少重复请求
- 缓存策略：本地存储 + 过期时间
- 关键数据及时更新，非关键数据可使用缓存
- 提供强制刷新机制

#### 正确示例
```js
// 带缓存的数据请求
const fetchCategoryList = async (forceRefresh = false) => {
  // 缓存key
  const cacheKey = 'category_list'
  // 缓存过期时间（毫秒）
  const expireTime = 30 * 60 * 1000 // 30分钟
  
  // 检查缓存
  if (!forceRefresh) {
    const cacheData = uni.getStorageSync(cacheKey)
    if (cacheData && cacheData.time && (Date.now() - cacheData.time < expireTime)) {
      return cacheData.data
    }
  }
  
  // 无缓存或强制刷新，发起请求
  try {
    const { data } = await uni.http.get(uni.api.getCategoryList)
    // 更新缓存
    uni.setStorageSync(cacheKey, {
      time: Date.now(),
      data: data.result
    })
    return data.result
  } catch (error) {
    console.error('获取分类列表失败', error)
    // 缓存获取失败时，尝试使用过期缓存
    const cacheData = uni.getStorageSync(cacheKey)
    if (cacheData && cacheData.data) {
      return cacheData.data
    }
    return []
  }
}
```

#### 错误示例
```js
// 错误：每次都请求，没有缓存策略
const fetchCategoryList = async () => {
  try {
    const { data } = await uni.http.get(uni.api.getCategoryList)
    return data.result
  } catch (error) {
    console.error('获取分类列表失败', error)
    return []
  }
}
```

#### 规则依据
合理的数据缓存策略可以减少网络请求，提高应用响应速度，降低服务器负载，优化用户体验。

## 6. 路由与页面管理规范

### 6.1 页面路径与命名规范

#### 规则描述
- 页面文件夹使用小写英文字母加连字符命名
- 页面文件使用与文件夹相同的名称
- 合理组织页面层级结构
- 在pages.json中清晰定义页面路径和配置

#### 正确示例
```
// 目录结构
pages/
  |- index/
     |- index.vue       // 首页
  |- user/
     |- user.vue        // 用户中心
     |- profile/
        |- profile.vue  // 个人资料
  |- product/
     |- list/
        |- list.vue     // 商品列表
     |- detail/
        |- detail.vue   // 商品详情
```

```json
// pages.json
{
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页"
      }
    },
    {
      "path": "pages/user/user",
      "style": {
        "navigationBarTitleText": "用户中心"
      }
    },
    {
      "path": "pages/user/profile/profile",
      "style": {
        "navigationBarTitleText": "个人资料"
      }
    }
  ]
}
```

#### 错误示例
```
// 错误的目录结构
pages/
  |- Index.vue          // 错误：首字母大写
  |- UserCenter.vue     // 错误：驼峰命名
  |- product-detail.vue // 错误：没有子目录
```

#### 规则依据
统一的页面命名和组织结构可以提高代码的可维护性，便于团队成员快速定位和理解页面结构。

### 6.2 页面配置规范

#### 规则描述
- 在pages.json中配置页面的标题、导航样式等
- 合理使用全局配置和页面级配置
- tabBar页面应放在pages数组的前面
- 合理使用子包组织相关页面

#### 正确示例
```json
// pages.json
{
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "商城",
    "navigationBarBackgroundColor": "#F8F8F8",
    "backgroundColor": "#F8F8F8"
  },
  "pages": [
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "首页",
        "enablePullDownRefresh": true
      }
    }
  ],
  "subPackages": [
    {
      "root": "pages/product",
      "pages": [
        {
          "path": "list/list",
          "style": {
            "navigationBarTitleText": "商品列表"
          }
        },
        {
          "path": "detail/detail",
          "style": {
            "navigationBarTitleText": "商品详情"
          }
        }
      ]
    }
  ],
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#FF0000",
    "backgroundColor": "#FFFFFF",
    "list": [
      {
        "pagePath": "pages/index/index",
        "text": "首页",
        "iconPath": "static/tabs/home.png",
        "selectedIconPath": "static/tabs/home-active.png"
      },
      {
        "pagePath": "pages/user/user",
        "text": "我的",
        "iconPath": "static/tabs/user.png",
        "selectedIconPath": "static/tabs/user-active.png"
      }
    ]
  }
}
```

#### 错误示例
```json
// 错误：没有使用子包组织相关页面
{
  "pages": [
    {
      "path": "pages/index/index"
    },
    {
      "path": "pages/product/list/list"
    },
    {
      "path": "pages/product/detail/detail"
    },
    {
      "path": "pages/order/list/list"
    },
    {
      "path": "pages/order/detail/detail"
    }
  ]
}
```
#### 规则依据
合理的页面配置和组织方式可以提高应用的加载性能，优化用户体验，提高代码的可维护性。

### 6.3 页面跳转与参数传递

#### 规则描述
- 使用uni.navigateTo、uni.redirectTo等API进行页面跳转
- 使用对象形式传递参数，避免硬编码URL参数
- 大量数据传递使用全局状态管理
- 注意处理参数编码问题

#### 正确示例
```js
// 跳转到商品详情页
const goToProductDetail = (productId) => {
  uni.navigateTo({
    url: '/pages/product/detail/detail',
    success: (res) => {
      // 向打开的页面传递数据
      res.eventChannel.emit('productData', { id: productId })
    }
  })
}

// 在目标页面接收数据
onLoad() {
  const eventChannel = this.getOpenerEventChannel()
  eventChannel.on('productData', (data) => {
    this.productId = data.id
    this.fetchProductDetail()
  })
}
```

#### 错误示例
```js
// 错误：硬编码URL参数
const goToProductDetail = (productId) => {
  uni.navigateTo({
    url: `/pages/product/detail/detail?id=${productId}&from=list&time=${Date.now()}`
  })
}
```

#### 规则依据
规范的页面跳转和参数传递方式可以提高代码的可维护性，避免参数传递过程中的错误和编码问题。

### 6.4 页面生命周期管理

#### 规则描述
- 合理使用页面生命周期函数
- 在onLoad中进行数据初始化和参数获取
- 在onShow中处理页面显示时需要刷新的数据
- 在onUnload中清理资源和事件监听
- 注意页面切换时的数据保留问题

#### 正确示例
```js
// 页面生命周期管理
export default {
  data() {
    return {
      productId: '',
      productInfo: null,
      loading: false
    }
  },
  onLoad(options) {
    // 获取路由参数
    this.productId = options.id
    // 初始化数据
    this.fetchProductDetail()
    // 添加事件监听
    uni.$on('updateProduct', this.handleProductUpdate)
  },
  onShow() {
    // 页面显示时刷新数据，如从购物车返回时需要刷新库存
    this.refreshStockInfo()
  },
  onUnload() {
    // 清理事件监听
    uni.$off('updateProduct', this.handleProductUpdate)
  },
  methods: {
    async fetchProductDetail() {
      this.loading = true
      try {
        const { data } = await uni.http.get(uni.api.getProductDetail, {
          params: { id: this.productId }
        })
        this.productInfo = data.result
      } catch (error) {
        console.error('获取商品详情失败', error)
      } finally {
        this.loading = false
      }
    },
    refreshStockInfo() {
      // 刷新库存信息
    },
    handleProductUpdate(data) {
      // 处理商品更新事件
      if (data.id === this.productId) {
        this.fetchProductDetail()
      }
    }
  }
}
```

#### 错误示例
```js
// 错误：生命周期使用不当
export default {
  data() {
    return {
      productId: '',
      productInfo: null
    }
  },
  created() {
    // 错误：在created中获取路由参数，可能为空
    this.productId = this.$route.query.id
    this.fetchProductDetail()
  },
  // 错误：没有清理事件监听
  mounted() {
    uni.$on('updateProduct', this.handleProductUpdate)
  }
}
```

#### 规则依据
合理使用页面生命周期函数可以避免内存泄漏和数据异常问题，确保页面在不同状态下正常工作。
## 7. 条件编译规范

### 7.1 条件编译基本用法

#### 规则描述
- 使用条件编译处理不同平台的差异化代码
- 遵循uni-app官方条件编译语法
- 合理组织条件编译代码，保持代码可读性
- 避免过多嵌套条件编译块

#### 正确示例
```js
// JavaScript 中条件编译
const getSystemInfo = () => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  console.log('App环境：', systemInfo.platform)
  return systemInfo
  // #endif
  
  // #ifdef MP-WEIXIN
  const systemInfo = wx.getSystemInfoSync()
  console.log('微信小程序环境：', systemInfo.platform)
  return systemInfo
  // #endif
  
  // #ifdef H5
  console.log('H5环境')
  return {}
  // #endif
}
```

```vue
<!-- 模板中条件编译 -->
<template>
  <view class="container">
    <!-- #ifdef APP-PLUS -->
    <view class="app-only">仅App可见的内容</view>
    <!-- #endif -->
    
    <!-- #ifdef MP-WEIXIN -->
    <view class="mp-only">仅微信小程序可见的内容</view>
    <!-- #endif -->
    
    <!-- #ifdef H5 -->
    <view class="h5-only">仅H5可见的内容</view>
    <!-- #endif -->
    
    <!-- 所有平台都显示的内容 -->
    <view class="common">所有平台共用的内容</view>
  </view>
</template>
```

```css
/* 样式中条件编译 */
.container {
  padding: 20rpx;
}

/* #ifdef APP-PLUS */
.app-only {
  color: #FF0000;
}
/* #endif */

/* #ifdef MP-WEIXIN */
.mp-only {
  color: #00FF00;
}
/* #endif */

/* #ifdef H5 */
.h5-only {
  color: #0000FF;
}
/* #endif */
```

#### 错误示例
```js
// 错误：过度嵌套条件编译
const getSystemInfo = () => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync()
  // #ifdef APP-PLUS-IOS
  console.log('iOS环境')
  // #ifdef APP-PLUS-IOS-X
  console.log('iPhoneX')
  // #endif
  // #endif
  // #endif
  
  // 错误：没有处理其他平台
  return systemInfo
}
```

#### 规则依据
合理使用条件编译可以有效处理多平台差异，提高代码的可维护性和可读性，避免运行时判断平台的性能开销。

### 7.2 条件编译组织策略

#### 规则描述
- 优先使用平台特有API而非条件编译
- 抽离平台差异代码到单独的文件
- 使用统一的条件编译标识
- 为不同平台提供统一的接口

#### 正确示例
```js
// 平台适配层 platform/index.js
// #ifdef APP-PLUS
import appAdapter from './app-adapter.js'
export default appAdapter
// #endif

// #ifdef MP-WEIXIN
import wxAdapter from './wx-adapter.js'
export default wxAdapter
// #endif

// #ifdef H5
import h5Adapter from './h5-adapter.js'
export default h5Adapter
// #endif
```

```js
// 各平台适配器实现相同接口
// platform/app-adapter.js
export default {
  login() {
    // App登录实现
  },
  share() {
    // App分享实现
  }
}

// platform/wx-adapter.js
export default {
  login() {
    // 微信小程序登录实现
  },
  share() {
    // 微信小程序分享实现
  }
}
```

```js
// 业务代码中统一调用
import platform from '@/platform/index.js'

// 不需要条件编译，统一接口
const login = () => {
  platform.login()
}
```

#### 错误示例
```js
// 错误：业务代码中大量条件编译
const login = () => {
  // #ifdef APP-PLUS
  // App登录逻辑
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序登录逻辑
  // #endif
  
  // #ifdef H5
  // H5登录逻辑
  // #endif
}

const share = () => {
  // #ifdef APP-PLUS
  // App分享逻辑
  // #endif
  
  // #ifdef MP-WEIXIN
  // 微信小程序分享逻辑
  // #endif
  
  // #ifdef H5
  // H5分享逻辑
  // #endif
}
```

#### 规则依据
良好的条件编译组织策略可以减少代码重复，提高可维护性，使业务逻辑与平台适配分离，便于后续扩展和维护。

### 7.3 条件编译常见场景

#### 规则描述
- 针对不同平台的UI适配
- 平台特有API的调用
- 平台特有功能的实现
- 针对不同平台的性能优化

#### 常见场景示例
```js
// 1. 平台特有API调用
const scanCode = () => {
  // #ifdef APP-PLUS
  uni.scanCode({
    success: (res) => {
      console.log('扫码结果：', res.result)
    }
  })
  // #endif
  
  // #ifdef MP-WEIXIN
  wx.scanCode({
    success: (res) => {
      console.log('扫码结果：', res.result)
    }
  })
  // #endif
  
  // #ifdef H5
  alert('H5环境不支持扫码功能')
  // #endif
}

// 2. 平台特有功能实现
// #ifdef APP-PLUS
// 监听手机返回键
document.addEventListener('plusready', () => {
  plus.key.addEventListener('backbutton', () => {
    console.log('监听到返回键')
  })
})
// #endif

// 3. 性能优化
// #ifdef MP-WEIXIN || MP-ALIPAY
// 小程序环境使用分包加载
// #endif

// #ifdef H5
// H5环境使用懒加载
// #endif
```

#### 规则依据
针对不同场景合理使用条件编译可以提高应用的兼容性和用户体验，同时保持代码的可维护性。

## 8. 性能优化规范

### 8.1 启动性能优化

#### 规则描述
- 减少启动时加载的资源和代码
- 合理使用分包加载
- 优化首屏渲染速度
- 延迟加载非关键资源

#### 正确示例
```js
// 1. 分包加载配置 (pages.json)
{
  "pages": [
    // 主包页面
    {
      "path": "pages/index/index"
    }
  ],
  "subPackages": [
    {
      "root": "pages/product",
      "pages": [
        {
          "path": "list/list"
        },
        {
          "path": "detail/detail"
        }
      ]
    },
    {
      "root": "pages/user",
      "pages": [
        {
          "path": "profile/profile"
        },
        {
          "path": "settings/settings"
        }
      ]
    }
  ]
}

// 2. 延迟加载非关键资源
onLoad() {
  // 首屏关键数据先加载
  this.loadCriticalData()
  
  // 非关键数据延迟加载
  setTimeout(() => {
    this.loadNonCriticalData()
  }, 2000)
}
```

#### 错误示例
```js
// 错误：所有页面放在主包中
{
  "pages": [
    {"path": "pages/index/index"},
    {"path": "pages/product/list/list"},
    {"path": "pages/product/detail/detail"},
    {"path": "pages/user/profile/profile"},
    {"path": "pages/user/settings/settings"},
    {"path": "pages/order/list/list"},
    {"path": "pages/order/detail/detail"}
    // 更多页面...
  ]
}

// 错误：启动时加载所有数据
onLoad() {
  Promise.all([
    this.loadBanners(),
    this.loadProducts(),
    this.loadCategories(),
    this.loadUserInfo(),
    this.loadRecommends(),
    this.loadHistory()
  ]).then(() => {
    this.isLoaded = true
  })
}
```

#### 规则依据
优化启动性能可以提高用户首次打开应用的体验，减少用户等待时间，降低用户流失率。

### 8.2 渲染性能优化

#### 规则描述
- 减少不必要的组件渲染
- 合理使用v-if和v-show
- 避免长列表渲染，使用虚拟列表
- 避免频繁更新视图

#### 正确示例
```vue
<!-- 1. 合理使用v-if和v-show -->
<template>
  <!-- 频繁切换的内容使用v-show -->
  <view v-show="isTabActive" class="tab-content">
    频繁切换的内容
  </view>
  
  <!-- 条件较少变化的内容使用v-if -->
  <view v-if="isLoggedIn" class="user-profile">
    用户信息内容
  </view>
  
  <!-- 2. 长列表使用虚拟列表 -->
  <recycle-list
    :list="longList"
    :item-size="itemSize"
    :buffer-scale="2"
  >
    <template v-slot:item="{ item }">
      <view class="list-item">{{ item.name }}</view>
    </template>
  </recycle-list>
  
  <!-- 3. 使用计算属性过滤数据 -->
  <view v-for="item in filteredList" :key="item.id">
    {{ item.name }}
  </view>
</template>

<script>
export default {
  data() {
    return {
      isTabActive: true,
      isLoggedIn: false,
      longList: Array.from({ length: 1000 }, (_, i) => ({ id: i, name: `Item ${i}` })),
      itemSize: 100,
      list: [],
      filter: ''
    }
  },
  computed: {
    // 使用计算属性避免在模板中进行复杂计算
    filteredList() {
      if (!this.filter) return this.list
      return this.list.filter(item => item.name.includes(this.filter))
    }
  },
  methods: {
    // 4. 避免频繁更新视图
    updateData() {
      // 批量更新数据，避免多次触发视图更新
      const newData = this.processData()
      this.list = newData
    }
  }
}
</script>
```

#### 错误示例
```vue
<!-- 错误：不合理使用v-if和v-show -->
<template>
  <!-- 错误：频繁切换的内容使用v-if -->
  <view v-if="isTabActive" class="tab-content">
    频繁切换的内容
  </view>
  
  <!-- 错误：直接渲染大量数据 -->
  <view v-for="item in longList" :key="item.id" class="list-item">
    {{ item.name }}
  </view>
  
  <!-- 错误：在模板中进行复杂计算 -->
  <view v-for="item in list.filter(i => i.name.includes(filter))" :key="item.id">
    {{ item.name }}
  </view>
</template>

<script>
export default {
  methods: {
    // 错误：频繁更新视图
    updateItems() {
      // 逐个更新数据，触发多次视图更新
      this.list.forEach((item, index) => {
        if (item.needsUpdate) {
          this.list[index] = { ...item, updated: true }
        }
      })
    }
  }
}
</script>
```

#### 规则依据
优化渲染性能可以提高应用的响应速度和流畅度，减少卡顿和闪烁，提升用户体验。

### 8.3 网络性能优化

#### 规则描述
- 减少不必要的网络请求
- 合理使用数据缓存
- 优化请求大小和频率
- 实现请求合并和防抖

#### 正确示例
```js
// 1. 使用缓存减少请求
const fetchCategoryList = async (forceRefresh = false) => {
  const cacheKey = 'category_list'
  const expireTime = 30 * 60 * 1000 // 30分钟
  
  // 检查缓存
  if (!forceRefresh) {
    const cacheData = uni.getStorageSync(cacheKey)
    if (cacheData && cacheData.time && (Date.now() - cacheData.time < expireTime)) {
      return cacheData.data
    }
  }
  
  // 无缓存或强制刷新，发起请求
  const { data } = await uni.http.get(uni.api.getCategoryList)
  // 更新缓存
  uni.setStorageSync(cacheKey, {
    time: Date.now(),
    data: data.result
  })
  return data.result
}

// 2. 请求合并
const fetchAllData = async () => {
  const [userRes, orderRes] = await Promise.all([
    uni.http.get(uni.api.getUserInfo),
    uni.http.get(uni.api.getOrderList)
  ])
  return {
    userInfo: userRes.data.result,
    orderList: orderRes.data.result
  }
}

// 3. 搜索防抖
import { debounce } from '@/utils/debounce'

const searchProducts = debounce(async (keyword) => {
  const { data } = await uni.http.get(uni.api.searchProducts, {
    params: { keyword }
  })
  this.searchResult = data.result
}, 300)
```

#### 错误示例
```js
// 错误：没有缓存策略
const fetchCategoryList = async () => {
  const { data } = await uni.http.get(uni.api.getCategoryList)
  return data.result
}

// 错误：串行请求
const fetchAllData = async () => {
  const userRes = await uni.http.get(uni.api.getUserInfo)
  const orderRes = await uni.http.get(uni.api.getOrderList)
  return {
    userInfo: userRes.data.result,
    orderList: orderRes.data.result
  }
}

// 错误：没有防抖处理
const searchProducts = async (keyword) => {
  const { data } = await uni.http.get(uni.api.searchProducts, {
    params: { keyword }
  })
  this.searchResult = data.result
}

// 输入框绑定
<input type="text" @input="searchProducts" />
```

#### 规则依据
优化网络性能可以减少数据传输量，降低服务器负载，提高应用响应速度，改善用户体验，尤其在网络条件不佳的情况下。

### 8.4 资源优化

#### 规则描述
- 优化图片资源大小和格式
- 合理使用字体图标
- 减少静态资源大小
- 延迟加载非关键资源

#### 正确示例
```vue
<!-- 1. 使用适当尺寸的图片 -->
<template>
  <!-- 根据实际显示尺寸选择图片 -->
  <image 
    src="/static/images/banner_small.jpg" 
    mode="widthFix"
    class="banner-image"
  ></image>
  
  <!-- 2. 使用字体图标代替图片图标 -->
  <text class="iconfont icon-home"></text>
  
  <!-- 3. 延迟加载图片 -->
  <image 
    v-if="isVisible"
    :src="imageUrl" 
    mode="aspectFill"
    class="lazy-image"
  ></image>
</template>

<script>
export default {
  data() {
    return {
      isVisible: false,
      imageUrl: '/static/images/product.jpg'
    }
  },
  onPageScroll(e) {
    // 根据滚动位置判断是否显示图片
    this.checkImageVisibility()
  },
  methods: {
    checkImageVisibility() {
      // 判断元素是否进入可视区域
      // ...
      this.isVisible = true
    }
  }
}
</script>
```

#### 错误示例
```vue
<!-- 错误：使用过大的图片资源 -->
<template>
  <!-- 错误：使用原图显示小尺寸图片 -->
  <image 
    src="/static/images/banner_original_4k.jpg" 
    style="width: 200rpx; height: 100rpx;"
  ></image>
  
  <!-- 错误：使用图片图标 -->
  <image src="/static/icons/home.png" class="icon"></image>
  
  <!-- 错误：不做懒加载处理 -->
  <image 
    v-for="item in 100" 
    :key="item"
    :src="`/static/images/product_${item}.jpg`" 
    class="product-image"
  ></image>
</template>
```

#### 规则依据
优化资源使用可以减少应用体积，加快加载速度，降低内存占用，提高应用整体性能和用户体验。

### 8.5 内存优化

#### 规则描述
- 及时释放不再使用的资源
- 避免内存泄漏
- 合理管理大型数据集
- 优化页面切换时的内存占用

#### 正确示例
```js
// 1. 页面卸载时清理资源
export default {
  data() {
    return {
      timer: null,
      observer: null,
      eventListeners: []
    }
  },
  onLoad() {
    // 设置定时器
    this.timer = setInterval(() => {
      this.updateData()
    }, 1000)
    
    // 创建观察者
    this.observer = new IntersectionObserver(entries => {
      // ...
    })
    
    // 添加事件监听
    const handler = () => { /* ... */ }
    uni.$on('customEvent', handler)
    this.eventListeners.push(['customEvent', handler])
  },
  onUnload() {
    // 清理定时器
    if (this.timer) {
      clearInterval(this.timer)
      this.timer = null
    }
    
    // 清理观察者
    if (this.observer) {
      this.observer.disconnect()
      this.observer = null
    }
    
    // 清理事件监听
    this.eventListeners.forEach(([event, handler]) => {
      uni.$off(event, handler)
    })
    this.eventListeners = []
  }
}

// 2. 分页加载大数据集
const loadDataByPage = async (page = 1, pageSize = 20) => {
  const { data } = await uni.http.get(uni.api.getList, {
    params: { page, pageSize }
  })
  return data.result
}
```

#### 错误示例
```js
// 错误：没有清理资源
export default {
  onLoad() {
    // 错误：没有保存定时器引用
    setInterval(() => {
      this.updateData()
    }, 1000)
    
    // 错误：没有在页面卸载时移除事件监听
    uni.$on('customEvent', this.handleEvent)
  },
  // 没有实现onUnload方法清理资源
  
  // 错误：一次性加载大量数据
  async loadAllData() {
    const { data } = await uni.http.get(uni.api.getList, {
      params: { limit: 1000 }
    })
    this.list = data.result
  }
}
```

#### 规则依据
优化内存使用可以避免应用崩溃，减少卡顿，提高应用稳定性和响应速度，尤其在低端设备上效果明显。

