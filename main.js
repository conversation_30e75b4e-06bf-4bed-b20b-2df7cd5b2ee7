import App from './App'
import './hooks/config.js'
import './hooks/api.js'
import './hooks/request.js'
// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false
App.mpType = 'app'

const app = new Vue({
	...App
})
app.$mount()
// #endif

// #ifdef VUE3
import {
	createSSRApp
} from 'vue'
import pinia from '@/store/store.js';
export function createApp() {
	const app = createSSRApp(App)
	app.use(pinia);
	return {
		app,
		pinia
	}
}
// #endif